"""
Nonlocal EM43 Proof-of-Concept

This addresses the efficiency limitation where radius-1 interactions make it hard
for information to "pass through" non-zero cells. We introduce nonlocal rules
that allow cells to interact with neighbors at distance 2 or more.

Key innovations:
1. Extended neighborhood: cells can read state 2 positions away
2. Skip-through rules: information can bypass blocking cells
3. Hybrid local/nonlocal rule system to keep combinatorial space manageable
"""

import numpy as np
import time
import pickle
from tqdm import tqdm
from typing import Tuple, List
from em43_numba import _sanitize_programme

# Nonlocal rule structure:
# - Standard rule: 64 entries for (left, center, right) combinations
# - Skip-left rule: 64 entries for (left-2, center, right) combinations  
# - Skip-right rule: 64 entries for (left, center, right+2) combinations
# - Long-range rule: 64 entries for (left-2, center, right+2) combinations

def _sanitize_nonlocal_rule(rule):
    """Sanitize nonlocal rule array (4 x 64 = 256 entries total)."""
    rule = rule.copy()
    rule = rule.reshape(4, 64)
    
    # Each sub-rule follows standard EM43 constraints
    for i in range(4):
        rule[i] = rule[i] & 0x03  # Ensure values are 0-3
        
        # Apply immutable constraints for each sub-rule
        # These are the standard EM43 immutable patterns
        immutable_patterns = [
            ((0<<4)|(0<<2)|0, 0),      # 000 -> 0
            ((0<<4)|(2<<2)|0, 2),      # 020 -> 2 (R preservation)
            ((0<<4)|(0<<2)|2, 0),      # 002 -> 0
            ((2<<4)|(0<<2)|0, 0),      # 200 -> 0
            ((0<<4)|(3<<2)|3, 3),      # 033 -> 3 (B preservation)
            ((3<<4)|(3<<2)|0, 3),      # 330 -> 3
            ((0<<4)|(0<<2)|3, 0),      # 003 -> 0
            ((3<<4)|(0<<2)|0, 0),      # 300 -> 0
        ]
        
        for pattern, value in immutable_patterns:
            rule[i][pattern] = value
    
    return rule.flatten()

def simulate_addition_nonlocal(rule, prog, a, b, window=500, max_steps=1000, halt_thresh=0.5):
    """
    Addition simulation with nonlocal rules.
    
    Rule structure:
    - rule[0:64]: standard local rule (left, center, right)
    - rule[64:128]: skip-left rule (left-2, center, right)
    - rule[128:192]: skip-right rule (left, center, right+2)
    - rule[192:256]: long-range rule (left-2, center, right+2)
    """
    L = len(prog)
    N = window
    
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(L):
        state[j] = prog[j]
    state[L] = 3      # B
    state[L + 1] = 3  # B
    
    # Write inputs: 0^(a+1) R 0^(b+1) R
    r1_pos = L + 2 + a + 1
    if r1_pos >= N:
        return -10
    state[r1_pos] = 2
    
    r2_pos = r1_pos + 1 + b + 1
    if r2_pos >= N:
        return -10
    state[r2_pos] = 2
    
    # Adaptive simulation range
    sim_range = min(N - 1, r2_pos + max(a + b + 15, 40))
    
    # Reshape rule for easier access
    rule_local = rule[0:64]
    rule_skip_left = rule[64:128]
    rule_skip_right = rule[128:192]
    rule_long_range = rule[192:256]
    
    # Run CA simulation with nonlocal rules
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(2, sim_range - 2):  # Need buffer for nonlocal access
            # Get local neighborhood
            left, center, right = state[x-1], state[x], state[x+1]
            local_idx = (left << 4) | (center << 2) | right
            
            # Get nonlocal neighborhoods
            left2, right2 = state[x-2], state[x+2]
            skip_left_idx = (left2 << 4) | (center << 2) | right
            skip_right_idx = (left << 4) | (center << 2) | right2
            long_range_idx = (left2 << 4) | (center << 2) | right2
            
            # Apply rule selection logic
            # Priority: use nonlocal rules when local neighborhood has blocking patterns
            
            # Check if center cell is non-zero (potential blocker)
            if center != 0:
                # Use long-range rule to allow information to pass through
                nxt[x] = rule_long_range[long_range_idx]
            elif left != 0 and right == 0:
                # Left neighbor is blocking, try skip-left rule
                nxt[x] = rule_skip_left[skip_left_idx]
            elif right != 0 and left == 0:
                # Right neighbor is blocking, try skip-right rule
                nxt[x] = rule_skip_right[skip_right_idx]
            else:
                # Use standard local rule
                nxt[x] = rule_local[local_idx]
        
        # Handle boundary cells with standard local rules
        for x in [1, sim_range-1]:
            if x < sim_range:
                left = state[x-1] if x > 0 else 0
                center = state[x]
                right = state[x+1] if x < N-1 else 0
                local_idx = (left << 4) | (center << 2) | right
                nxt[x] = rule_local[local_idx]
        
        # Halting check
        live = blue = 0
        for x in range(sim_range):
            v = nxt[x]
            if v != 0:
                live += 1
                if v == 3:
                    blue += 1
        
        if live > 0 and blue / live >= halt_thresh:
            # Find rightmost R
            rpos = -1
            for x in range(sim_range - 1, -1, -1):
                if nxt[x] == 2:
                    rpos = x
                    break
            
            if rpos != -1:
                return rpos - (L + 3)
            else:
                return -10
        
        state = nxt
    
    return -10

def create_random_nonlocal_rule():
    """Create a random nonlocal rule with proper constraints."""
    rule = np.random.randint(0, 4, size=256, dtype=np.uint8)
    return _sanitize_nonlocal_rule(rule)

def evaluate_nonlocal_model(rule, prog, test_cases):
    """Evaluate nonlocal model on test cases."""
    results = []
    
    for a, b in test_cases:
        expected = a + b
        result = simulate_addition_nonlocal(rule, prog, a, b)
        
        status = "✓" if result == expected else "✗"
        convergent = result != -10
        
        results.append({
            'a': a, 'b': b, 'expected': expected, 'result': result,
            'correct': result == expected, 'convergent': convergent, 'status': status
        })
    
    return results

def train_nonlocal_addition_poc():
    """Train a proof-of-concept nonlocal addition model."""
    print("🚀 NONLOCAL EM43 PROOF-OF-CONCEPT")
    print("=" * 60)
    print("Testing nonlocal rules to address information propagation limitations")
    
    # Start with simple test cases
    test_cases = [(1, 1), (1, 2), (2, 1), (2, 2), (1, 3), (3, 1), (2, 3), (3, 2)]
    print(f"Test cases: {test_cases}")
    
    # Training parameters - smaller for proof-of-concept
    POP_SIZE = 50
    GENERATIONS = 100
    MUTATION_RATE = 0.05
    
    print(f"Population: {POP_SIZE}, Generations: {GENERATIONS}")
    
    # Initialize population
    population = []
    rng = np.random.default_rng()
    
    for i in range(POP_SIZE):
        rule = create_random_nonlocal_rule()
        prog = rng.choice([0, 1, 2], size=16, p=[0.7, 0.2, 0.1])
        prog = _sanitize_programme(prog)
        population.append((rule, prog))
    
    best_accuracy = 0
    best_model = None
    
    print("Training nonlocal model...")
    
    for gen in tqdm(range(GENERATIONS), desc="Nonlocal Training"):
        # Evaluate population
        fitness_scores = []
        
        for rule, prog in population:
            results = evaluate_nonlocal_model(rule, prog, test_cases)
            
            correct = sum(1 for r in results if r['correct'])
            convergent = sum(1 for r in results if r['convergent'])
            
            accuracy = correct / len(test_cases)
            convergence_rate = convergent / len(test_cases)
            
            # Fitness function
            fitness = correct * 100 + convergent * 10
            
            # Bonus for perfect accuracy
            if accuracy == 1.0:
                fitness += 500
            
            fitness_scores.append(fitness)
        
        # Track best
        gen_best_idx = np.argmax(fitness_scores)
        gen_results = evaluate_nonlocal_model(population[gen_best_idx][0], 
                                            population[gen_best_idx][1], test_cases)
        gen_accuracy = sum(1 for r in gen_results if r['correct']) / len(test_cases)
        
        if gen_accuracy > best_accuracy:
            best_accuracy = gen_accuracy
            best_model = population[gen_best_idx]
        
        # Progress report
        if gen % 20 == 0:
            gen_convergence = sum(1 for r in gen_results if r['convergent']) / len(test_cases)
            print(f"  Gen {gen}: accuracy={gen_accuracy:.2%}, convergence={gen_convergence:.2%}")
            
            if gen_accuracy < 1.0:
                failing = [r for r in gen_results if not r['correct']]
                print(f"    Failing: {[(r['a'], r['b']) for r in failing]}")
        
        # Early stopping
        if best_accuracy >= 1.0:
            print(f"  🎯 Perfect accuracy reached at generation {gen}!")
            break
        
        # Selection and reproduction
        if gen < GENERATIONS - 1:
            # Elite selection
            sorted_indices = np.argsort(fitness_scores)[::-1]
            n_elite = POP_SIZE // 4
            
            new_population = []
            
            # Keep elites
            for i in range(n_elite):
                new_population.append(population[sorted_indices[i]])
            
            # Generate offspring
            while len(new_population) < POP_SIZE:
                # Tournament selection
                tournament_indices = rng.choice(sorted_indices[:POP_SIZE//2], size=3, replace=False)
                parent_idx = tournament_indices[0]
                
                rule, prog = population[parent_idx]
                
                # Mutate
                new_rule = rule.copy()
                new_prog = prog.copy()
                
                # Mutate rule
                mask = rng.random(256) < MUTATION_RATE
                new_rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)
                new_rule = _sanitize_nonlocal_rule(new_rule)
                
                # Mutate program
                mask = rng.random(len(prog)) < MUTATION_RATE * 2
                new_prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.7, 0.2, 0.1])
                new_prog = _sanitize_programme(new_prog)
                
                new_population.append((new_rule, new_prog))
            
            population = new_population
    
    return best_model, best_accuracy, test_cases

if __name__ == "__main__":
    start_time = time.time()
    
    print("🧪 NONLOCAL EM43 PROOF-OF-CONCEPT")
    print("Addressing information propagation limitations")
    print("=" * 60)
    
    # Train nonlocal model
    best_model, final_accuracy, test_cases = train_nonlocal_addition_poc()
    
    print(f"\n🎯 NONLOCAL TRAINING COMPLETE!")
    print(f"Final accuracy: {final_accuracy:.2%}")
    
    if best_model:
        rule, prog = best_model
        
        # Detailed analysis
        print("\n📊 DETAILED ANALYSIS")
        results = evaluate_nonlocal_model(rule, prog, test_cases)
        
        correct = sum(1 for r in results if r['correct'])
        convergent = sum(1 for r in results if r['convergent'])
        
        print(f"Accuracy: {correct}/{len(test_cases)} ({correct/len(test_cases)*100:.1f}%)")
        print(f"Convergence: {convergent}/{len(test_cases)} ({convergent/len(test_cases)*100:.1f}%)")
        
        print(f"\nResults:")
        for r in results:
            print(f"  {r['a']}+{r['b']}={r['expected']} → {r['result']} {r['status']}")
        
        # Save model
        nonlocal_model = {
            'rule': rule,
            'prog': prog,
            'accuracy': final_accuracy,
            'type': 'nonlocal_poc',
            'test_cases': test_cases
        }
        
        with open('nonlocal_em43_poc.pkl', 'wb') as f:
            pickle.dump(nonlocal_model, f)
        
        print(f"\n💾 Nonlocal model saved: nonlocal_em43_poc.pkl")
    
    end_time = time.time()
    print(f"\n⏱️  Training time: {(end_time - start_time)/60:.1f} minutes")
    
    if final_accuracy >= 1.0:
        print("\n🎉 SUCCESS: Nonlocal rules achieved perfect accuracy!")
        print("🔬 This demonstrates that nonlocal interactions can solve propagation issues")
    elif final_accuracy >= 0.75:
        print("\n✅ PROMISING: Nonlocal rules show significant improvement potential")
    else:
        print("\n⚠️  LEARNING: More work needed, but concept is demonstrated")
