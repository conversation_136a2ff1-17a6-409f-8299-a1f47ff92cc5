"""
Nonlocal EM43 Visualizer

Creates HTML visualization to show how nonlocal rules work compared to standard EM43.
This helps understand how information can "skip through" blocking cells.
"""

import numpy as np
import pickle
from nonlocal_em43_poc import simulate_addition_nonlocal, _sanitize_nonlocal_rule, _sanitize_programme
from em43_numba import _sanitize_rule

def simulate_addition_standard(rule, prog, a, b, window=500, max_steps=1000, halt_thresh=0.5):
    """Standard EM43 simulation for comparison."""
    L = len(prog)
    N = window
    
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(L):
        state[j] = prog[j]
    state[L] = 3      # B
    state[L + 1] = 3  # B
    
    # Write inputs
    r1_pos = L + 2 + a + 1
    if r1_pos >= N:
        return [], -10
    state[r1_pos] = 2
    
    r2_pos = r1_pos + 1 + b + 1
    if r2_pos >= N:
        return [], -10
    state[r2_pos] = 2
    
    sim_range = min(N - 1, r2_pos + max(a + b + 15, 40))
    
    # Store evolution for visualization
    evolution = [state[:sim_range].copy()]
    
    # Run CA simulation
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(1, sim_range):
            idx = (state[x-1] << 4) | (state[x] << 2) | state[x+1]
            nxt[x] = rule[idx]
        
        evolution.append(nxt[:sim_range].copy())
        
        # Halting check
        live = blue = 0
        for x in range(sim_range):
            v = nxt[x]
            if v != 0:
                live += 1
                if v == 3:
                    blue += 1
        
        if live > 0 and blue / live >= halt_thresh:
            # Find rightmost R
            rpos = -1
            for x in range(sim_range - 1, -1, -1):
                if nxt[x] == 2:
                    rpos = x
                    break
            
            if rpos != -1:
                return evolution, rpos - (L + 3)
            else:
                return evolution, -10
        
        state = nxt
    
    return evolution, -10

def simulate_addition_nonlocal_with_trace(rule, prog, a, b, window=500, max_steps=1000, halt_thresh=0.5):
    """Nonlocal simulation with detailed tracing."""
    L = len(prog)
    N = window
    
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(L):
        state[j] = prog[j]
    state[L] = 3      # B
    state[L + 1] = 3  # B
    
    # Write inputs
    r1_pos = L + 2 + a + 1
    if r1_pos >= N:
        return [], [], -10
    state[r1_pos] = 2
    
    r2_pos = r1_pos + 1 + b + 1
    if r2_pos >= N:
        return [], [], -10
    state[r2_pos] = 2
    
    sim_range = min(N - 1, r2_pos + max(a + b + 15, 40))
    
    # Reshape rule for easier access
    rule_local = rule[0:64]
    rule_skip_left = rule[64:128]
    rule_skip_right = rule[128:192]
    rule_long_range = rule[192:256]
    
    # Store evolution and rule usage for visualization
    evolution = [state[:sim_range].copy()]
    rule_usage = []  # Track which rule was used for each cell
    
    # Run CA simulation with nonlocal rules
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        step_rule_usage = np.zeros(sim_range, dtype=np.uint8)  # 0=local, 1=skip_left, 2=skip_right, 3=long_range
        
        for x in range(2, sim_range - 2):
            # Get neighborhoods
            left, center, right = state[x-1], state[x], state[x+1]
            left2, right2 = state[x-2], state[x+2]
            
            local_idx = (left << 4) | (center << 2) | right
            skip_left_idx = (left2 << 4) | (center << 2) | right
            skip_right_idx = (left << 4) | (center << 2) | right2
            long_range_idx = (left2 << 4) | (center << 2) | right2
            
            # Apply rule selection logic
            if center != 0:
                # Use long-range rule to allow information to pass through
                nxt[x] = rule_long_range[long_range_idx]
                step_rule_usage[x] = 3
            elif left != 0 and right == 0:
                # Left neighbor is blocking, try skip-left rule
                nxt[x] = rule_skip_left[skip_left_idx]
                step_rule_usage[x] = 1
            elif right != 0 and left == 0:
                # Right neighbor is blocking, try skip-right rule
                nxt[x] = rule_skip_right[skip_right_idx]
                step_rule_usage[x] = 2
            else:
                # Use standard local rule
                nxt[x] = rule_local[local_idx]
                step_rule_usage[x] = 0
        
        # Handle boundary cells with standard local rules
        for x in [1, sim_range-1]:
            if x < sim_range:
                left = state[x-1] if x > 0 else 0
                center = state[x]
                right = state[x+1] if x < N-1 else 0
                local_idx = (left << 4) | (center << 2) | right
                nxt[x] = rule_local[local_idx]
                step_rule_usage[x] = 0
        
        evolution.append(nxt[:sim_range].copy())
        rule_usage.append(step_rule_usage.copy())
        
        # Halting check
        live = blue = 0
        for x in range(sim_range):
            v = nxt[x]
            if v != 0:
                live += 1
                if v == 3:
                    blue += 1
        
        if live > 0 and blue / live >= halt_thresh:
            # Find rightmost R
            rpos = -1
            for x in range(sim_range - 1, -1, -1):
                if nxt[x] == 2:
                    rpos = x
                    break
            
            if rpos != -1:
                return evolution, rule_usage, rpos - (L + 3)
            else:
                return evolution, rule_usage, -10
        
        state = nxt
    
    return evolution, rule_usage, -10

def create_comparison_html(a, b, standard_evolution, standard_result, 
                          nonlocal_evolution, nonlocal_rule_usage, nonlocal_result):
    """Create HTML visualization comparing standard vs nonlocal EM43."""
    
    html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>EM43 Nonlocal Rules Comparison: {a} + {b}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .container {{ display: flex; gap: 20px; }}
        .column {{ flex: 1; }}
        .grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(15px, 1fr)); gap: 1px; margin: 5px 0; }}
        .cell {{ width: 15px; height: 15px; border: 1px solid #ccc; text-align: center; font-size: 10px; }}
        .cell-0 {{ background-color: #ffffff; }}
        .cell-1 {{ background-color: #000000; color: white; }}
        .cell-2 {{ background-color: #ff2222; color: white; }}
        .cell-3 {{ background-color: #1665c1; color: white; }}
        .rule-local {{ border: 2px solid #00ff00; }}
        .rule-skip-left {{ border: 2px solid #ff8800; }}
        .rule-skip-right {{ border: 2px solid #8800ff; }}
        .rule-long-range {{ border: 2px solid #ff0088; }}
        .step {{ margin: 10px 0; }}
        .legend {{ margin: 20px 0; }}
        .legend-item {{ display: inline-block; margin: 5px 10px; }}
        .legend-cell {{ display: inline-block; width: 15px; height: 15px; border: 1px solid #000; margin-right: 5px; }}
        h2 {{ color: #333; }}
        .result {{ font-weight: bold; font-size: 18px; margin: 10px 0; }}
        .success {{ color: green; }}
        .failure {{ color: red; }}
    </style>
</head>
<body>
    <h1>EM43 Nonlocal Rules Comparison: {a} + {b} = {a + b}</h1>
    
    <div class="legend">
        <h3>Cell Types:</h3>
        <div class="legend-item"><div class="legend-cell cell-0"></div>Blank (0)</div>
        <div class="legend-item"><div class="legend-cell cell-1"></div>Program (1)</div>
        <div class="legend-item"><div class="legend-cell cell-2"></div>Red marker (2)</div>
        <div class="legend-item"><div class="legend-cell cell-3"></div>Blue marker (3)</div>
    </div>
    
    <div class="legend">
        <h3>Nonlocal Rule Types:</h3>
        <div class="legend-item"><div class="legend-cell rule-local"></div>Local (radius 1)</div>
        <div class="legend-item"><div class="legend-cell rule-skip-left"></div>Skip Left (left-2, center, right)</div>
        <div class="legend-item"><div class="legend-cell rule-skip-right"></div>Skip Right (left, center, right+2)</div>
        <div class="legend-item"><div class="legend-cell rule-long-range"></div>Long Range (left-2, center, right+2)</div>
    </div>
    
    <div class="container">
        <div class="column">
            <h2>Standard EM43 (Radius 1)</h2>
            <div class="result {'success' if standard_result == a + b else 'failure'}">
                Result: {standard_result} {'✓' if standard_result == a + b else '✗'}
            </div>
"""
    
    # Add standard evolution
    for i, row in enumerate(standard_evolution[:20]):  # Limit to first 20 steps
        html += f'            <div class="step">Step {i}:<div class="grid">'
        for cell in row:
            html += f'<div class="cell cell-{cell}">{cell if cell != 0 else ""}</div>'
        html += '</div></div>\n'
    
    html += """
        </div>
        
        <div class="column">
            <h2>Nonlocal EM43</h2>
            <div class="result """ + ('success' if nonlocal_result == a + b else 'failure') + f"""">
                Result: {nonlocal_result} {'✓' if nonlocal_result == a + b else '✗'}
            </div>
"""
    
    # Add nonlocal evolution with rule usage highlighting
    for i, (row, rule_row) in enumerate(zip(nonlocal_evolution[:20], nonlocal_rule_usage[:19] + [np.zeros(len(nonlocal_evolution[0]))])):
        html += f'            <div class="step">Step {i}:<div class="grid">'
        for j, cell in enumerate(row):
            rule_class = ""
            if i > 0 and j < len(rule_row):
                rule_types = ["rule-local", "rule-skip-left", "rule-skip-right", "rule-long-range"]
                rule_class = rule_types[rule_row[j]]
            html += f'<div class="cell cell-{cell} {rule_class}">{cell if cell != 0 else ""}</div>'
        html += '</div></div>\n'
    
    html += """
        </div>
    </div>
    
    <div style="margin-top: 30px;">
        <h3>Analysis:</h3>
        <p><strong>Standard EM43:</strong> Uses only radius-1 interactions. Information must propagate step-by-step through neighboring cells.</p>
        <p><strong>Nonlocal EM43:</strong> Can skip over blocking cells using extended neighborhoods. Colored borders show which rule type was used:</p>
        <ul>
            <li><span style="color: #00ff00;">Green border:</span> Standard local rule (left, center, right)</li>
            <li><span style="color: #ff8800;">Orange border:</span> Skip-left rule (left-2, center, right) - bypasses left neighbor</li>
            <li><span style="color: #8800ff;">Purple border:</span> Skip-right rule (left, center, right+2) - bypasses right neighbor</li>
            <li><span style="color: #ff0088;">Pink border:</span> Long-range rule (left-2, center, right+2) - bypasses both neighbors</li>
        </ul>
    </div>
</body>
</html>
"""
    
    return html

def create_comparison_visualization(a=2, b=3):
    """Create a comparison visualization for a specific addition problem."""
    print(f"Creating comparison visualization for {a} + {b}")
    
    # Create simple test rules
    standard_rule = np.random.randint(0, 4, size=64, dtype=np.uint8)
    standard_rule = _sanitize_rule(standard_rule)
    
    nonlocal_rule = np.random.randint(0, 4, size=256, dtype=np.uint8)
    nonlocal_rule = _sanitize_nonlocal_rule(nonlocal_rule)
    
    # Create simple program
    prog = np.array([1, 0, 1, 0, 0, 0, 0, 0], dtype=np.uint8)
    prog = _sanitize_programme(prog)
    
    # Run simulations
    print("Running standard EM43 simulation...")
    standard_evolution, standard_result = simulate_addition_standard(standard_rule, prog, a, b, max_steps=50)
    
    print("Running nonlocal EM43 simulation...")
    nonlocal_evolution, nonlocal_rule_usage, nonlocal_result = simulate_addition_nonlocal_with_trace(
        nonlocal_rule, prog, a, b, max_steps=50)
    
    # Create HTML
    html = create_comparison_html(a, b, standard_evolution, standard_result,
                                nonlocal_evolution, nonlocal_rule_usage, nonlocal_result)
    
    # Save HTML file
    filename = f"nonlocal_comparison_{a}_{b}.html"
    with open(filename, 'w') as f:
        f.write(html)
    
    print(f"Visualization saved as {filename}")
    print(f"Standard result: {standard_result}")
    print(f"Nonlocal result: {nonlocal_result}")
    
    return filename

if __name__ == "__main__":
    print("🎨 NONLOCAL EM43 VISUALIZER")
    print("=" * 40)
    
    # Create comparison for a few different cases
    test_cases = [(1, 2), (2, 3), (3, 2)]
    
    for a, b in test_cases:
        filename = create_comparison_visualization(a, b)
        print(f"Created: {filename}")
        print()
    
    print("🎉 Visualizations created!")
    print("Open the HTML files in a browser to see the comparison between standard and nonlocal EM43.")
