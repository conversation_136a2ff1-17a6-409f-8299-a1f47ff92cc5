"""
Focused Nonlocal Training

Let's focus on getting just 1+1=2 working first, then expand.
Use a hybrid approach: start with a working standard EM43 base and add nonlocal capabilities.
"""

import numpy as np
import time
from tqdm import tqdm
from em43_numba import _sanitize_programme, _sanitize_rule

def create_hybrid_nonlocal_rule(base_rule):
    """Create nonlocal rule starting from a working standard rule."""
    # Expand standard 64-entry rule to 256-entry nonlocal rule
    nonlocal_rule = np.zeros(256, dtype=np.uint8)
    
    # Copy base rule to all 4 rule types as starting point
    for i in range(4):
        nonlocal_rule[i*64:(i+1)*64] = base_rule.copy()
    
    return nonlocal_rule

def simulate_hybrid_addition(rule, prog, a, b, max_steps=100):
    """Hybrid simulation: use standard EM43 with optional nonlocal fallback."""
    L = len(prog)
    N = 30
    
    state = np.zeros(N, dtype=np.uint8)
    
    # Standard EM43 encoding: [prog] BB 0^a R 0^b R
    for j in range(min(L, N)):
        state[j] = prog[j]
    
    if L + 1 < N:
        state[L] = 3      # B
    if L + 2 < N:
        state[L + 1] = 3  # B
    
    base_pos = L + 2
    
    # Write first input
    for i in range(a):
        pos = base_pos + i
        if pos < N:
            state[pos] = 0
    
    # First R marker
    r1_pos = base_pos + a
    if r1_pos < N:
        state[r1_pos] = 2
    
    # Write second input
    for i in range(b):
        pos = r1_pos + 1 + i
        if pos < N:
            state[pos] = 0
    
    # Second R marker
    r2_pos = r1_pos + 1 + b
    if r2_pos < N:
        state[r2_pos] = 2
    
    # Extract rule components
    rule_local = rule[0:64]
    rule_skip_left = rule[64:128]
    rule_skip_right = rule[128:192]
    rule_long_range = rule[192:256]
    
    # Run simulation
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        
        # Apply rules - mostly standard with selective nonlocal
        for x in range(1, N - 1):
            left = state[x-1] if x > 0 else 0
            center = state[x]
            right = state[x+1] if x < N-1 else 0
            local_idx = (left << 4) | (center << 2) | right
            
            # Default: use standard local rule (like original EM43)
            nxt[x] = rule_local[local_idx]
            
            # Only use nonlocal rules in specific situations
            if x >= 2 and x < N - 2:
                left2 = state[x-2]
                right2 = state[x+2]
                
                # Use nonlocal rules sparingly - only when really needed
                if center == 1 and (left == 2 or right == 2):
                    # Program cell next to R marker - might benefit from long-range
                    long_range_idx = (left2 << 4) | (center << 2) | right2
                    nxt[x] = rule_long_range[long_range_idx]
                elif center == 0 and left == 3 and right == 0:
                    # Zero between blue and blank - try skip-right
                    skip_right_idx = (left << 4) | (center << 2) | right2
                    nxt[x] = rule_skip_right[skip_right_idx]
        
        # Standard EM43 halting condition
        live = blue = 0
        for x in range(N):
            v = nxt[x]
            if v != 0:
                live += 1
                if v == 3:
                    blue += 1
        
        if live > 0 and blue / live >= 0.5:
            # Find rightmost R (standard EM43 decoding)
            rpos = -1
            for x in range(N - 1, -1, -1):
                if nxt[x] == 2:
                    rpos = x
                    break
            
            if rpos != -1:
                # Count zeros after rightmost R
                zero_count = 0
                for x in range(rpos + 1, N):
                    if nxt[x] == 0:
                        zero_count += 1
                    elif nxt[x] != 0:
                        break
                return zero_count
            else:
                return -10
        
        state = nxt
    
    return -10

def evaluate_hybrid(rule, prog, test_cases):
    """Evaluate hybrid model."""
    results = []
    for a, b in test_cases:
        expected = a + b
        result = simulate_hybrid_addition(rule, prog, a, b)
        correct = (result == expected)
        convergent = (result != -10)
        results.append({
            'a': a, 'b': b, 'expected': expected, 'result': result,
            'correct': correct, 'convergent': convergent
        })
    return results

def focused_training():
    """Focused training starting with working base."""
    print("🎯 FOCUSED NONLOCAL TRAINING")
    print("=" * 50)
    print("Strategy: Start with working standard EM43, add nonlocal capabilities")
    
    # Start with just 1+1=2
    test_cases = [(1, 1)]
    print(f"Test cases: {test_cases}")
    
    # Training parameters
    POP_SIZE = 30
    GENERATIONS = 100
    MUTATION_RATE = 0.05
    
    print(f"Population: {POP_SIZE}, Generations: {GENERATIONS}")
    
    # Initialize population with working base
    population = []
    rng = np.random.default_rng(42)
    
    for i in range(POP_SIZE):
        # Start with a reasonable standard rule
        base_rule = np.zeros(64, dtype=np.uint8)
        
        # Set up basic working patterns
        base_rule[0] = 0   # 000 -> 0
        base_rule[2] = 2   # 002 -> 2 (preserve R)
        base_rule[8] = 2   # 020 -> 2 (preserve R)
        base_rule[32] = 0  # 200 -> 0
        base_rule[12] = 3  # 030 -> 3 (generate blue)
        base_rule[48] = 3  # 300 -> 3 (preserve blue)
        base_rule[4] = 1   # 010 -> 1 (propagate program)
        base_rule[1] = 0   # 001 -> 0
        base_rule[16] = 0  # 100 -> 0
        
        # Add some variation
        for j in range(64):
            if rng.random() < 0.1:  # 10% variation
                base_rule[j] = rng.integers(0, 4)
        
        base_rule = _sanitize_rule(base_rule)
        
        # Create nonlocal version
        rule = create_hybrid_nonlocal_rule(base_rule)
        
        # Simple program
        prog = rng.choice([0, 1], size=6, p=[0.7, 0.3])
        prog = _sanitize_programme(prog)
        
        population.append((rule, prog))
    
    best_accuracy = 0
    best_model = None
    
    print("\nTraining...")
    
    for gen in range(GENERATIONS):
        fitness_scores = []
        
        for rule, prog in population:
            results = evaluate_hybrid(rule, prog, test_cases)
            
            correct = sum(1 for r in results if r['correct'])
            convergent = sum(1 for r in results if r['convergent'])
            
            # Fitness: heavily reward correct answers
            fitness = correct * 1000 + convergent * 100
            
            # Bonus for getting close to the right answer
            for r in results:
                if r['convergent'] and r['result'] != -10:
                    error = abs(r['result'] - r['expected'])
                    if error <= 1:
                        fitness += 200  # Very close
                    elif error <= 2:
                        fitness += 100  # Close
                    elif error <= 5:
                        fitness += 50   # Somewhat close
            
            fitness_scores.append(fitness)
        
        # Track best
        best_idx = np.argmax(fitness_scores)
        gen_results = evaluate_hybrid(population[best_idx][0], population[best_idx][1], test_cases)
        gen_accuracy = sum(1 for r in gen_results if r['correct']) / len(test_cases)
        gen_convergence = sum(1 for r in gen_results if r['convergent']) / len(test_cases)
        
        if gen_accuracy > best_accuracy:
            best_accuracy = gen_accuracy
            best_model = population[best_idx]
        
        # Progress report
        if gen % 20 == 0 or gen_accuracy > 0:
            print(f"Gen {gen:2d}: acc={gen_accuracy:.1%}, conv={gen_convergence:.1%}, best={best_accuracy:.1%}")
            
            if gen_accuracy > 0 or gen % 20 == 0:
                print("  Current results:")
                for r in gen_results:
                    status = "✓" if r['correct'] else "✗"
                    conv = "C" if r['convergent'] else "NC"
                    print(f"    {r['a']}+{r['b']}={r['expected']} → {r['result']} {status} {conv}")
        
        # Early stopping
        if best_accuracy >= 1.0:
            print(f"🎯 Perfect accuracy reached at generation {gen}!")
            break
        
        # Reproduction
        if gen < GENERATIONS - 1:
            sorted_indices = np.argsort(fitness_scores)[::-1]
            n_keep = POP_SIZE // 3
            
            new_population = []
            
            # Keep best
            for i in range(n_keep):
                new_population.append(population[sorted_indices[i]])
            
            # Generate offspring
            while len(new_population) < POP_SIZE:
                parent_idx = rng.choice(sorted_indices[:n_keep])
                rule, prog = population[parent_idx]
                
                # Mutate
                new_rule = rule.copy()
                new_prog = prog.copy()
                
                # Mutate rule (focus on nonlocal parts)
                for i in range(256):
                    if rng.random() < MUTATION_RATE:
                        new_rule[i] = rng.integers(0, 4)
                
                # Re-sanitize the base rule part
                base_part = new_rule[0:64]
                base_part = _sanitize_rule(base_part)
                new_rule[0:64] = base_part
                
                # Mutate program
                for i in range(len(prog)):
                    if rng.random() < MUTATION_RATE * 2:
                        new_prog[i] = rng.choice([0, 1], p=[0.7, 0.3])
                
                new_prog = _sanitize_programme(new_prog)
                
                new_population.append((new_rule, new_prog))
            
            population = new_population
    
    return best_model, best_accuracy, test_cases

if __name__ == "__main__":
    start_time = time.time()
    
    print("🧪 FOCUSED NONLOCAL TRAINING")
    print("Building on standard EM43 foundation")
    print("=" * 60)
    
    # Run focused training
    best_model, final_accuracy, test_cases = focused_training()
    
    print(f"\n🎯 TRAINING COMPLETE!")
    print(f"Final accuracy: {final_accuracy:.1%}")
    
    if best_model and final_accuracy > 0:
        rule, prog = best_model
        
        print("\n📊 FINAL RESULTS:")
        results = evaluate_hybrid(rule, prog, test_cases)
        
        for r in results:
            status = "✓" if r['correct'] else "✗"
            conv = "C" if r['convergent'] else "NC"
            print(f"  {r['a']}+{r['b']}={r['expected']} → {r['result']} {status} {conv}")
        
        print(f"\n🔍 ANALYSIS:")
        print(f"Program: {prog}")
        
        # Test on expanded cases if 1+1 works
        if final_accuracy >= 1.0:
            print("\n🚀 TESTING EXPANDED CASES:")
            expanded_cases = [(1, 1), (1, 2), (2, 1)]
            expanded_results = evaluate_hybrid(rule, prog, expanded_cases)
            
            for r in expanded_results:
                status = "✓" if r['correct'] else "✗"
                conv = "C" if r['convergent'] else "NC"
                print(f"  {r['a']}+{r['b']}={r['expected']} → {r['result']} {status} {conv}")
    
    else:
        print("\n❌ Still need to debug the approach")
    
    end_time = time.time()
    print(f"\n⏱️  Training time: {(end_time - start_time):.1f} seconds")
    
    if final_accuracy >= 1.0:
        print("\n🎉 SUCCESS: Focused approach works!")
    elif final_accuracy > 0:
        print(f"\n✅ PROGRESS: {final_accuracy:.1%} accuracy shows improvement")
    else:
        print("\n⚠️  Need to continue debugging")
