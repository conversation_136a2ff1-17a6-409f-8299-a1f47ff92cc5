"""
Improved Addition Trainer with Optimal Decoding Strategy
Using pattern+zero_count approach discovered in experiments
"""

import numpy as np
import time
import pickle
from tqdm import tqdm
from em43_numba import _sanitize_rule, _sanitize_programme

def simulate_addition_improved(rule, prog, a, b, window=300, max_steps=600):
    """
    Improved addition simulation using pattern+zero_count strategy.
    
    Halting: R...R pattern with only zeros/blues between
    Decoding: Count zeros between R markers
    """
    L = len(prog)
    N = window
    
    # Initialize state
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(L):
        state[j] = prog[j]
    state[L] = 3      # B
    state[L + 1] = 3  # B
    
    # Write inputs: 0^(a+1) R 0^(b+1) R
    r1_pos = L + 2 + a + 1
    if r1_pos >= N:
        return -10
    state[r1_pos] = 2
    
    r2_pos = r1_pos + 1 + b + 1
    if r2_pos >= N:
        return -10
    state[r2_pos] = 2
    
    # Simulation range
    sim_range = min(N - 1, r2_pos + max(a + b + 10, 30))
    
    # Run CA simulation
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(1, sim_range):
            idx = (state[x-1] << 4) | (state[x] << 2) | state[x+1]
            nxt[x] = rule[idx]
        
        # Pattern-based halting: R...R with only zeros/blues between
        r_positions = [x for x in range(sim_range) if nxt[x] == 2]
        
        if len(r_positions) >= 2:
            # Check if region between Rs contains only zeros and blues
            start, end = r_positions[0], r_positions[-1]
            between_valid = all(nxt[x] in [0, 3] for x in range(start + 1, end))
            
            if between_valid:
                # Decode: count zeros between R markers
                zero_count = sum(1 for x in range(start + 1, end) if nxt[x] == 0)
                return zero_count
        
        state = nxt
    
    return -10  # Didn't halt

def evaluate_improved(rule, prog, test_cases):
    """Evaluate using improved strategy."""
    results = []
    
    for a, b in test_cases:
        expected = a + b
        result = simulate_addition_improved(rule, prog, a, b)
        
        status = "✓" if result == expected else "✗"
        convergent = result != -10
        
        results.append({
            'a': a, 'b': b, 'expected': expected, 'result': result,
            'correct': result == expected, 'convergent': convergent, 'status': status
        })
    
    return results

def train_with_improved_strategy():
    """Train addition model with improved decoding strategy."""
    print("🚀 TRAINING WITH IMPROVED DECODING STRATEGY")
    print("=" * 60)
    print("Strategy: pattern+zero_count")
    print("Halting: R...R pattern with zeros/blues between")
    print("Decoding: Count zeros between R markers")
    
    # Load base model
    try:
        with open('best_genome.pkl', 'rb') as f:
            model_data = pickle.load(f)
        if isinstance(model_data, dict):
            base_rule = model_data['rule']
            base_prog = model_data['prog']
        else:
            base_rule, base_prog = model_data
        print("✅ Loaded divide-by-2 model as starting point")
    except:
        print("❌ Could not load base model")
        return None, None
    
    # Test cases - start with basics
    target_cases = [(1, 1), (1, 2), (2, 1), (2, 2), (3, 1), (1, 3), (3, 2), (2, 3)]
    print(f"Target cases: {target_cases}")
    
    # Evaluate initial performance with improved strategy
    print("\n📊 INITIAL PERFORMANCE (Improved Strategy)")
    initial_results = evaluate_improved(base_rule, base_prog, target_cases)
    
    initial_correct = sum(1 for r in initial_results if r['correct'])
    initial_convergent = sum(1 for r in initial_results if r['convergent'])
    
    print(f"Initial accuracy: {initial_correct}/{len(target_cases)} ({initial_correct/len(target_cases)*100:.1f}%)")
    print(f"Convergence rate: {initial_convergent}/{len(target_cases)} ({initial_convergent/len(target_cases)*100:.1f}%)")
    
    print("Initial results:")
    for r in initial_results:
        print(f"  {r['a']}+{r['b']}={r['expected']} → {r['result']} {r['status']}")
    
    # Training parameters
    POP_SIZE = 60
    GENERATIONS = 300
    MUTATION_RATE = 0.01  # Slightly higher for exploration
    
    print(f"\n🚀 TRAINING")
    print(f"Population: {POP_SIZE}, Generations: {GENERATIONS}, Mutation: {MUTATION_RATE}")
    
    # Initialize population
    population = []
    rng = np.random.default_rng()
    
    for i in range(POP_SIZE):
        rule = base_rule.copy()
        prog = base_prog.copy()
        
        if i > 0:  # Keep one copy unchanged
            # Mutations
            mask = rng.random(64) < MUTATION_RATE
            rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)
            
            mask = rng.random(len(prog)) < MUTATION_RATE * 2
            prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.8, 0.15, 0.05])
        
        rule = _sanitize_rule(rule)
        prog = _sanitize_programme(prog)
        population.append((rule, prog))
    
    # Training loop
    best_accuracy = initial_correct / len(target_cases)
    best_model = (base_rule, base_prog)
    best_fitness = -1000
    
    print("Training...")
    
    for gen in tqdm(range(GENERATIONS), desc="Improved Training"):
        # Evaluate population
        fitness_scores = []
        
        for rule, prog in population:
            results = evaluate_improved(rule, prog, target_cases)
            
            correct = sum(1 for r in results if r['correct'])
            convergent = sum(1 for r in results if r['convergent'])
            
            accuracy = correct / len(target_cases)
            convergence_rate = convergent / len(target_cases)
            
            # Enhanced fitness function
            fitness = correct * 200 + convergent * 50
            
            # Bonus for perfect accuracy
            if accuracy == 1.0:
                fitness += 1000
            elif accuracy >= 0.75:
                fitness += 500
            elif accuracy >= 0.5:
                fitness += 200
            
            fitness_scores.append(fitness)
        
        # Track best
        gen_best_idx = np.argmax(fitness_scores)
        gen_best_fitness = fitness_scores[gen_best_idx]
        
        gen_results = evaluate_improved(population[gen_best_idx][0], population[gen_best_idx][1], target_cases)
        gen_accuracy = sum(1 for r in gen_results if r['correct']) / len(target_cases)
        gen_convergence = sum(1 for r in gen_results if r['convergent']) / len(target_cases)
        
        if gen_accuracy > best_accuracy:
            best_accuracy = gen_accuracy
            best_model = population[gen_best_idx]
            best_fitness = gen_best_fitness
        
        # Progress report
        if gen % 50 == 0:
            print(f"  Gen {gen}: best_acc={best_accuracy:.2%}, conv={gen_convergence:.2%}, fitness={gen_best_fitness:.1f}")
            
            # Show current results
            current_failing = [r for r in gen_results if not r['correct']]
            if len(current_failing) <= 5:
                print(f"    Failing: {[(r['a'], r['b']) for r in current_failing]}")
            else:
                print(f"    Failing: {len(current_failing)} cases")
        
        # Early stopping if perfect
        if best_accuracy >= 1.0:
            print(f"  🎉 Perfect accuracy reached at generation {gen}!")
            break
        
        # Selection and reproduction
        if gen < GENERATIONS - 1:
            # Elite selection
            sorted_indices = np.argsort(fitness_scores)[::-1]
            n_elite = POP_SIZE // 3
            
            new_population = []
            
            # Keep elites
            for i in range(n_elite):
                new_population.append(population[sorted_indices[i]])
            
            # Generate offspring
            while len(new_population) < POP_SIZE:
                # Tournament selection
                tournament_size = 3
                tournament_indices = rng.choice(sorted_indices[:POP_SIZE//2], size=tournament_size, replace=False)
                parent_idx = tournament_indices[0]
                
                rule, prog = population[parent_idx]
                
                # Adaptive mutation
                current_mutation_rate = MUTATION_RATE
                if gen > 150 and best_accuracy < 0.75:
                    current_mutation_rate *= 1.5
                
                # Mutate
                new_rule = rule.copy()
                new_prog = prog.copy()
                
                mask = rng.random(64) < current_mutation_rate
                new_rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)
                
                mask = rng.random(len(prog)) < current_mutation_rate * 2
                new_prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.8, 0.15, 0.05])
                
                new_population.append((_sanitize_rule(new_rule), _sanitize_programme(new_prog)))
            
            population = new_population
    
    return best_model, best_accuracy, target_cases

def comprehensive_test(rule, prog):
    """Test the improved model on various cases."""
    print("\n" + "=" * 60)
    print("🔬 COMPREHENSIVE TESTING")
    print("=" * 60)
    
    # Test different ranges
    test_ranges = {
        'Basic': [(1, 1), (1, 2), (2, 1), (2, 2)],
        'Extended': [(3, 1), (1, 3), (3, 2), (2, 3), (3, 3)],
        'Challenging': [(4, 1), (1, 4), (4, 2), (2, 4), (1, 5), (5, 1)],
        'Large': [(4, 3), (3, 4), (4, 4), (5, 2), (2, 5), (5, 3)]
    }
    
    overall_correct = 0
    overall_total = 0
    
    for range_name, test_cases in test_ranges.items():
        print(f"\n{range_name} Range:")
        results = evaluate_improved(rule, prog, test_cases)
        
        correct = sum(1 for r in results if r['correct'])
        convergent = sum(1 for r in results if r['convergent'])
        
        print(f"  Accuracy: {correct}/{len(test_cases)} ({correct/len(test_cases)*100:.1f}%)")
        print(f"  Convergence: {convergent}/{len(test_cases)} ({convergent/len(test_cases)*100:.1f}%)")
        
        for r in results:
            print(f"    {r['a']}+{r['b']}={r['expected']} → {r['result']} {r['status']}")
        
        overall_correct += correct
        overall_total += len(test_cases)
    
    overall_accuracy = overall_correct / overall_total
    print(f"\n📊 OVERALL PERFORMANCE:")
    print(f"Total accuracy: {overall_correct}/{overall_total} ({overall_accuracy:.1%})")
    
    return overall_accuracy

if __name__ == "__main__":
    start_time = time.time()
    
    print("🎯 IMPROVED ADDITION TRAINING")
    print("Using optimal decoding strategy from experiments")
    print("=" * 60)
    
    # Train with improved strategy
    best_model, final_accuracy, target_cases = train_with_improved_strategy()
    
    if best_model:
        print(f"\n🎯 TRAINING COMPLETE!")
        print(f"Final accuracy: {final_accuracy:.2%}")
        
        # Comprehensive testing
        rule, prog = best_model
        overall_accuracy = comprehensive_test(rule, prog)
        
        # Save improved model
        improved_model = {
            'rule': rule,
            'prog': prog,
            'accuracy': final_accuracy,
            'overall_accuracy': overall_accuracy,
            'strategy': 'pattern+zero_count',
            'target_cases': target_cases
        }
        
        with open('improved_addition_model.pkl', 'wb') as f:
            pickle.dump(improved_model, f)
        
        end_time = time.time()
        
        print(f"\n⏱️  Training time: {(end_time - start_time)/60:.1f} minutes")
        print(f"💾 Model saved: improved_addition_model.pkl")
        
        if final_accuracy >= 0.75:
            print("\n🎉 EXCELLENT: Improved strategy shows significant gains!")
        elif final_accuracy >= 0.5:
            print("\n✅ GOOD: Clear improvement over baseline!")
        else:
            print("\n💡 PROGRESS: Strategy shows promise, needs refinement!")
        
        print(f"\n🔬 KEY INSIGHTS:")
        print(f"  ✅ Pattern-based halting is more reliable than ratio-based")
        print(f"  ✅ Zero-counting decoding aligns with addition semantics")
        print(f"  ✅ R...R pattern detection captures computation completion")
        print(f"  🚀 This approach can be applied to other arithmetic operations!")
    
    else:
        print("❌ Training failed - check model loading")
