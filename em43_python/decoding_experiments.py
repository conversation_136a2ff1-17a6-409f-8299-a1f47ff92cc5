"""
Decoding and Halting Experiments for Addition EM
Testing different strategies to improve accuracy
"""

import numpy as np
import pickle
from em43_numba import _sanitize_rule, _sanitize_programme

def simulate_addition_with_strategy(rule, prog, a, b, halt_strategy="blue_ratio", decode_strategy="rightmost_r", 
                                  window=300, max_steps=600, **kwargs):
    """
    Addition simulation with configurable halting and decoding strategies.
    
    Halting strategies:
    - "blue_ratio": blue/live >= threshold (current)
    - "all_blue": only blue cells remain (like divide-by-2)
    - "stable": no changes for N steps
    - "pattern": specific pattern detected
    
    Decoding strategies:
    - "rightmost_r": position of rightmost R minus offset (current)
    - "leftmost_r": position of leftmost R minus offset
    - "r_distance": distance between Rs
    - "blue_count": count of blue cells
    - "zero_count": count of zeros in specific region
    """
    L = len(prog)
    N = window
    
    # Initialize state
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(L):
        state[j] = prog[j]
    state[L] = 3      # B
    state[L + 1] = 3  # B
    
    # Write inputs: 0^(a+1) R 0^(b+1) R
    r1_pos = L + 2 + a + 1
    if r1_pos >= N:
        return -10
    state[r1_pos] = 2
    
    r2_pos = r1_pos + 1 + b + 1
    if r2_pos >= N:
        return -10
    state[r2_pos] = 2
    
    # Simulation range
    sim_range = min(N - 1, r2_pos + max(a + b + 10, 30))
    
    # For stable halting
    prev_states = []
    stable_count = 0
    
    # Run CA simulation
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(1, sim_range):
            idx = (state[x-1] << 4) | (state[x] << 2) | state[x+1]
            nxt[x] = rule[idx]
        
        # Check halting condition
        halted = False
        
        if halt_strategy == "blue_ratio":
            threshold = kwargs.get('threshold', 0.5)
            live = blue = 0
            for x in range(sim_range):
                v = nxt[x]
                if v != 0:
                    live += 1
                    if v == 3:
                        blue += 1
            halted = live > 0 and blue / live >= threshold
            
        elif halt_strategy == "all_blue":
            # Only blue cells remain (like divide-by-2)
            non_blue_count = 0
            blue_count = 0
            for x in range(sim_range):
                v = nxt[x]
                if v != 0:
                    if v == 3:
                        blue_count += 1
                    else:
                        non_blue_count += 1
            halted = blue_count > 0 and non_blue_count == 0
            
        elif halt_strategy == "stable":
            # No changes for N steps
            stable_steps = kwargs.get('stable_steps', 5)
            if len(prev_states) >= stable_steps:
                if all(np.array_equal(nxt[:sim_range], prev[:sim_range]) for prev in prev_states[-stable_steps:]):
                    halted = True
            prev_states.append(nxt.copy())
            if len(prev_states) > stable_steps:
                prev_states.pop(0)
                
        elif halt_strategy == "pattern":
            # Specific pattern detected (e.g., R...R with only zeros/blues between)
            r_positions = [x for x in range(sim_range) if nxt[x] == 2]
            if len(r_positions) >= 2:
                # Check if region between Rs contains only zeros and blues
                start, end = r_positions[0], r_positions[-1]
                between_valid = all(nxt[x] in [0, 3] for x in range(start + 1, end))
                halted = between_valid
        
        if halted:
            # Apply decoding strategy
            if decode_strategy == "rightmost_r":
                rpos = -1
                for x in range(sim_range - 1, -1, -1):
                    if nxt[x] == 2:
                        rpos = x
                        break
                if rpos != -1:
                    return rpos - (L + 3)
                else:
                    return -10
                    
            elif decode_strategy == "leftmost_r":
                rpos = -1
                for x in range(sim_range):
                    if nxt[x] == 2:
                        rpos = x
                        break
                if rpos != -1:
                    return rpos - (L + 3)
                else:
                    return -10
                    
            elif decode_strategy == "r_distance":
                r_positions = [x for x in range(sim_range) if nxt[x] == 2]
                if len(r_positions) >= 2:
                    return r_positions[-1] - r_positions[0] - 1
                else:
                    return -10
                    
            elif decode_strategy == "blue_count":
                blue_count = sum(1 for x in range(sim_range) if nxt[x] == 3)
                return blue_count
                
            elif decode_strategy == "zero_count":
                # Count zeros in specific region
                r_positions = [x for x in range(sim_range) if nxt[x] == 2]
                if len(r_positions) >= 2:
                    start, end = r_positions[0], r_positions[-1]
                    zero_count = sum(1 for x in range(start + 1, end) if nxt[x] == 0)
                    return zero_count
                else:
                    return -10
                    
            elif decode_strategy == "blue_position":
                # Position of rightmost blue cell
                blue_pos = -1
                for x in range(sim_range - 1, -1, -1):
                    if nxt[x] == 3:
                        blue_pos = x
                        break
                if blue_pos != -1:
                    return blue_pos - (L + 3)
                else:
                    return -10
        
        state = nxt
    
    return -10  # Didn't halt

def test_strategies():
    """Test different halting and decoding strategies."""
    print("🧪 TESTING DECODING AND HALTING STRATEGIES")
    print("=" * 60)
    
    # Load working model
    try:
        with open('best_genome.pkl', 'rb') as f:
            model_data = pickle.load(f)
        if isinstance(model_data, dict):
            rule = model_data['rule']
            prog = model_data['prog']
        else:
            rule, prog = model_data
        print("✅ Loaded divide-by-2 model")
    except:
        print("❌ Could not load model")
        return
    
    # Test cases
    test_cases = [(1, 1), (1, 2), (2, 1), (2, 2), (3, 1), (1, 3), (3, 2), (2, 3)]
    
    # Strategy combinations to test
    strategies = [
        # Current approach
        {"halt": "blue_ratio", "decode": "rightmost_r", "threshold": 0.5},
        
        # Divide-by-2 style
        {"halt": "all_blue", "decode": "blue_position"},
        {"halt": "all_blue", "decode": "rightmost_r"},
        
        # Alternative ratios
        {"halt": "blue_ratio", "decode": "rightmost_r", "threshold": 0.8},
        {"halt": "blue_ratio", "decode": "rightmost_r", "threshold": 0.3},
        
        # Different decoding
        {"halt": "blue_ratio", "decode": "leftmost_r", "threshold": 0.5},
        {"halt": "blue_ratio", "decode": "r_distance", "threshold": 0.5},
        {"halt": "blue_ratio", "decode": "blue_count", "threshold": 0.5},
        {"halt": "blue_ratio", "decode": "zero_count", "threshold": 0.5},
        
        # Stable halting
        {"halt": "stable", "decode": "rightmost_r", "stable_steps": 3},
        {"halt": "stable", "decode": "blue_count", "stable_steps": 5},
        
        # Pattern-based
        {"halt": "pattern", "decode": "zero_count"},
        {"halt": "pattern", "decode": "r_distance"},
    ]
    
    print(f"Testing {len(strategies)} strategy combinations on {len(test_cases)} cases")
    print()
    
    results = {}
    
    for i, strategy in enumerate(strategies):
        halt_strategy = strategy["halt"]
        decode_strategy = strategy["decode"]
        strategy_name = f"{halt_strategy}+{decode_strategy}"
        
        print(f"Strategy {i+1}: {strategy_name}")
        
        correct = 0
        convergent = 0
        details = []
        
        for a, b in test_cases:
            expected = a + b
            result = simulate_addition_with_strategy(rule, prog, a, b, 
                                                   halt_strategy=halt_strategy,
                                                   decode_strategy=decode_strategy,
                                                   **{k: v for k, v in strategy.items() if k not in ["halt", "decode"]})
            
            if result != -10:
                convergent += 1
                if result == expected:
                    correct += 1
            
            status = "✓" if result == expected else "✗"
            details.append(f"{a}+{b}={expected}→{result}{status}")
        
        accuracy = correct / len(test_cases)
        convergence = convergent / len(test_cases)
        
        results[strategy_name] = {
            'accuracy': accuracy,
            'convergence': convergence,
            'correct': correct,
            'convergent': convergent,
            'details': details
        }
        
        print(f"  Accuracy: {correct}/{len(test_cases)} ({accuracy:.1%})")
        print(f"  Convergence: {convergent}/{len(test_cases)} ({convergence:.1%})")
        print(f"  Results: {' | '.join(details[:4])}...")
        print()
    
    # Summary
    print("📊 STRATEGY COMPARISON")
    print("=" * 60)
    
    # Sort by accuracy, then convergence
    sorted_results = sorted(results.items(), key=lambda x: (x[1]['accuracy'], x[1]['convergence']), reverse=True)
    
    print("Rank | Strategy | Accuracy | Convergence | Details")
    print("-" * 70)
    
    for rank, (strategy_name, data) in enumerate(sorted_results, 1):
        print(f"{rank:2d}   | {strategy_name:20s} | {data['accuracy']:7.1%} | {data['convergence']:10.1%} | {data['correct']}/{len(test_cases)} correct")
    
    # Show best strategy details
    if sorted_results:
        best_strategy, best_data = sorted_results[0]
        print(f"\n🏆 BEST STRATEGY: {best_strategy}")
        print("Detailed results:")
        for detail in best_data['details']:
            print(f"  {detail}")
        
        return best_strategy, best_data
    
    return None, None

def analyze_halting_patterns():
    """Analyze what patterns emerge when the model halts."""
    print("\n🔍 ANALYZING HALTING PATTERNS")
    print("=" * 60)
    
    # Load working model
    try:
        with open('best_genome.pkl', 'rb') as f:
            model_data = pickle.load(f)
        if isinstance(model_data, dict):
            rule = model_data['rule']
            prog = model_data['prog']
        else:
            rule, prog = model_data
    except:
        print("❌ Could not load model")
        return
    
    # Test a few cases and show the halting state
    test_cases = [(1, 1), (1, 2), (2, 1), (2, 2)]
    
    for a, b in test_cases:
        expected = a + b
        print(f"\nAnalyzing {a} + {b} = {expected}:")
        
        # Run simulation and capture halting state
        L = len(prog)
        N = 300
        
        state = np.zeros(N, dtype=np.uint8)
        
        # Initialize
        for j in range(L):
            state[j] = prog[j]
        state[L] = 3
        state[L + 1] = 3
        
        r1_pos = L + 2 + a + 1
        state[r1_pos] = 2
        r2_pos = r1_pos + 1 + b + 1
        state[r2_pos] = 2
        
        sim_range = min(N - 1, r2_pos + 30)
        
        # Run until halt
        for step in range(600):
            nxt = np.zeros(N, dtype=np.uint8)
            
            for x in range(1, sim_range):
                idx = (state[x-1] << 4) | (state[x] << 2) | state[x+1]
                nxt[x] = rule[idx]
            
            # Check current halting condition
            live = blue = 0
            for x in range(sim_range):
                v = nxt[x]
                if v != 0:
                    live += 1
                    if v == 3:
                        blue += 1
            
            if live > 0 and blue / live >= 0.5:
                print(f"  Halted at step {step}")
                print(f"  Live cells: {live}, Blue cells: {blue}, Ratio: {blue/live:.2f}")
                
                # Show the pattern
                pattern = ""
                for x in range(L, min(sim_range, L + 50)):
                    v = nxt[x]
                    if v == 0:
                        pattern += "0"
                    elif v == 1:
                        pattern += "1"
                    elif v == 2:
                        pattern += "R"
                    elif v == 3:
                        pattern += "B"
                
                print(f"  Pattern: {pattern}")
                
                # Find Rs and analyze regions
                r_positions = [x for x in range(sim_range) if nxt[x] == 2]
                print(f"  R positions: {r_positions}")
                
                if len(r_positions) >= 2:
                    between_start, between_end = r_positions[0], r_positions[-1]
                    between_pattern = ""
                    for x in range(between_start + 1, between_end):
                        v = nxt[x]
                        if v == 0:
                            between_pattern += "0"
                        elif v == 3:
                            between_pattern += "B"
                        else:
                            between_pattern += str(v)
                    print(f"  Between Rs: '{between_pattern}' (length: {len(between_pattern)})")
                
                break
            
            state = nxt

if __name__ == "__main__":
    # Test different strategies
    best_strategy, best_data = test_strategies()
    
    # Analyze halting patterns
    analyze_halting_patterns()
    
    if best_strategy:
        print(f"\n🎯 RECOMMENDATION:")
        print(f"Use strategy: {best_strategy}")
        print(f"This achieved {best_data['accuracy']:.1%} accuracy with {best_data['convergence']:.1%} convergence")
        print(f"Compared to current approach which likely has lower accuracy")
        
        print(f"\n💡 NEXT STEPS:")
        print(f"1. Implement the best strategy in the training loop")
        print(f"2. Retrain models with the improved decoding/halting")
        print(f"3. Test on larger problem sets")
        print(f"4. Consider hybrid approaches combining multiple strategies")
