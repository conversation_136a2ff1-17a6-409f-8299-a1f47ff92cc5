"""
Advanced Addition Trainer with Curriculum Learning and Hybrid Techniques
"""

import numpy as np
import time
import pickle
from pathlib import Path
from tqdm import tqdm
import matplotlib.pyplot as plt
from em43_numba import _simulate_addition, _sanitize_rule, _sanitize_programme

# Curriculum stages - progressively harder addition problems
CURRICULUM = {
    'stage1': {
        'inputs_a': np.array([1, 1, 2], dtype=np.int64),
        'inputs_b': np.array([1, 2, 1], dtype=np.int64),
        'description': 'Basic addition [1+1, 1+2, 2+1]',
        'target_fitness': 75.0
    },
    'stage2': {
        'inputs_a': np.array([1, 1, 2, 2, 3, 1], dtype=np.int64),
        'inputs_b': np.array([1, 2, 1, 2, 1, 3], dtype=np.int64),
        'description': 'Extended basics [1+1, 1+2, 2+1, 2+2, 3+1, 1+3]',
        'target_fitness': 70.0
    },
    'stage3': {
        'inputs_a': np.array([1, 1, 2, 2, 3, 1, 3, 2, 4, 1], dtype=np.int64),
        'inputs_b': np.array([1, 2, 1, 2, 1, 3, 2, 3, 1, 4], dtype=np.int64),
        'description': 'Medium numbers [previous + 3+2, 2+3, 4+1, 1+4]',
        'target_fitness': 65.0
    },
    'stage4': {
        'inputs_a': np.array([1, 1, 2, 2, 3, 1, 3, 2, 4, 1, 3, 4, 5, 2], dtype=np.int64),
        'inputs_b': np.array([1, 2, 1, 2, 1, 3, 2, 3, 1, 4, 3, 2, 1, 5], dtype=np.int64),
        'description': 'Larger numbers [previous + 3+3, 4+2, 5+1, 2+5]',
        'target_fitness': 60.0
    },
    'stage5': {
        'inputs_a': np.array([1, 2, 3, 4, 5, 6, 7, 8], dtype=np.int64),
        'inputs_b': np.array([1, 2, 3, 4, 5, 4, 3, 2], dtype=np.int64),
        'description': 'Full range [1+1 through 8+2]',
        'target_fitness': 55.0
    }
}

# Adaptive parameters based on curriculum stage
def get_stage_params(stage_name):
    base_params = {
        'window': 300,
        'max_steps': 600,
        'halt_thresh': 0.50,
        'pop_size': 100,
        'prog_length': 12,
        'generations': 300,
        'mutation_rate': 0.12,
        'elite_frac': 0.15
    }

    # Scale parameters based on problem complexity
    stage_num = int(stage_name[-1])

    # Larger problems need more resources
    base_params['window'] = 250 + (stage_num * 50)
    base_params['max_steps'] = 500 + (stage_num * 100)
    base_params['pop_size'] = 80 + (stage_num * 20)
    base_params['prog_length'] = 10 + (stage_num * 2)
    base_params['generations'] = 250 + (stage_num * 50)

    # Lower mutation rate for later stages (fine-tuning)
    base_params['mutation_rate'] = max(0.08, 0.15 - (stage_num * 0.01))

    return base_params

def evaluate_genome(rule, prog, stage_data, params):
    """Evaluate a single genome on a curriculum stage."""
    inputs_a = stage_data['inputs_a']
    inputs_b = stage_data['inputs_b']
    targets = inputs_a + inputs_b

    outputs = _simulate_addition(rule, prog, inputs_a, inputs_b,
                                params['window'], params['max_steps'], params['halt_thresh'])

    correct = 0
    convergent = 0
    total_error = 0

    for pred, target in zip(outputs, targets):
        if pred != -10:
            convergent += 1
            if pred == target:
                correct += 1
            else:
                total_error += abs(pred - target)

    # Enhanced fitness function
    if convergent == 0:
        fitness = -100  # Heavily penalize non-convergence
    else:
        accuracy = correct / len(outputs)
        convergence_rate = convergent / len(outputs)
        avg_error = total_error / len(outputs) if convergent > 0 else 10

        # Weighted fitness with generalization bonus
        fitness = (accuracy * 60) + (convergence_rate * 25) - (avg_error * 3)

        # Bonus for perfect accuracy
        if accuracy == 1.0:
            fitness += 20

    return fitness, outputs, correct, convergent

def evaluate_generalization(rule, prog, params):
    """Test generalization on unseen addition problems."""
    # Test cases not in any curriculum stage
    test_a = np.array([6, 7, 8, 9], dtype=np.int64)
    test_b = np.array([3, 2, 1, 1], dtype=np.int64)
    test_targets = test_a + test_b

    outputs = _simulate_addition(rule, prog, test_a, test_b,
                                params['window'], params['max_steps'], params['halt_thresh'])

    correct = np.sum((outputs != -10) & (outputs == test_targets))
    convergent = np.sum(outputs != -10)

    if convergent == 0:
        return 0.0

    return correct / len(test_targets) * 100

def random_genome(prog_length):
    """Generate a random genome."""
    rng = np.random.default_rng()
    rule = rng.integers(0, 4, 64, dtype=np.uint8)
    prog = rng.choice([0, 1, 2], size=prog_length, p=[0.75, 0.2, 0.05])  # Favor zeros
    return _sanitize_rule(rule), _sanitize_programme(prog)

def mutate_genome(rule, prog, rate=0.1):
    """Mutate a genome with adaptive rate."""
    new_rule = rule.copy()
    new_prog = prog.copy()

    rng = np.random.default_rng()

    # Mutate rule with lower rate for fine-tuning
    mask = rng.random(64) < rate
    new_rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)

    # Mutate program with even lower rate
    prog_rate = rate * 0.8
    mask = rng.random(len(prog)) < prog_rate
    new_prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.75, 0.2, 0.05])

    return _sanitize_rule(new_rule), _sanitize_programme(new_prog)

def crossover_genomes(rule1, prog1, rule2, prog2):
    """Crossover two genomes."""
    rng = np.random.default_rng()

    # Rule crossover
    cut = rng.integers(1, 63)
    new_rule = np.concatenate([rule1[:cut], rule2[cut:]])

    # Program crossover
    cut = rng.integers(1, len(prog1)-1)
    new_prog = np.concatenate([prog1[:cut], prog2[cut:]])

    return _sanitize_rule(new_rule), _sanitize_programme(new_prog)

def train_stage(stage_name, stage_data, params, initial_population=None):
    """Train on a single curriculum stage."""
    print(f"\n{'='*60}")
    print(f"TRAINING STAGE: {stage_name.upper()}")
    print(f"{'='*60}")
    print(f"Description: {stage_data['description']}")
    print(f"Training cases: {len(stage_data['inputs_a'])}")
    print(f"Target fitness: {stage_data['target_fitness']}")
    print(f"Parameters: pop={params['pop_size']}, gen={params['generations']}, prog_len={params['prog_length']}")

    # Initialize or inherit population
    if initial_population is None:
        print("Initializing random population...")
        population = []
        for i in range(params['pop_size']):
            rule, prog = random_genome(params['prog_length'])
            population.append((rule, prog))
    else:
        print(f"Inheriting {len(initial_population)} genomes from previous stage...")
        population = initial_population.copy()

        # Expand population if needed
        while len(population) < params['pop_size']:
            # Mutate existing good genomes
            idx = np.random.randint(len(initial_population))
            rule, prog = initial_population[idx]
            rule, prog = mutate_genome(rule, prog, rate=0.2)
            population.append((rule, prog))

    # Track training progress
    fitness_history = []
    generalization_history = []
    best_fitness = -np.inf
    best_genome = None

    # Evolution loop
    for gen in tqdm(range(params['generations']), desc=f"Stage {stage_name}"):
        # Evaluate population
        fitness_scores = []
        for rule, prog in population:
            fitness, _, _, _ = evaluate_genome(rule, prog, stage_data, params)
            fitness_scores.append(fitness)

        fitness_array = np.array(fitness_scores)

        # Track best
        gen_best_idx = np.argmax(fitness_array)
        gen_best_fitness = fitness_array[gen_best_idx]

        if gen_best_fitness > best_fitness:
            best_fitness = gen_best_fitness
            best_genome = population[gen_best_idx]

        # Test generalization
        if gen % 25 == 0:
            gen_score = evaluate_generalization(best_genome[0], best_genome[1], params)
            generalization_history.append(gen_score)

            if gen % 50 == 0:
                avg_fitness = np.mean(fitness_array)
                print(f"  Gen {gen}: best={best_fitness:.2f}, avg={avg_fitness:.2f}, generalization={gen_score:.1f}%")

        fitness_history.append({
            'best': best_fitness,
            'mean': np.mean(fitness_array),
            'std': np.std(fitness_array)
        })

        # Early stopping if target reached
        if best_fitness >= stage_data['target_fitness']:
            print(f"  Target fitness {stage_data['target_fitness']} reached at generation {gen}!")
            break

        # Selection and reproduction
        if gen < params['generations'] - 1:
            n_elite = max(1, int(params['pop_size'] * params['elite_frac']))
            elite_indices = np.argsort(fitness_array)[-n_elite:]

            new_population = []

            # Keep elites
            for idx in elite_indices:
                new_population.append(population[idx])

            # Generate offspring
            rng = np.random.default_rng()
            while len(new_population) < params['pop_size']:
                if rng.random() < 0.7:  # Crossover
                    p1_idx = rng.choice(elite_indices)
                    p2_idx = rng.choice(elite_indices)
                    rule1, prog1 = population[p1_idx]
                    rule2, prog2 = population[p2_idx]
                    child_rule, child_prog = crossover_genomes(rule1, prog1, rule2, prog2)
                else:  # Mutation
                    parent_idx = rng.choice(elite_indices)
                    parent_rule, parent_prog = population[parent_idx]
                    child_rule, child_prog = mutate_genome(parent_rule, parent_prog, params['mutation_rate'])

                new_population.append((child_rule, child_prog))

            population = new_population

    # Final evaluation
    final_fitness, final_outputs, final_correct, final_convergent = evaluate_genome(
        best_genome[0], best_genome[1], stage_data, params)

    final_generalization = evaluate_generalization(best_genome[0], best_genome[1], params)

    print(f"\nStage {stage_name} Results:")
    print(f"  Best fitness: {final_fitness:.2f}")
    print(f"  Training accuracy: {final_correct}/{len(stage_data['inputs_a'])} ({final_correct/len(stage_data['inputs_a'])*100:.1f}%)")
    print(f"  Generalization: {final_generalization:.1f}%")

    # Return top performers for next stage
    fitness_scores = []
    for rule, prog in population:
        fitness, _, _, _ = evaluate_genome(rule, prog, stage_data, params)
        fitness_scores.append(fitness)

    # Select top 20% for transfer to next stage
    n_transfer = max(5, params['pop_size'] // 5)
    top_indices = np.argsort(fitness_scores)[-n_transfer:]
    transfer_population = [population[i] for i in top_indices]

    return best_genome, final_fitness, transfer_population, fitness_history, generalization_history

def curriculum_training():
    """Run full curriculum training with progressive difficulty."""
    print("🚀 ADVANCED ADDITION TRAINING WITH CURRICULUM LEARNING")
    print("=" * 80)

    all_results = {}
    transfer_population = None
    overall_best_genome = None
    overall_best_fitness = -np.inf

    # Train through curriculum stages
    for stage_name in ['stage1', 'stage2', 'stage3', 'stage4', 'stage5']:
        stage_data = CURRICULUM[stage_name]
        params = get_stage_params(stage_name)

        best_genome, best_fitness, transfer_pop, fitness_hist, gen_hist = train_stage(
            stage_name, stage_data, params, transfer_population)

        # Track overall best
        if best_fitness > overall_best_fitness:
            overall_best_fitness = best_fitness
            overall_best_genome = best_genome

        # Store results
        all_results[stage_name] = {
            'best_genome': best_genome,
            'best_fitness': best_fitness,
            'fitness_history': fitness_hist,
            'generalization_history': gen_hist,
            'params': params
        }

        # Transfer best performers to next stage
        transfer_population = transfer_pop

        # Save checkpoint
        checkpoint_path = Path(f"addition_checkpoint_{stage_name}.pkl")
        with open(checkpoint_path, 'wb') as f:
            pickle.dump(all_results, f)
        print(f"  Checkpoint saved: {checkpoint_path}")

    return overall_best_genome, overall_best_fitness, all_results

def comprehensive_test(best_genome, all_results):
    """Run comprehensive testing on the final model."""
    print("\n" + "=" * 80)
    print("🧪 COMPREHENSIVE TESTING")
    print("=" * 80)

    rule, prog = best_genome

    # Test on all curriculum stages
    for stage_name, stage_data in CURRICULUM.items():
        params = get_stage_params(stage_name)
        fitness, outputs, correct, convergent = evaluate_genome(rule, prog, stage_data, params)

        inputs_a = stage_data['inputs_a']
        inputs_b = stage_data['inputs_b']
        expected = inputs_a + inputs_b

        print(f"\n{stage_name.upper()}: {stage_data['description']}")
        print(f"  Accuracy: {correct}/{len(expected)} ({correct/len(expected)*100:.1f}%)")
        print(f"  Convergence: {convergent}/{len(expected)} ({convergent/len(expected)*100:.1f}%)")

        # Show some examples
        for i in range(min(5, len(inputs_a))):
            status = "✓" if outputs[i] == expected[i] else "✗"
            print(f"    {inputs_a[i]} + {inputs_b[i]} = {expected[i]} → {outputs[i]} {status}")

    # Test generalization on completely unseen cases
    print(f"\n🔍 GENERALIZATION TEST (Unseen Cases):")
    test_cases = [
        ([10, 11, 12], [1, 2, 3], "Large numbers"),
        ([0, 0, 1], [1, 2, 0], "With zeros"),
        ([15, 20], [5, 10], "Very large"),
    ]

    for inputs_a, inputs_b, description in test_cases:
        a_arr = np.array(inputs_a, dtype=np.int64)
        b_arr = np.array(inputs_b, dtype=np.int64)
        expected = a_arr + b_arr

        # Use largest stage parameters
        params = get_stage_params('stage5')
        outputs = _simulate_addition(rule, prog, a_arr, b_arr,
                                   params['window'], params['max_steps'], params['halt_thresh'])

        correct = np.sum((outputs != -10) & (outputs == expected))
        convergent = np.sum(outputs != -10)

        print(f"  {description}: {correct}/{len(expected)} correct, {convergent}/{len(expected)} convergent")
        for i in range(len(inputs_a)):
            status = "✓" if outputs[i] == expected[i] else "✗"
            print(f"    {inputs_a[i]} + {inputs_b[i]} = {expected[i]} → {outputs[i]} {status}")

def plot_training_progress(all_results):
    """Plot training progress across curriculum stages."""
    print("\n📊 Generating training progress plots...")

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

    # Plot 1: Best fitness across stages
    stages = list(all_results.keys())
    best_fitnesses = [all_results[stage]['best_fitness'] for stage in stages]

    ax1.bar(stages, best_fitnesses, color='skyblue', alpha=0.7)
    ax1.set_title('Best Fitness by Curriculum Stage')
    ax1.set_ylabel('Fitness')
    ax1.tick_params(axis='x', rotation=45)

    # Plot 2: Fitness evolution within each stage
    for stage in stages:
        fitness_hist = all_results[stage]['fitness_history']
        bests = [h['best'] for h in fitness_hist]
        ax2.plot(bests, label=stage, alpha=0.7)

    ax2.set_title('Fitness Evolution During Training')
    ax2.set_xlabel('Generation')
    ax2.set_ylabel('Best Fitness')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # Plot 3: Generalization scores
    for stage in stages:
        gen_hist = all_results[stage]['generalization_history']
        if gen_hist:
            ax3.plot(range(0, len(gen_hist)*25, 25), gen_hist, 'o-', label=stage, alpha=0.7)

    ax3.set_title('Generalization Performance')
    ax3.set_xlabel('Generation')
    ax3.set_ylabel('Generalization Score (%)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # Plot 4: Model complexity across stages
    prog_lengths = [all_results[stage]['params']['prog_length'] for stage in stages]
    pop_sizes = [all_results[stage]['params']['pop_size'] for stage in stages]

    ax4_twin = ax4.twinx()
    bars1 = ax4.bar([i-0.2 for i in range(len(stages))], prog_lengths, 0.4,
                    label='Program Length', color='lightcoral', alpha=0.7)
    bars2 = ax4_twin.bar([i+0.2 for i in range(len(stages))], pop_sizes, 0.4,
                         label='Population Size', color='lightgreen', alpha=0.7)

    ax4.set_title('Model Complexity by Stage')
    ax4.set_xlabel('Curriculum Stage')
    ax4.set_ylabel('Program Length', color='red')
    ax4_twin.set_ylabel('Population Size', color='green')
    ax4.set_xticks(range(len(stages)))
    ax4.set_xticklabels(stages, rotation=45)

    plt.tight_layout()
    plt.savefig('addition_training_progress.png', dpi=150, bbox_inches='tight')
    print("  Saved: addition_training_progress.png")
    plt.show()

if __name__ == "__main__":
    start_time = time.time()

    # Run curriculum training
    best_genome, best_fitness, all_results = curriculum_training()

    # Comprehensive testing
    comprehensive_test(best_genome, all_results)

    # Plot results
    plot_training_progress(all_results)

    end_time = time.time()

    print(f"\n" + "=" * 80)
    print("🎉 TRAINING COMPLETE!")
    print("=" * 80)
    print(f"Total training time: {(end_time - start_time)/60:.1f} minutes")
    print(f"Overall best fitness: {best_fitness:.2f}")

    if best_fitness > 50:
        print("🎯 SUCCESS: Advanced addition model trained successfully!")
        print("✅ The model demonstrates curriculum learning and generalization!")
    else:
        print("⚠️  Training completed but may need further optimization.")
        print("💡 Consider adjusting parameters or extending curriculum.")

    # Save final model
    final_model = {
        'best_genome': best_genome,
        'best_fitness': best_fitness,
        'all_results': all_results,
        'training_time': end_time - start_time
    }

    with open('final_addition_model.pkl', 'wb') as f:
        pickle.dump(final_model, f)
    print("💾 Final model saved: final_addition_model.pkl")
