"""
Curriculum Learning for Nonlocal EM43

Progressive training approach:
Stage 1: 1+1=2 (foundation)
Stage 2: 1+1, 1+2, 2+1 (basic cases)
Stage 3: Add 2+2=4 (equal inputs)
Stage 4: Add 1+3, 3+1 (larger range)
Stage 5: Add 2+3, 3+2 (full basic set)
"""

import numpy as np
import time
import pickle
from tqdm import tqdm
from em43_numba import _sanitize_programme, _sanitize_rule

def create_hybrid_nonlocal_rule(base_rule):
    """Create nonlocal rule starting from a working standard rule."""
    nonlocal_rule = np.zeros(256, dtype=np.uint8)
    
    # Copy base rule to all 4 rule types as starting point
    for i in range(4):
        nonlocal_rule[i*64:(i+1)*64] = base_rule.copy()
    
    return nonlocal_rule

def simulate_hybrid_addition(rule, prog, a, b, max_steps=150):
    """Hybrid simulation with improved stability."""
    L = len(prog)
    N = 40  # Larger window for more complex cases
    
    state = np.zeros(N, dtype=np.uint8)
    
    # Standard EM43 encoding: [prog] BB 0^a R 0^b R
    for j in range(min(L, N)):
        state[j] = prog[j]
    
    if L + 1 < N:
        state[L] = 3      # B
    if L + 2 < N:
        state[L + 1] = 3  # B
    
    base_pos = L + 2
    
    # Write first input
    for i in range(a):
        pos = base_pos + i
        if pos < N:
            state[pos] = 0
    
    # First R marker
    r1_pos = base_pos + a
    if r1_pos < N:
        state[r1_pos] = 2
    
    # Write second input
    for i in range(b):
        pos = r1_pos + 1 + i
        if pos < N:
            state[pos] = 0
    
    # Second R marker
    r2_pos = r1_pos + 1 + b
    if r2_pos < N:
        state[r2_pos] = 2
    
    # Extract rule components
    rule_local = rule[0:64]
    rule_skip_left = rule[64:128]
    rule_skip_right = rule[128:192]
    rule_long_range = rule[192:256]
    
    # Run simulation with adaptive nonlocal usage
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(1, N - 1):
            left = state[x-1] if x > 0 else 0
            center = state[x]
            right = state[x+1] if x < N-1 else 0
            local_idx = (left << 4) | (center << 2) | right
            
            # Default: use standard local rule
            nxt[x] = rule_local[local_idx]
            
            # Selective nonlocal rule usage
            if x >= 2 and x < N - 2:
                left2 = state[x-2]
                right2 = state[x+2]
                
                # Use nonlocal rules in specific patterns
                if center != 0:
                    # Non-zero center: try long-range to pass through
                    long_range_idx = (left2 << 4) | (center << 2) | right2
                    nxt[x] = rule_long_range[long_range_idx]
                elif left != 0 and right == 0:
                    # Left blocking: try skip-left
                    skip_left_idx = (left2 << 4) | (center << 2) | right
                    nxt[x] = rule_skip_left[skip_left_idx]
                elif right != 0 and left == 0:
                    # Right blocking: try skip-right
                    skip_right_idx = (left << 4) | (center << 2) | right2
                    nxt[x] = rule_skip_right[skip_right_idx]
        
        # Standard EM43 halting condition
        live = blue = 0
        for x in range(N):
            v = nxt[x]
            if v != 0:
                live += 1
                if v == 3:
                    blue += 1
        
        if live > 0 and blue / live >= 0.5:
            # Find rightmost R
            rpos = -1
            for x in range(N - 1, -1, -1):
                if nxt[x] == 2:
                    rpos = x
                    break
            
            if rpos != -1:
                # Count zeros after rightmost R
                zero_count = 0
                for x in range(rpos + 1, N):
                    if nxt[x] == 0:
                        zero_count += 1
                    elif nxt[x] != 0:
                        break
                return zero_count
            else:
                return -10
        
        state = nxt
    
    return -10

def evaluate_curriculum(rule, prog, test_cases):
    """Evaluate model on current curriculum stage."""
    results = []
    for a, b in test_cases:
        expected = a + b
        result = simulate_hybrid_addition(rule, prog, a, b)
        correct = (result == expected)
        convergent = (result != -10)
        results.append({
            'a': a, 'b': b, 'expected': expected, 'result': result,
            'correct': correct, 'convergent': convergent
        })
    return results

def train_curriculum_stage(stage_name, test_cases, initial_population=None, generations=100):
    """Train a single curriculum stage."""
    print(f"\n🎓 CURRICULUM STAGE: {stage_name}")
    print(f"Cases: {test_cases}")
    print(f"Target: {len(test_cases)} cases with 90%+ accuracy")
    
    POP_SIZE = 40
    MUTATION_RATE = 0.04
    
    # Initialize population
    if initial_population is None:
        population = []
        rng = np.random.default_rng()
        
        for i in range(POP_SIZE):
            # Create base rule with good patterns
            base_rule = np.zeros(64, dtype=np.uint8)
            
            # Essential patterns
            base_rule[0] = 0   # 000 -> 0
            base_rule[2] = 2   # 002 -> 2 (preserve R)
            base_rule[8] = 2   # 020 -> 2 (preserve R)
            base_rule[32] = 0  # 200 -> 0
            base_rule[12] = 3  # 030 -> 3 (generate blue)
            base_rule[48] = 3  # 300 -> 3 (preserve blue)
            base_rule[4] = 1   # 010 -> 1 (propagate program)
            base_rule[1] = 0   # 001 -> 0
            base_rule[16] = 0  # 100 -> 0
            
            # Add controlled variation
            for j in range(64):
                if rng.random() < 0.15:
                    base_rule[j] = rng.integers(0, 4)
            
            base_rule = _sanitize_rule(base_rule)
            rule = create_hybrid_nonlocal_rule(base_rule)
            
            # Program
            prog = rng.choice([0, 1], size=8, p=[0.6, 0.4])
            prog = _sanitize_programme(prog)
            
            population.append((rule, prog))
    else:
        population = initial_population.copy()
        # Add some new diversity
        rng = np.random.default_rng()
        for i in range(len(population) // 4):  # Replace 25% with new individuals
            rule, prog = population[i]
            
            # Mutate existing good solutions
            new_rule = rule.copy()
            new_prog = prog.copy()
            
            # Light mutation
            for j in range(256):
                if rng.random() < 0.02:
                    new_rule[j] = rng.integers(0, 4)
            
            # Re-sanitize base part
            base_part = new_rule[0:64]
            base_part = _sanitize_rule(base_part)
            new_rule[0:64] = base_part
            
            for j in range(len(prog)):
                if rng.random() < 0.05:
                    new_prog[j] = rng.choice([0, 1], p=[0.6, 0.4])
            
            new_prog = _sanitize_programme(new_prog)
            population[i] = (new_rule, new_prog)
    
    best_accuracy = 0
    best_model = None
    rng = np.random.default_rng()
    
    for gen in range(generations):
        fitness_scores = []
        
        for rule, prog in population:
            results = evaluate_curriculum(rule, prog, test_cases)
            
            correct = sum(1 for r in results if r['correct'])
            convergent = sum(1 for r in results if r['convergent'])
            
            accuracy = correct / len(test_cases)
            convergence_rate = convergent / len(test_cases)
            
            # Fitness function with curriculum bonuses
            fitness = correct * 1000 + convergent * 100
            
            # Bonus for high accuracy
            if accuracy >= 0.9:
                fitness += 2000
            elif accuracy >= 0.7:
                fitness += 1000
            elif accuracy >= 0.5:
                fitness += 500
            
            # Bonus for getting close to right answers
            for r in results:
                if r['convergent'] and r['result'] != -10:
                    error = abs(r['result'] - r['expected'])
                    if error == 0:
                        fitness += 300  # Perfect
                    elif error <= 1:
                        fitness += 150  # Very close
                    elif error <= 2:
                        fitness += 75   # Close
            
            fitness_scores.append(fitness)
        
        # Track best
        best_idx = np.argmax(fitness_scores)
        gen_results = evaluate_curriculum(population[best_idx][0], population[best_idx][1], test_cases)
        gen_accuracy = sum(1 for r in gen_results if r['correct']) / len(test_cases)
        gen_convergence = sum(1 for r in gen_results if r['convergent']) / len(test_cases)
        
        if gen_accuracy > best_accuracy:
            best_accuracy = gen_accuracy
            best_model = population[best_idx]
        
        # Progress report
        if gen % 25 == 0 or gen_accuracy >= 0.9:
            print(f"  Gen {gen:3d}: acc={gen_accuracy:.1%}, conv={gen_convergence:.1%}, best={best_accuracy:.1%}")
            
            if gen_accuracy > 0:
                failing = [r for r in gen_results if not r['correct']]
                if failing:
                    print(f"    Failing: {[(r['a'], r['b'], r['result']) for r in failing[:3]]}")
        
        # Early stopping for this stage
        if best_accuracy >= 0.9:
            print(f"  🎯 Stage target reached at generation {gen}!")
            break
        
        # Reproduction
        if gen < generations - 1:
            sorted_indices = np.argsort(fitness_scores)[::-1]
            n_elite = POP_SIZE // 4
            
            new_population = []
            
            # Keep elites
            for i in range(n_elite):
                new_population.append(population[sorted_indices[i]])
            
            # Generate offspring
            while len(new_population) < POP_SIZE:
                parent_idx = rng.choice(sorted_indices[:POP_SIZE//2])
                rule, prog = population[parent_idx]
                
                # Mutate
                new_rule = rule.copy()
                new_prog = prog.copy()
                
                # Adaptive mutation rate
                current_mutation = MUTATION_RATE
                if gen > 50 and best_accuracy < 0.5:
                    current_mutation *= 2  # Increase exploration if stuck
                
                for i in range(256):
                    if rng.random() < current_mutation:
                        new_rule[i] = rng.integers(0, 4)
                
                # Re-sanitize base part
                base_part = new_rule[0:64]
                base_part = _sanitize_rule(base_part)
                new_rule[0:64] = base_part
                
                for i in range(len(prog)):
                    if rng.random() < current_mutation * 2:
                        new_prog[i] = rng.choice([0, 1], p=[0.6, 0.4])
                
                new_prog = _sanitize_programme(new_prog)
                new_population.append((new_rule, new_prog))
            
            population = new_population
    
    return best_model, best_accuracy, population

def curriculum_training():
    """Full curriculum training pipeline."""
    print("🎓 CURRICULUM NONLOCAL TRAINING")
    print("=" * 60)
    print("Progressive learning: Start simple, add complexity gradually")
    
    # Define curriculum stages
    curriculum_stages = [
        ("Stage 1: Foundation", [(1, 1)]),
        ("Stage 2: Basic Cases", [(1, 1), (1, 2), (2, 1)]),
        ("Stage 3: Equal Inputs", [(1, 1), (1, 2), (2, 1), (2, 2)]),
        ("Stage 4: Larger Range", [(1, 1), (1, 2), (2, 1), (2, 2), (1, 3), (3, 1)]),
        ("Stage 5: Full Basic Set", [(1, 1), (1, 2), (2, 1), (2, 2), (1, 3), (3, 1), (2, 3), (3, 2)]),
    ]
    
    population = None
    stage_results = {}
    
    for stage_name, test_cases in curriculum_stages:
        start_time = time.time()
        
        best_model, accuracy, population = train_curriculum_stage(
            stage_name, test_cases, population, generations=150
        )
        
        stage_time = time.time() - start_time
        stage_results[stage_name] = {
            'accuracy': accuracy,
            'test_cases': test_cases,
            'time': stage_time,
            'model': best_model
        }
        
        print(f"  ✅ {stage_name} completed: {accuracy:.1%} accuracy in {stage_time:.1f}s")
        
        # Show detailed results
        if best_model:
            rule, prog = best_model
            results = evaluate_curriculum(rule, prog, test_cases)
            
            print(f"  📊 Results:")
            for r in results:
                status = "✓" if r['correct'] else "✗"
                conv = "C" if r['convergent'] else "NC"
                print(f"    {r['a']}+{r['b']}={r['expected']} → {r['result']} {status} {conv}")
        
        # Stop if stage failed badly
        if accuracy < 0.3:
            print(f"  ⚠️  Stage failed, stopping curriculum")
            break
        
        print()
    
    return stage_results

if __name__ == "__main__":
    start_time = time.time()
    
    print("🧮 CURRICULUM NONLOCAL EM43 TRAINING")
    print("Combining curriculum learning with nonlocal rules")
    print("=" * 60)
    
    # Run curriculum training
    results = curriculum_training()
    
    total_time = time.time() - start_time
    
    print(f"\n🎯 CURRICULUM TRAINING COMPLETE!")
    print(f"Total time: {total_time/60:.1f} minutes")
    
    # Summary
    print(f"\n📈 CURRICULUM SUMMARY:")
    for stage_name, data in results.items():
        print(f"  {stage_name}: {data['accuracy']:.1%} on {len(data['test_cases'])} cases ({data['time']:.1f}s)")
    
    # Save best final model
    if results:
        final_stage = list(results.keys())[-1]
        final_data = results[final_stage]
        
        if final_data['model'] and final_data['accuracy'] > 0.5:
            rule, prog = final_data['model']
            
            model_data = {
                'rule': rule,
                'prog': prog,
                'accuracy': final_data['accuracy'],
                'type': 'curriculum_nonlocal',
                'test_cases': final_data['test_cases'],
                'curriculum_results': results
            }
            
            with open('curriculum_nonlocal_model.pkl', 'wb') as f:
                pickle.dump(model_data, f)
            
            print(f"\n💾 Final model saved: curriculum_nonlocal_model.pkl")
            print(f"   Final accuracy: {final_data['accuracy']:.1%} on {len(final_data['test_cases'])} cases")
    
    print(f"\n🎉 Curriculum learning demonstrates progressive improvement!")
    print(f"   Nonlocal rules enable training on increasingly complex addition problems")
