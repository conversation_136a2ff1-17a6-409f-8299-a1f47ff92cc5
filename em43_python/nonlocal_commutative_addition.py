"""
Nonlocal EM43 with Commutative Addition Training

Combines nonlocal rules with commutative positional encoding:
- Two inputs can overlap in the same position when a=b
- Order doesn't matter due to commutativity of addition
- Uses nonlocal rules to handle information propagation efficiently
"""

import numpy as np
import time
import pickle
from tqdm import tqdm
from typing import Tuple, List
from em43_numba import _sanitize_programme

def _sanitize_nonlocal_rule(rule):
    """Sanitize nonlocal rule array (4 x 64 = 256 entries total)."""
    rule = rule.copy()
    rule = rule.reshape(4, 64)
    
    # Each sub-rule follows standard EM43 constraints
    for i in range(4):
        rule[i] = rule[i] & 0x03  # Ensure values are 0-3
        
        # Apply immutable constraints for each sub-rule
        immutable_patterns = [
            ((0<<4)|(0<<2)|0, 0),      # 000 -> 0
            ((0<<4)|(2<<2)|0, 2),      # 020 -> 2 (R preservation)
            ((0<<4)|(0<<2)|2, 0),      # 002 -> 0
            ((2<<4)|(0<<2)|0, 0),      # 200 -> 0
            ((0<<4)|(3<<2)|3, 3),      # 033 -> 3 (B preservation)
            ((3<<4)|(3<<2)|0, 3),      # 330 -> 3
            ((0<<4)|(0<<2)|3, 0),      # 003 -> 0
            ((3<<4)|(0<<2)|0, 0),      # 300 -> 0
        ]
        
        for pattern, value in immutable_patterns:
            rule[i][pattern] = value
    
    return rule.flatten()

def simulate_commutative_addition(rule, prog, a, b, window=500, max_steps=1000, halt_thresh=0.5):
    """
    Commutative addition simulation with nonlocal rules.
    
    Encoding: [program] BB 0^a R 0^b R (overlapping positions for commutative inputs)
    When a=b, they occupy the same cells, representing the overlap naturally.
    """
    L = len(prog)
    N = window
    
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(L):
        state[j] = prog[j]
    state[L] = 3      # B
    state[L + 1] = 3  # B
    
    # Commutative encoding: inputs can overlap
    # Base position for first input
    base_pos = L + 2
    
    # Encode first input: 0^a
    for i in range(a):
        if base_pos + i < N:
            state[base_pos + i] = 0  # Explicitly set (though already 0)
    
    # First R marker
    r1_pos = base_pos + a
    if r1_pos >= N:
        return -10
    state[r1_pos] = 2
    
    # Encode second input: 0^b (can overlap with first if positions coincide)
    # Start from same base position to allow overlap
    for i in range(b):
        if base_pos + i < N:
            # If this position already has a 0 from first input, it stays 0
            # This naturally handles the a=b case where inputs completely overlap
            state[base_pos + i] = 0
    
    # Second R marker (positioned after the longer of the two inputs)
    r2_pos = base_pos + max(a, b)
    if r2_pos >= N:
        return -10
    state[r2_pos] = 2
    
    # If a != b, we need to handle the non-overlapping part
    # The encoding becomes: 0^min(a,b) 0^|a-b| R
    # But since we want 0^a R 0^b R semantics, we adjust:
    if a != b:
        # Place second R after accounting for both inputs
        r2_pos = r1_pos + 1 + b
        if r2_pos >= N:
            return -10
        state[r2_pos] = 2
        
        # Fill the second input region
        for i in range(b):
            pos = r1_pos + 1 + i
            if pos < r2_pos and pos < N:
                state[pos] = 0
    
    # Adaptive simulation range
    sim_range = min(N - 1, r2_pos + max(a + b + 15, 40))
    
    # Reshape rule for easier access
    rule_local = rule[0:64]
    rule_skip_left = rule[64:128]
    rule_skip_right = rule[128:192]
    rule_long_range = rule[192:256]
    
    # Run CA simulation with nonlocal rules
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(2, sim_range - 2):  # Need buffer for nonlocal access
            # Get local neighborhood
            left, center, right = state[x-1], state[x], state[x+1]
            local_idx = (left << 4) | (center << 2) | right
            
            # Get nonlocal neighborhoods
            left2, right2 = state[x-2], state[x+2]
            skip_left_idx = (left2 << 4) | (center << 2) | right
            skip_right_idx = (left << 4) | (center << 2) | right2
            long_range_idx = (left2 << 4) | (center << 2) | right2
            
            # Apply nonlocal rule selection logic
            if center != 0:
                # Use long-range rule to allow information to pass through
                nxt[x] = rule_long_range[long_range_idx]
            elif left != 0 and right == 0:
                # Left neighbor is blocking, try skip-left rule
                nxt[x] = rule_skip_left[skip_left_idx]
            elif right != 0 and left == 0:
                # Right neighbor is blocking, try skip-right rule
                nxt[x] = rule_skip_right[skip_right_idx]
            else:
                # Use standard local rule
                nxt[x] = rule_local[local_idx]
        
        # Handle boundary cells with standard local rules
        for x in [1, sim_range-1]:
            if x < sim_range:
                left = state[x-1] if x > 0 else 0
                center = state[x]
                right = state[x+1] if x < N-1 else 0
                local_idx = (left << 4) | (center << 2) | right
                nxt[x] = rule_local[local_idx]
        
        # Halting check
        live = blue = 0
        for x in range(sim_range):
            v = nxt[x]
            if v != 0:
                live += 1
                if v == 3:
                    blue += 1
        
        if live > 0 and blue / live >= halt_thresh:
            # Find rightmost R and count zeros after it
            rpos = -1
            for x in range(sim_range - 1, -1, -1):
                if nxt[x] == 2:
                    rpos = x
                    break
            
            if rpos != -1:
                # Count zeros after the rightmost R
                zero_count = 0
                for x in range(rpos + 1, sim_range):
                    if nxt[x] == 0:
                        zero_count += 1
                    elif nxt[x] != 0:
                        break  # Stop at first non-zero
                return zero_count
            else:
                return -10
        
        state = nxt
    
    return -10

def create_random_nonlocal_rule():
    """Create a random nonlocal rule with proper constraints."""
    rule = np.random.randint(0, 4, size=256, dtype=np.uint8)
    return _sanitize_nonlocal_rule(rule)

def evaluate_commutative_model(rule, prog, test_cases):
    """Evaluate commutative model on test cases."""
    results = []
    
    for a, b in test_cases:
        expected = a + b
        result = simulate_commutative_addition(rule, prog, a, b)
        
        status = "✓" if result == expected else "✗"
        convergent = result != -10
        
        results.append({
            'a': a, 'b': b, 'expected': expected, 'result': result,
            'correct': result == expected, 'convergent': convergent, 'status': status
        })
    
    return results

def create_commutative_test_cases():
    """Create test cases that leverage commutativity."""
    cases = []
    
    # Include both (a,b) and (b,a) to test commutativity
    base_cases = [(1, 1), (1, 2), (2, 2), (1, 3), (2, 3), (3, 3)]
    
    for a, b in base_cases:
        cases.append((a, b))
        if a != b:  # Add commutative pair
            cases.append((b, a))
    
    return cases

def train_commutative_nonlocal_addition():
    """Train commutative addition with nonlocal rules."""
    print("🚀 COMMUTATIVE NONLOCAL ADDITION TRAINING")
    print("=" * 60)
    print("Features:")
    print("  ✓ Nonlocal rules for better information propagation")
    print("  ✓ Commutative encoding (a+b = b+a)")
    print("  ✓ Overlapping inputs when a=b")
    
    # Create test cases
    test_cases = create_commutative_test_cases()
    print(f"\nTest cases ({len(test_cases)}): {test_cases}")
    
    # Training parameters
    POP_SIZE = 100
    GENERATIONS = 200
    MUTATION_RATE = 0.04
    
    print(f"\nTraining parameters:")
    print(f"  Population: {POP_SIZE}")
    print(f"  Generations: {GENERATIONS}")
    print(f"  Mutation rate: {MUTATION_RATE}")
    
    # Initialize population
    population = []
    rng = np.random.default_rng()
    
    for i in range(POP_SIZE):
        rule = create_random_nonlocal_rule()
        prog = rng.choice([0, 1, 2], size=12, p=[0.7, 0.2, 0.1])
        prog = _sanitize_programme(prog)
        population.append((rule, prog))
    
    best_accuracy = 0
    best_model = None
    
    print("\nTraining commutative nonlocal model...")
    
    for gen in tqdm(range(GENERATIONS), desc="Training"):
        # Evaluate population
        fitness_scores = []
        
        for rule, prog in population:
            results = evaluate_commutative_model(rule, prog, test_cases)
            
            correct = sum(1 for r in results if r['correct'])
            convergent = sum(1 for r in results if r['convergent'])
            
            accuracy = correct / len(test_cases)
            convergence_rate = convergent / len(test_cases)
            
            # Fitness function with bonuses for commutativity
            fitness = correct * 100 + convergent * 20
            
            # Bonus for handling commutative pairs correctly
            commutative_bonus = 0
            for a, b in [(1, 2), (2, 3), (1, 3)]:
                if a != b:
                    result_ab = None
                    result_ba = None
                    for r in results:
                        if r['a'] == a and r['b'] == b:
                            result_ab = r['result']
                        elif r['a'] == b and r['b'] == a:
                            result_ba = r['result']
                    
                    if result_ab is not None and result_ba is not None:
                        if result_ab == result_ba and result_ab == a + b:
                            commutative_bonus += 50  # Bonus for consistent commutative results
            
            fitness += commutative_bonus
            
            # Perfect accuracy bonus
            if accuracy == 1.0:
                fitness += 1000
            
            fitness_scores.append(fitness)
        
        # Track best
        gen_best_idx = np.argmax(fitness_scores)
        gen_results = evaluate_commutative_model(population[gen_best_idx][0], 
                                               population[gen_best_idx][1], test_cases)
        gen_accuracy = sum(1 for r in gen_results if r['correct']) / len(test_cases)
        
        if gen_accuracy > best_accuracy:
            best_accuracy = gen_accuracy
            best_model = population[gen_best_idx]
        
        # Progress report
        if gen % 25 == 0:
            gen_convergence = sum(1 for r in gen_results if r['convergent']) / len(test_cases)
            print(f"  Gen {gen}: accuracy={gen_accuracy:.2%}, convergence={gen_convergence:.2%}")
            
            if gen_accuracy < 1.0:
                failing = [r for r in gen_results if not r['correct']][:5]
                print(f"    Sample failures: {[(r['a'], r['b'], r['result']) for r in failing]}")
        
        # Early stopping
        if best_accuracy >= 1.0:
            print(f"  🎯 Perfect accuracy reached at generation {gen}!")
            break
        
        # Selection and reproduction
        if gen < GENERATIONS - 1:
            # Elite selection
            sorted_indices = np.argsort(fitness_scores)[::-1]
            n_elite = POP_SIZE // 4
            
            new_population = []
            
            # Keep elites
            for i in range(n_elite):
                new_population.append(population[sorted_indices[i]])
            
            # Generate offspring
            while len(new_population) < POP_SIZE:
                # Tournament selection
                tournament_indices = rng.choice(sorted_indices[:POP_SIZE//2], size=3, replace=False)
                parent_idx = tournament_indices[0]
                
                rule, prog = population[parent_idx]
                
                # Mutate
                new_rule = rule.copy()
                new_prog = prog.copy()
                
                # Mutate rule
                mask = rng.random(256) < MUTATION_RATE
                new_rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)
                new_rule = _sanitize_nonlocal_rule(new_rule)
                
                # Mutate program
                mask = rng.random(len(prog)) < MUTATION_RATE * 2
                new_prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.7, 0.2, 0.1])
                new_prog = _sanitize_programme(new_prog)
                
                new_population.append((new_rule, new_prog))
            
            population = new_population
    
    return best_model, best_accuracy, test_cases

if __name__ == "__main__":
    start_time = time.time()
    
    print("🧮 COMMUTATIVE NONLOCAL EM43 ADDITION")
    print("Combining nonlocal rules with commutative encoding")
    print("=" * 60)
    
    # Train model
    best_model, final_accuracy, test_cases = train_commutative_nonlocal_addition()
    
    print(f"\n🎯 TRAINING COMPLETE!")
    print(f"Final accuracy: {final_accuracy:.2%}")
    
    if best_model:
        rule, prog = best_model
        
        # Detailed analysis
        print("\n📊 DETAILED ANALYSIS")
        results = evaluate_commutative_model(rule, prog, test_cases)
        
        correct = sum(1 for r in results if r['correct'])
        convergent = sum(1 for r in results if r['convergent'])
        
        print(f"Accuracy: {correct}/{len(test_cases)} ({correct/len(test_cases)*100:.1f}%)")
        print(f"Convergence: {convergent}/{len(test_cases)} ({convergent/len(test_cases)*100:.1f}%)")
        
        print(f"\nResults:")
        for r in results:
            print(f"  {r['a']}+{r['b']}={r['expected']} → {r['result']} {r['status']}")
        
        # Test commutativity specifically
        print(f"\n🔄 COMMUTATIVITY TEST:")
        commutative_pairs = [(1, 2), (2, 3), (1, 3)]
        for a, b in commutative_pairs:
            result_ab = next((r['result'] for r in results if r['a'] == a and r['b'] == b), None)
            result_ba = next((r['result'] for r in results if r['a'] == b and r['b'] == a), None)
            
            if result_ab is not None and result_ba is not None:
                commutative_ok = result_ab == result_ba == a + b
                status = "✓" if commutative_ok else "✗"
                print(f"  {a}+{b}={result_ab}, {b}+{a}={result_ba} {status}")
        
        # Save model
        model_data = {
            'rule': rule,
            'prog': prog,
            'accuracy': final_accuracy,
            'type': 'commutative_nonlocal',
            'test_cases': test_cases,
            'features': ['nonlocal_rules', 'commutative_encoding']
        }
        
        with open('commutative_nonlocal_addition.pkl', 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"\n💾 Model saved: commutative_nonlocal_addition.pkl")
    
    end_time = time.time()
    print(f"\n⏱️  Training time: {(end_time - start_time)/60:.1f} minutes")
    
    if final_accuracy >= 1.0:
        print("\n🎉 SUCCESS: Perfect commutative nonlocal addition achieved!")
        print("🔬 This demonstrates both nonlocal rules and commutative encoding work together")
    elif final_accuracy >= 0.8:
        print("\n✅ STRONG: Very promising results for the combined approach")
    else:
        print("\n⚠️  LEARNING: More refinement needed, but concept is demonstrated")
