"""
Create Interactive HTML Viewer for Perfect Addition Model

Similar to the divide-by-2 viewer, but for the perfect addition model.
Shows the nonlocal rules in action with step-by-step evolution.
"""

import numpy as np
import pickle
import json
from adaptive_nonlocal_training import AdaptiveEM43

def load_perfect_model():
    """Load the perfect addition model."""
    try:
        with open('perfect_adaptive_model.pkl', 'rb') as f:
            model_data = pickle.load(f)
        
        perfect_model = AdaptiveEM43(
            model_data['rule'],
            model_data['prog'],
            model_data['encoding_params'],
            model_data['decoding_params'],
            model_data['halting_params']
        )
        
        print(f"✅ Loaded perfect model: {model_data['accuracy']:.1%} accuracy")
        return perfect_model, model_data
    
    except FileNotFoundError:
        print("❌ Perfect model not found!")
        return None, None

def simulate_with_detailed_trace(model, a, b, max_steps=50):
    """Simulate addition with detailed trace for visualization."""
    N = 60
    state = model.encode_inputs(a, b, N)
    
    # Store evolution
    evolution = [state.copy()]
    rule_usage = []  # Track which nonlocal rule was used
    
    # Extract rule components
    rule_local = model.rule[0:64]
    rule_skip_left = model.rule[64:128]
    rule_skip_right = model.rule[128:192]
    rule_long_range = model.rule[192:256]
    
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        step_rule_usage = np.zeros(N, dtype=np.uint8)  # 0=local, 1=skip_left, 2=skip_right, 3=long_range
        
        for x in range(1, N - 1):
            left = state[x-1] if x > 0 else 0
            center = state[x]
            right = state[x+1] if x < N-1 else 0
            local_idx = (left << 4) | (center << 2) | right
            
            # Default to local rule
            nxt[x] = rule_local[local_idx]
            step_rule_usage[x] = 0
            
            # Apply nonlocal rules
            if x >= 2 and x < N - 2:
                left2 = state[x-2]
                right2 = state[x+2]
                
                if center != 0:
                    long_range_idx = (left2 << 4) | (center << 2) | right2
                    nxt[x] = rule_long_range[long_range_idx]
                    step_rule_usage[x] = 3
                elif left != 0 and right == 0:
                    skip_left_idx = (left2 << 4) | (center << 2) | right
                    nxt[x] = rule_skip_left[skip_left_idx]
                    step_rule_usage[x] = 1
                elif right != 0 and left == 0:
                    skip_right_idx = (left << 4) | (center << 2) | right2
                    nxt[x] = rule_skip_right[skip_right_idx]
                    step_rule_usage[x] = 2
        
        evolution.append(nxt.copy())
        rule_usage.append(step_rule_usage.copy())
        
        # Check halting
        if model.check_halting(nxt):
            result = model.decode_result(nxt)
            return evolution, rule_usage, result, step + 1
        
        state = nxt
    
    return evolution, rule_usage, -10, max_steps

def create_addition_viewer_html():
    """Create interactive HTML viewer for perfect addition model."""
    
    # Load the perfect model
    model, model_data = load_perfect_model()
    if not model:
        return None
    
    print("Creating interactive addition viewer...")
    
    # Test a variety of cases to show in the viewer
    demo_cases = [
        (1, 1), (1, 2), (2, 1), (2, 2), (1, 3), (3, 1),
        (2, 3), (3, 2), (3, 3), (4, 4), (5, 2), (2, 5),
        (4, 3), (3, 4), (5, 5), (6, 1), (1, 6), (6, 6)
    ]
    
    # Generate simulation data for all demo cases
    simulation_data = {}
    for a, b in demo_cases:
        evolution, rule_usage, result, steps = simulate_with_detailed_trace(model, a, b)
        simulation_data[f"{a}_{b}"] = {
            'evolution': evolution,
            'rule_usage': rule_usage,
            'result': result,
            'steps': steps,
            'expected': a + b
        }
    
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Perfect Addition EM43 - Nonlocal Rules Viewer</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        
        .header p {{
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }}
        
        .controls {{
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }}
        
        .control-group {{
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }}
        
        .control-group label {{
            font-weight: 600;
            color: #495057;
        }}
        
        .input-group {{
            display: flex;
            align-items: center;
            gap: 10px;
        }}
        
        input[type="number"] {{
            width: 60px;
            padding: 8px 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 16px;
            text-align: center;
        }}
        
        input[type="number"]:focus {{
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }}
        
        button {{
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }}
        
        button:hover {{
            transform: translateY(-2px);
        }}
        
        .preset-buttons {{
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }}
        
        .preset-btn {{
            padding: 6px 12px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
        }}
        
        .preset-btn:hover {{
            background: #5a6268;
        }}
        
        .info-panel {{
            padding: 20px 30px;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            margin: 20px 30px;
            border-radius: 8px;
        }}
        
        .simulation-area {{
            padding: 30px;
        }}
        
        .step-controls {{
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }}
        
        .step-slider {{
            flex: 1;
            min-width: 200px;
        }}
        
        input[type="range"] {{
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
        }}
        
        .ca-grid {{
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            overflow-x: auto;
            margin-bottom: 20px;
        }}
        
        .ca-row {{
            display: flex;
            margin-bottom: 8px;
            align-items: center;
        }}
        
        .step-label {{
            width: 80px;
            font-weight: bold;
            color: #495057;
            font-size: 14px;
        }}
        
        .cell {{
            width: 25px;
            height: 25px;
            border: 1px solid #ccc;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 2px;
        }}
        
        .cell-0 {{ background-color: #ffffff; color: #999; }}
        .cell-1 {{ background-color: #343a40; color: white; }}
        .cell-2 {{ background-color: #dc3545; color: white; }}
        .cell-3 {{ background-color: #007bff; color: white; }}
        
        .rule-local {{ border: 3px solid #28a745; }}
        .rule-skip-left {{ border: 3px solid #fd7e14; }}
        .rule-skip-right {{ border: 3px solid #6f42c1; }}
        .rule-long-range {{ border: 3px solid #e83e8c; }}
        
        .legend {{
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }}
        
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        
        .legend-cell {{
            width: 20px;
            height: 20px;
            border: 1px solid #000;
        }}
        
        .result-display {{
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            font-size: 1.5em;
            font-weight: bold;
            margin-top: 20px;
        }}
        
        .result-correct {{
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }}
        
        .result-incorrect {{
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        }}
        
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }}
        
        .stat-card {{
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }}
        
        .stat-value {{
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }}
        
        .stat-label {{
            color: #6c757d;
            margin-top: 5px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏆 Perfect Addition EM43</h1>
            <p>Nonlocal Rules • Adaptive Schemes • 100% Accuracy</p>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>Addition Problem:</label>
                <div class="input-group">
                    <input type="number" id="inputA" value="2" min="1" max="7">
                    <span>+</span>
                    <input type="number" id="inputB" value="3" min="1" max="7">
                    <span>=</span>
                    <span id="expectedResult">5</span>
                </div>
                <button onclick="runSimulation()">▶ Run Simulation</button>
            </div>
            
            <div class="control-group">
                <label>Quick Examples:</label>
                <div class="preset-buttons">
                    <button class="preset-btn" onclick="setInputs(1,1)">1+1</button>
                    <button class="preset-btn" onclick="setInputs(2,3)">2+3</button>
                    <button class="preset-btn" onclick="setInputs(4,4)">4+4</button>
                    <button class="preset-btn" onclick="setInputs(5,2)">5+2</button>
                    <button class="preset-btn" onclick="setInputs(6,6)">6+6</button>
                    <button class="preset-btn" onclick="setInputs(3,4)">3+4</button>
                </div>
            </div>
        </div>
        
        <div class="info-panel">
            <strong>🧬 Model Specifications:</strong><br>
            Encoding: {model.encoding_params}<br>
            Decoding: {model.decoding_params}<br>
            Halting: {model.halting_params}<br>
            Program Length: {len(model.prog)} cells
        </div>
        
        <div class="simulation-area">
            <div class="step-controls">
                <label>Step:</label>
                <div class="step-slider">
                    <input type="range" id="stepSlider" min="0" max="10" value="0" oninput="showStep(this.value)">
                </div>
                <span id="stepInfo">Step 0 / 10</span>
                <button onclick="playAnimation()">▶ Play</button>
                <button onclick="pauseAnimation()">⏸ Pause</button>
            </div>
            
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-cell cell-0"></div>
                    <span>Blank (0)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-cell cell-1"></div>
                    <span>Program (1)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-cell cell-2"></div>
                    <span>Red Marker (2)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-cell cell-3"></div>
                    <span>Blue Marker (3)</span>
                </div>
            </div>
            
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-cell rule-local"></div>
                    <span>Local Rule</span>
                </div>
                <div class="legend-item">
                    <div class="legend-cell rule-skip-left"></div>
                    <span>Skip-Left Rule</span>
                </div>
                <div class="legend-item">
                    <div class="legend-cell rule-skip-right"></div>
                    <span>Skip-Right Rule</span>
                </div>
                <div class="legend-item">
                    <div class="legend-cell rule-long-range"></div>
                    <span>Long-Range Rule</span>
                </div>
            </div>
            
            <div class="ca-grid" id="caGrid">
                <!-- CA evolution will be displayed here -->
            </div>
            
            <div class="result-display" id="resultDisplay">
                Ready to simulate addition
            </div>
            
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-value" id="stepsValue">-</div>
                    <div class="stat-label">Steps to Halt</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="accuracyValue">100%</div>
                    <div class="stat-label">Model Accuracy</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="nonlocalValue">-</div>
                    <div class="stat-label">Nonlocal Rules Used</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simulation data embedded from Python
        const simulationData = {json.dumps(simulation_data, default=str)};
        
        let currentSimulation = null;
        let animationInterval = null;
        
        function setInputs(a, b) {{
            document.getElementById('inputA').value = a;
            document.getElementById('inputB').value = b;
            document.getElementById('expectedResult').textContent = a + b;
        }}
        
        function runSimulation() {{
            const a = parseInt(document.getElementById('inputA').value);
            const b = parseInt(document.getElementById('inputB').value);
            const key = `${{a}}_${{b}}`;
            
            if (simulationData[key]) {{
                currentSimulation = simulationData[key];
                displaySimulation();
            }} else {{
                alert(`Simulation for ${{a}}+${{b}} not available. Try values 1-6.`);
            }}
        }}
        
        function displaySimulation() {{
            if (!currentSimulation) return;
            
            const slider = document.getElementById('stepSlider');
            slider.max = currentSimulation.evolution.length - 1;
            slider.value = currentSimulation.evolution.length - 1; // Show final step
            
            showStep(slider.value);
            updateResult();
            updateStats();
        }}
        
        function showStep(stepIndex) {{
            if (!currentSimulation) return;
            
            stepIndex = parseInt(stepIndex);
            const evolution = currentSimulation.evolution;
            const ruleUsage = currentSimulation.rule_usage;
            
            document.getElementById('stepInfo').textContent = `Step ${{stepIndex}} / ${{evolution.length - 1}}`;
            
            const grid = document.getElementById('caGrid');
            grid.innerHTML = '';
            
            // Show current step
            const state = evolution[stepIndex];
            const rules = stepIndex > 0 ? ruleUsage[stepIndex - 1] : new Array(state.length).fill(0);
            
            const row = document.createElement('div');
            row.className = 'ca-row';
            
            const label = document.createElement('div');
            label.className = 'step-label';
            label.textContent = `Step ${{stepIndex}}:`;
            row.appendChild(label);
            
            for (let i = 0; i < Math.min(state.length, 30); i++) {{
                const cell = document.createElement('div');
                cell.className = `cell cell-${{state[i]}}`;
                
                // Add rule type border
                if (stepIndex > 0 && rules[i] !== undefined) {{
                    const ruleTypes = ['rule-local', 'rule-skip-left', 'rule-skip-right', 'rule-long-range'];
                    cell.classList.add(ruleTypes[rules[i]]);
                }}
                
                cell.textContent = state[i] === 0 ? '' : state[i];
                row.appendChild(cell);
            }}
            
            grid.appendChild(row);
        }}
        
        function updateResult() {{
            if (!currentSimulation) return;
            
            const resultDiv = document.getElementById('resultDisplay');
            const result = currentSimulation.result;
            const expected = currentSimulation.expected;
            const correct = result === expected;
            
            resultDiv.className = `result-display ${{correct ? 'result-correct' : 'result-incorrect'}}`;
            resultDiv.innerHTML = `
                Result: ${{result}} (Expected: ${{expected}}) ${{correct ? '✅' : '❌'}}<br>
                <small>${{correct ? 'Perfect Accuracy!' : 'Error Detected'}}</small>
            `;
        }}
        
        function updateStats() {{
            if (!currentSimulation) return;
            
            document.getElementById('stepsValue').textContent = currentSimulation.steps;
            
            // Count nonlocal rule usage
            let nonlocalCount = 0;
            for (const ruleStep of currentSimulation.rule_usage) {{
                for (const rule of ruleStep) {{
                    if (rule > 0) nonlocalCount++;
                }}
            }}
            document.getElementById('nonlocalValue').textContent = nonlocalCount;
        }}
        
        function playAnimation() {{
            if (!currentSimulation) return;
            
            pauseAnimation();
            
            let step = 0;
            const maxStep = currentSimulation.evolution.length - 1;
            
            animationInterval = setInterval(() => {{
                document.getElementById('stepSlider').value = step;
                showStep(step);
                step++;
                
                if (step > maxStep) {{
                    pauseAnimation();
                }}
            }}, 800);
        }}
        
        function pauseAnimation() {{
            if (animationInterval) {{
                clearInterval(animationInterval);
                animationInterval = null;
            }}
        }}
        
        // Initialize with default simulation
        window.onload = function() {{
            setInputs(2, 3);
            runSimulation();
        }};
        
        // Update expected result when inputs change
        document.getElementById('inputA').addEventListener('input', function() {{
            const a = parseInt(this.value) || 0;
            const b = parseInt(document.getElementById('inputB').value) || 0;
            document.getElementById('expectedResult').textContent = a + b;
        }});
        
        document.getElementById('inputB').addEventListener('input', function() {{
            const a = parseInt(document.getElementById('inputA').value) || 0;
            const b = parseInt(this.value) || 0;
            document.getElementById('expectedResult').textContent = a + b;
        }});
    </script>
</body>
</html>
"""
    
    # Save the HTML file
    filename = 'perfect_addition_viewer.html'
    with open(filename, 'w') as f:
        f.write(html_content)
    
    print(f"✅ Interactive viewer created: {filename}")
    return filename

if __name__ == "__main__":
    print("🎨 CREATING PERFECT ADDITION VIEWER")
    print("=" * 50)
    
    filename = create_addition_viewer_html()
    
    if filename:
        print(f"\n🎉 Perfect Addition Viewer created successfully!")
        print(f"📁 File: {filename}")
        print(f"🌐 Features:")
        print(f"   • Interactive input controls (1-7 range)")
        print(f"   • Step-by-step evolution display")
        print(f"   • Nonlocal rule visualization")
        print(f"   • Animation playback")
        print(f"   • Perfect accuracy demonstration")
        print(f"   • Real-time statistics")
        
        print(f"\n🚀 Ready to explore the perfect addition model!")
    else:
        print(f"\n❌ Failed to create viewer - perfect model not found")
