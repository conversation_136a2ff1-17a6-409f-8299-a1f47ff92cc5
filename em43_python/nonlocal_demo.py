"""
Nonlocal EM43 Demonstration

A simple demonstration of the nonlocal rules concept without full training.
Shows how information can propagate through blocking cells using extended neighborhoods.
"""

import numpy as np
from em43_numba import _sanitize_programme

def create_demo_nonlocal_rule():
    """Create a demonstration nonlocal rule that shows the concept."""
    # Create a rule that demonstrates nonlocal behavior
    rule = np.zeros(256, dtype=np.uint8)
    
    # Standard local rules (first 64 entries)
    local_rule = rule[0:64]
    
    # Some basic local patterns
    local_rule[0] = 0  # 000 -> 0
    local_rule[1] = 0  # 001 -> 0  
    local_rule[2] = 2  # 002 -> 2 (preserve R)
    local_rule[4] = 1  # 010 -> 1 (program propagation)
    local_rule[8] = 2  # 020 -> 2 (preserve R)
    local_rule[16] = 0  # 100 -> 0
    local_rule[32] = 0  # 200 -> 0
    
    # Skip-left rules (entries 64-127) - can see past left neighbor
    skip_left_rule = rule[64:128]
    skip_left_rule[0] = 0   # 000 -> 0
    skip_left_rule[1] = 0   # 001 -> 0
    skip_left_rule[2] = 2   # 002 -> 2
    skip_left_rule[4] = 1   # 010 -> 1 (can propagate past blocking left cell)
    skip_left_rule[8] = 2   # 020 -> 2
    
    # Skip-right rules (entries 128-191) - can see past right neighbor  
    skip_right_rule = rule[128:192]
    skip_right_rule[0] = 0   # 000 -> 0
    skip_right_rule[1] = 1   # 001 -> 1 (can propagate past blocking right cell)
    skip_right_rule[2] = 2   # 002 -> 2
    skip_right_rule[4] = 1   # 010 -> 1
    skip_right_rule[8] = 2   # 020 -> 2
    
    # Long-range rules (entries 192-255) - can see past both neighbors
    long_range_rule = rule[192:256]
    long_range_rule[0] = 0   # 000 -> 0
    long_range_rule[1] = 1   # 001 -> 1 (long-range propagation)
    long_range_rule[2] = 2   # 002 -> 2
    long_range_rule[4] = 1   # 010 -> 1 (pass through center cell)
    long_range_rule[8] = 2   # 020 -> 2
    
    return rule

def simulate_step_by_step(rule, initial_state, max_steps=10):
    """Simulate step by step and return evolution."""
    N = len(initial_state)
    state = initial_state.copy()
    evolution = [state.copy()]
    
    # Reshape rule for easier access
    rule_local = rule[0:64]
    rule_skip_left = rule[64:128]
    rule_skip_right = rule[128:192]
    rule_long_range = rule[192:256]
    
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        rule_used = np.zeros(N, dtype=np.uint8)  # Track which rule was used
        
        for x in range(2, N - 2):
            # Get neighborhoods
            left, center, right = state[x-1], state[x], state[x+1]
            left2, right2 = state[x-2], state[x+2]
            
            local_idx = (left << 4) | (center << 2) | right
            skip_left_idx = (left2 << 4) | (center << 2) | right
            skip_right_idx = (left << 4) | (center << 2) | right2
            long_range_idx = (left2 << 4) | (center << 2) | right2
            
            # Rule selection logic - demonstrate nonlocal behavior
            if center != 0:
                # Center cell is non-zero, use long-range to pass through
                nxt[x] = rule_long_range[long_range_idx]
                rule_used[x] = 3
            elif left != 0 and right == 0:
                # Left neighbor blocking, skip it
                nxt[x] = rule_skip_left[skip_left_idx]
                rule_used[x] = 1
            elif right != 0 and left == 0:
                # Right neighbor blocking, skip it
                nxt[x] = rule_skip_right[skip_right_idx]
                rule_used[x] = 2
            else:
                # Use standard local rule
                nxt[x] = rule_local[local_idx]
                rule_used[x] = 0
        
        # Handle boundaries with local rules
        for x in [1, N-2]:
            if 0 < x < N-1:
                left = state[x-1] if x > 0 else 0
                center = state[x]
                right = state[x+1] if x < N-1 else 0
                local_idx = (left << 4) | (center << 2) | right
                nxt[x] = rule_local[local_idx]
                rule_used[x] = 0
        
        evolution.append((nxt.copy(), rule_used.copy()))
        state = nxt
        
        # Stop if no changes
        if np.array_equal(state, evolution[-2][0] if len(evolution) > 1 else initial_state):
            break
    
    return evolution

def create_demo_scenarios():
    """Create demonstration scenarios showing nonlocal behavior."""
    scenarios = []
    
    # Scenario 1: Information blocked by non-zero cell
    state1 = np.array([0, 1, 0, 2, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], dtype=np.uint8)
    scenarios.append(("Blocked propagation", state1))
    
    # Scenario 2: Information needs to skip past blocking cells
    state2 = np.array([0, 1, 0, 3, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], dtype=np.uint8)
    scenarios.append(("Skip through blocker", state2))
    
    # Scenario 3: Long-range interaction needed
    state3 = np.array([0, 1, 0, 2, 0, 3, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0], dtype=np.uint8)
    scenarios.append(("Long-range needed", state3))
    
    return scenarios

def print_evolution(name, evolution):
    """Print the evolution in a readable format."""
    print(f"\n{name}:")
    print("=" * 50)
    
    # Print initial state
    initial_state = evolution[0]
    print(f"Step 0: {' '.join(str(x) if x != 0 else '.' for x in initial_state)}")
    
    # Print subsequent steps
    for i, (state, rule_used) in enumerate(evolution[1:], 1):
        state_str = ' '.join(str(x) if x != 0 else '.' for x in state)
        rule_str = ' '.join(['L', 'S', 'R', 'G'][r] for r in rule_used)  # Local, Skip-left, skip-Right, lonG-range
        print(f"Step {i}: {state_str}")
        print(f"Rules:  {rule_str}")
        print()

def demonstrate_nonlocal_concept():
    """Demonstrate the nonlocal rules concept."""
    print("🔬 NONLOCAL EM43 CONCEPT DEMONSTRATION")
    print("=" * 60)
    print("Showing how nonlocal rules can address information propagation limitations")
    print()
    print("Legend:")
    print("  0 = Blank (shown as .)")
    print("  1 = Program cell")
    print("  2 = Red marker (R)")
    print("  3 = Blue marker (B)")
    print()
    print("Rule types:")
    print("  L = Local (standard radius-1)")
    print("  S = Skip-left (can see past left neighbor)")
    print("  R = Skip-right (can see past right neighbor)")
    print("  G = Long-range (can see past both neighbors)")
    
    # Create demo rule
    rule = create_demo_nonlocal_rule()
    
    # Run scenarios
    scenarios = create_demo_scenarios()
    
    for name, initial_state in scenarios:
        evolution = simulate_step_by_step(rule, initial_state, max_steps=5)
        print_evolution(name, evolution)
    
    print("\n🎯 KEY INSIGHTS:")
    print("1. Standard EM43 (radius-1) can get 'blocked' by non-zero cells")
    print("2. Nonlocal rules allow information to 'skip past' blocking cells")
    print("3. This enables better information propagation in complex computations")
    print("4. The combinatorial space remains manageable (4 rule types × 64 patterns = 256 total)")
    print("5. Rule selection can be adaptive based on local neighborhood patterns")

def create_simple_html_demo():
    """Create a simple HTML demonstration."""
    rule = create_demo_nonlocal_rule()
    scenarios = create_demo_scenarios()
    
    html = """
<!DOCTYPE html>
<html>
<head>
    <title>Nonlocal EM43 Concept Demo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .scenario { margin: 20px 0; border: 1px solid #ccc; padding: 15px; }
        .step { margin: 10px 0; }
        .grid { display: grid; grid-template-columns: repeat(16, 25px); gap: 2px; margin: 5px 0; }
        .cell { width: 25px; height: 25px; border: 1px solid #999; text-align: center; line-height: 25px; }
        .cell-0 { background-color: #ffffff; }
        .cell-1 { background-color: #000000; color: white; }
        .cell-2 { background-color: #ff2222; color: white; }
        .cell-3 { background-color: #1665c1; color: white; }
        .rule-L { border: 2px solid #00ff00; }
        .rule-S { border: 2px solid #ff8800; }
        .rule-R { border: 2px solid #8800ff; }
        .rule-G { border: 2px solid #ff0088; }
    </style>
</head>
<body>
    <h1>Nonlocal EM43 Concept Demonstration</h1>
    <p>This demonstrates how nonlocal rules can help information propagate past blocking cells.</p>
    
    <div style="margin: 20px 0;">
        <h3>Legend:</h3>
        <div style="display: flex; gap: 20px;">
            <div><div class="cell cell-0" style="display: inline-block;">0</div> Blank</div>
            <div><div class="cell cell-1" style="display: inline-block;">1</div> Program</div>
            <div><div class="cell cell-2" style="display: inline-block;">2</div> Red marker</div>
            <div><div class="cell cell-3" style="display: inline-block;">3</div> Blue marker</div>
        </div>
        <h3>Rule Types:</h3>
        <div style="display: flex; gap: 20px;">
            <div><div class="cell rule-L" style="display: inline-block;">L</div> Local</div>
            <div><div class="cell rule-S" style="display: inline-block;">S</div> Skip-left</div>
            <div><div class="cell rule-R" style="display: inline-block;">R</div> Skip-right</div>
            <div><div class="cell rule-G" style="display: inline-block;">G</div> Long-range</div>
        </div>
    </div>
"""
    
    for name, initial_state in scenarios:
        evolution = simulate_step_by_step(rule, initial_state, max_steps=3)
        
        html += f'    <div class="scenario"><h3>{name}</h3>\n'
        
        # Initial state
        html += '        <div class="step">Step 0:<div class="grid">'
        for cell in initial_state:
            html += f'<div class="cell cell-{cell}">{cell if cell != 0 else ""}</div>'
        html += '</div></div>\n'
        
        # Evolution steps
        for i, (state, rule_used) in enumerate(evolution[1:], 1):
            html += f'        <div class="step">Step {i}:<div class="grid">'
            for j, cell in enumerate(state):
                rule_class = f"rule-{['L', 'S', 'R', 'G'][rule_used[j]]}"
                html += f'<div class="cell cell-{cell} {rule_class}">{cell if cell != 0 else ""}</div>'
            html += '</div></div>\n'
        
        html += '    </div>\n'
    
    html += """
    <div style="margin-top: 30px;">
        <h3>Key Insights:</h3>
        <ul>
            <li>Standard EM43 (radius-1) can get 'blocked' by non-zero cells</li>
            <li>Nonlocal rules allow information to 'skip past' blocking cells</li>
            <li>This enables better information propagation in complex computations</li>
            <li>The combinatorial space remains manageable (256 total rules vs 64 standard)</li>
        </ul>
    </div>
</body>
</html>
"""
    
    with open('nonlocal_demo.html', 'w') as f:
        f.write(html)
    
    print("📄 HTML demo saved as 'nonlocal_demo.html'")

if __name__ == "__main__":
    demonstrate_nonlocal_concept()
    create_simple_html_demo()
    print("\n🎉 Nonlocal concept demonstration complete!")
    print("Open 'nonlocal_demo.html' in a browser to see the visual demonstration.")
