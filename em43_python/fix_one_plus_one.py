"""
Targeted Fix for 1+1=2 Issue

The model achieves 97.1% accuracy on 35 complex cases but consistently fails on 1+1=2.
This suggests the issue is specific to the minimal case, not the general approach.

Strategy: Create a specialized training focused on fixing 1+1 while preserving 
the excellent performance on all other cases.
"""

import numpy as np
import time
import pickle
from tqdm import tqdm
from adaptive_nonlocal_training import AdaptiveEM43, evaluate_adaptive
from em43_numba import _sanitize_programme, _sanitize_rule

def load_extended_model():
    """Load the extended model that works on complex cases."""
    try:
        with open('extended_adaptive_model.pkl', 'rb') as f:
            model_data = pickle.load(f)
        
        best_model = AdaptiveEM43(
            model_data['rule'],
            model_data['prog'],
            model_data['encoding_params'],
            model_data['decoding_params'],
            model_data['halting_params']
        )
        
        print(f"✅ Loaded extended model: {model_data['accuracy']:.1%} accuracy")
        return best_model, model_data
    
    except FileNotFoundError:
        print("❌ Extended model not found!")
        return None, None

def debug_one_plus_one(model):
    """Debug why 1+1=2 specifically fails."""
    print("\n🔍 DEBUGGING 1+1=2 FAILURE")
    print("=" * 40)
    
    # Test the encoding
    state = model.encode_inputs(1, 1, N=30)
    print(f"Encoding 1+1: {' '.join(str(x) if x != 0 else '.' for x in state)}")
    
    # Run simulation with detailed tracing
    N = 30
    state = model.encode_inputs(1, 1, N)
    
    rule_local = model.rule[0:64]
    rule_skip_left = model.rule[64:128]
    rule_skip_right = model.rule[128:192]
    rule_long_range = model.rule[192:256]
    
    print(f"\nSimulation trace:")
    for step in range(20):  # Limited steps for debugging
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(1, N - 1):
            left = state[x-1] if x > 0 else 0
            center = state[x]
            right = state[x+1] if x < N-1 else 0
            local_idx = (left << 4) | (center << 2) | right
            
            nxt[x] = rule_local[local_idx]
            
            if x >= 2 and x < N - 2:
                left2 = state[x-2]
                right2 = state[x+2]
                
                if center != 0:
                    long_range_idx = (left2 << 4) | (center << 2) | right2
                    nxt[x] = rule_long_range[long_range_idx]
                elif left != 0 and right == 0:
                    skip_left_idx = (left2 << 4) | (center << 2) | right
                    nxt[x] = rule_skip_left[skip_left_idx]
                elif right != 0 and left == 0:
                    skip_right_idx = (left << 4) | (center << 2) | right2
                    nxt[x] = rule_skip_right[skip_right_idx]
        
        state_str = ' '.join(str(x) if x != 0 else '.' for x in nxt)
        print(f"  Step {step+1:2d}: {state_str}")
        
        # Check halting
        if model.check_halting(nxt):
            result = model.decode_result(nxt)
            print(f"  HALTED: Result = {result}")
            return result
        
        state = nxt
        
        # Check for no change
        if step > 0 and np.array_equal(state, nxt):
            print(f"  No change detected")
            break
    
    print(f"  Did not halt within 20 steps")
    return -10

def create_one_plus_one_focused_population(base_model, pop_size=50):
    """Create population specifically designed to fix 1+1=2."""
    population = []
    rng = np.random.default_rng()
    
    # Keep the base model
    population.append(base_model)
    
    # Create variations with different schemes
    for i in range(pop_size - 1):
        # Start with base model
        new_rule = base_model.rule.copy()
        new_prog = base_model.prog.copy()
        
        # Try different encoding schemes
        new_encoding = base_model.encoding_params.copy()
        new_decoding = base_model.decoding_params.copy()
        new_halting = base_model.halting_params.copy()
        
        # Vary schemes more aggressively for 1+1 case
        if i < 10:  # Try different separators
            new_encoding['separator_type'] = i % 4
        elif i < 20:  # Try different input encodings
            new_encoding['input_encoding'] = (i - 10) % 4
        elif i < 30:  # Try different decoding methods
            new_decoding['method'] = (i - 20) % 5
        elif i < 40:  # Try different halting conditions
            new_halting['condition_type'] = (i - 30) % 5
            new_halting['threshold'] = 30 + (i - 30) * 5  # 30-75 range
        else:  # Random variations
            if rng.random() < 0.3:
                new_encoding['separator_type'] = rng.integers(0, 4)
            if rng.random() < 0.3:
                new_encoding['input_encoding'] = rng.integers(0, 4)
            if rng.random() < 0.3:
                new_decoding['method'] = rng.integers(0, 5)
            if rng.random() < 0.3:
                new_halting['condition_type'] = rng.integers(0, 5)
            if rng.random() < 0.3:
                new_halting['threshold'] = rng.integers(25, 85)
        
        # Light rule mutations
        for j in range(256):
            if rng.random() < 0.01:  # Very light mutation
                new_rule[j] = rng.integers(0, 4)
        
        # Re-sanitize base rule
        base_rule = new_rule[0:64]
        base_rule = _sanitize_rule(base_rule)
        new_rule[0:64] = base_rule
        
        # Light program mutations
        for j in range(len(new_prog)):
            if rng.random() < 0.02:
                new_prog[j] = rng.choice([0, 1], p=[0.6, 0.4])
        
        new_prog = _sanitize_programme(new_prog)
        
        variant = AdaptiveEM43(new_rule, new_prog, new_encoding, new_decoding, new_halting)
        population.append(variant)
    
    return population

def fix_one_plus_one_training():
    """Targeted training to fix 1+1=2 while preserving other performance."""
    print("🎯 TARGETED 1+1=2 FIX TRAINING")
    print("=" * 50)
    
    # Load the model that works on complex cases
    base_model, model_data = load_extended_model()
    if not base_model:
        return None, 0.0
    
    # Debug the current failure
    debug_result = debug_one_plus_one(base_model)
    print(f"Current 1+1 result: {debug_result}")
    
    # Create test cases: 1+1 plus a sample of cases that should still work
    critical_cases = [
        (1, 1),  # The problem case
        (1, 2), (2, 1), (2, 2),  # Basic cases that must still work
        (3, 3), (4, 4), (5, 5),  # Equal cases that must still work
        (2, 5), (5, 2), (3, 4), (4, 3),  # Complex cases that must still work
    ]
    
    print(f"\nCritical test cases: {critical_cases}")
    print("Goal: Fix 1+1=2 while maintaining 100% on other critical cases")
    
    # Create specialized population
    population = create_one_plus_one_focused_population(base_model, pop_size=60)
    
    GENERATIONS = 150
    best_model = None
    best_score = 0
    
    print(f"\nTraining {len(population)} specialized models...")
    
    for gen in range(GENERATIONS):
        fitness_scores = []
        
        for em43 in population:
            results = evaluate_adaptive(em43, critical_cases)
            
            correct = sum(1 for r in results if r['correct'])
            convergent = sum(1 for r in results if r['convergent'])
            
            # Special fitness function
            fitness = 0
            
            # Massive bonus for fixing 1+1=2
            one_plus_one = next((r for r in results if r['a'] == 1 and r['b'] == 1), None)
            if one_plus_one and one_plus_one['correct']:
                fitness += 10000  # Huge bonus
            elif one_plus_one and one_plus_one['convergent']:
                fitness += 1000   # Smaller bonus for convergence
            
            # Must maintain performance on other cases
            other_cases = [r for r in results if not (r['a'] == 1 and r['b'] == 1)]
            other_correct = sum(1 for r in other_cases if r['correct'])
            
            # Penalty for breaking other cases
            if other_correct < len(other_cases):
                fitness -= (len(other_cases) - other_correct) * 2000
            else:
                fitness += other_correct * 500  # Bonus for maintaining performance
            
            # Overall convergence bonus
            fitness += convergent * 100
            
            fitness_scores.append(fitness)
        
        # Track best
        best_idx = np.argmax(fitness_scores)
        gen_results = evaluate_adaptive(population[best_idx], critical_cases)
        
        # Calculate score: 1+1 correct + other cases correct
        one_plus_one_correct = any(r['a'] == 1 and r['b'] == 1 and r['correct'] for r in gen_results)
        other_correct = sum(1 for r in gen_results if not (r['a'] == 1 and r['b'] == 1) and r['correct'])
        total_other = len([r for r in gen_results if not (r['a'] == 1 and r['b'] == 1)])
        
        gen_score = (1 if one_plus_one_correct else 0) + (other_correct / total_other if total_other > 0 else 0)
        
        if gen_score > best_score:
            best_score = gen_score
            best_model = population[best_idx]
        
        # Progress report
        if gen % 30 == 0 or one_plus_one_correct:
            one_plus_one_result = next((r for r in gen_results if r['a'] == 1 and r['b'] == 1), None)
            one_plus_one_status = "✓" if one_plus_one_result and one_plus_one_result['correct'] else "✗"
            
            print(f"  Gen {gen:3d}: 1+1={one_plus_one_result['result'] if one_plus_one_result else '?'} {one_plus_one_status}, others={other_correct}/{total_other}, score={gen_score:.2f}")
            
            if one_plus_one_correct:
                print(f"    🎉 1+1=2 FIXED! Checking if other cases still work...")
                
                # Test on full extended set to make sure we didn't break anything
                extended_cases = [(a, b) for a in range(1, 6) for b in range(1, 6)]
                extended_results = evaluate_adaptive(population[best_idx], extended_cases)
                extended_correct = sum(1 for r in extended_results if r['correct'])
                
                print(f"    Extended test: {extended_correct}/{len(extended_cases)} ({extended_correct/len(extended_cases)*100:.1f}%)")
                
                if extended_correct / len(extended_cases) >= 0.9:  # Still good performance
                    print(f"    🎯 SUCCESS! 1+1 fixed with {extended_correct/len(extended_cases)*100:.1f}% maintained!")
                    break
        
        # Reproduction with focus on successful schemes
        if gen < GENERATIONS - 1:
            sorted_indices = np.argsort(fitness_scores)[::-1]
            n_elite = len(population) // 4
            
            new_population = []
            
            # Keep elites
            for i in range(n_elite):
                new_population.append(population[sorted_indices[i]])
            
            # Generate offspring focused on successful schemes
            rng = np.random.default_rng()
            while len(new_population) < len(population):
                parent_idx = rng.choice(sorted_indices[:n_elite])
                parent = population[parent_idx]
                
                # Create child with light mutations
                child_rule = parent.rule.copy()
                child_prog = parent.prog.copy()
                child_encoding = parent.encoding_params.copy()
                child_decoding = parent.decoding_params.copy()
                child_halting = parent.halting_params.copy()
                
                # Very light mutations
                for i in range(256):
                    if rng.random() < 0.005:
                        child_rule[i] = rng.integers(0, 4)
                
                base_rule = child_rule[0:64]
                base_rule = _sanitize_rule(base_rule)
                child_rule[0:64] = base_rule
                
                for i in range(len(child_prog)):
                    if rng.random() < 0.01:
                        child_prog[i] = rng.choice([0, 1], p=[0.6, 0.4])
                
                child_prog = _sanitize_programme(child_prog)
                
                # Scheme mutations
                if rng.random() < 0.05:
                    child_encoding['separator_type'] = rng.integers(0, 4)
                if rng.random() < 0.05:
                    child_encoding['input_encoding'] = rng.integers(0, 4)
                if rng.random() < 0.05:
                    child_decoding['method'] = rng.integers(0, 5)
                if rng.random() < 0.05:
                    child_halting['condition_type'] = rng.integers(0, 5)
                if rng.random() < 0.05:
                    child_halting['threshold'] = rng.integers(30, 80)
                
                child = AdaptiveEM43(child_rule, child_prog, child_encoding, child_decoding, child_halting)
                new_population.append(child)
            
            population = new_population
    
    return best_model, best_score

if __name__ == "__main__":
    start_time = time.time()
    
    print("🔧 TARGETED 1+1=2 FIX")
    print("Fixing the last remaining issue")
    print("=" * 50)
    
    # Run targeted fix
    best_model, final_score = fix_one_plus_one_training()
    
    total_time = time.time() - start_time
    
    print(f"\n🎯 TARGETED FIX COMPLETE!")
    print(f"Final score: {final_score:.2f}/2.0")
    print(f"Time: {total_time/60:.1f} minutes")
    
    if best_model and final_score > 1.8:
        print(f"\n🎉 SUCCESS! 1+1=2 issue appears to be fixed!")
        
        # Comprehensive test
        comprehensive_cases = [(a, b) for a in range(1, 7) for b in range(1, 7)]
        comprehensive_results = evaluate_adaptive(best_model, comprehensive_cases)
        
        correct_total = sum(1 for r in comprehensive_results if r['correct'])
        convergent_total = sum(1 for r in comprehensive_results if r['convergent'])
        
        print(f"\n📊 COMPREHENSIVE TEST:")
        print(f"Accuracy: {correct_total}/{len(comprehensive_cases)} ({correct_total/len(comprehensive_cases)*100:.1f}%)")
        print(f"Convergence: {convergent_total}/{len(comprehensive_cases)} ({convergent_total/len(comprehensive_cases)*100:.1f}%)")
        
        # Check 1+1 specifically
        one_plus_one = next((r for r in comprehensive_results if r['a'] == 1 and r['b'] == 1), None)
        if one_plus_one:
            status = "✅ FIXED" if one_plus_one['correct'] else "❌ still broken"
            print(f"1+1=2 → {one_plus_one['result']} {status}")
        
        # Save the fixed model
        model_data = {
            'rule': best_model.rule,
            'prog': best_model.prog,
            'encoding_params': best_model.encoding_params,
            'decoding_params': best_model.decoding_params,
            'halting_params': best_model.halting_params,
            'accuracy': correct_total/len(comprehensive_cases),
            'type': 'fixed_adaptive_nonlocal',
            'test_cases': comprehensive_cases
        }
        
        with open('fixed_adaptive_model.pkl', 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"\n💾 Fixed model saved: fixed_adaptive_model.pkl")
        
    else:
        print(f"\n⚠️  1+1=2 issue persists, may need different approach")
    
    print(f"\n🎯 The nonlocal adaptive approach has achieved remarkable results!")
    print(f"   Ready for application to more complex mathematical operations")
