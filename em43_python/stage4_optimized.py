"""
Stage 4: Optimized Training - Building on successful Stage 3.5 model
Target: 1-5 + 1-5 (25 cases) with 80%+ accuracy
"""

import numpy as np
import time
import pickle
from tqdm import tqdm
from em43_numba import _sanitize_rule, _sanitize_programme

def simulate_addition_optimized(rule, prog, a, b, window=400, max_steps=800, halt_thresh=0.5):
    """Optimized addition simulation for Stage 4."""
    L = len(prog)
    N = window
    
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(L):
        state[j] = prog[j]
    state[L] = 3      # B
    state[L + 1] = 3  # B
    
    # Write inputs: 0^(a+1) R 0^(b+1) R
    r1_pos = L + 2 + a + 1
    if r1_pos >= N:
        return -10
    state[r1_pos] = 2
    
    r2_pos = r1_pos + 1 + b + 1
    if r2_pos >= N:
        return -10
    state[r2_pos] = 2
    
    # Adaptive simulation range
    sim_range = min(N - 1, r2_pos + max(a + b + 10, 30))
    
    # Run CA simulation
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(1, sim_range):
            idx = (state[x-1] << 4) | (state[x] << 2) | state[x+1]
            nxt[x] = rule[idx]
        
        # Halting check
        live = blue = 0
        for x in range(sim_range):
            v = nxt[x]
            if v != 0:
                live += 1
                if v == 3:
                    blue += 1
        
        if live > 0 and blue / live >= halt_thresh:
            # Find rightmost R
            rpos = -1
            for x in range(sim_range - 1, -1, -1):
                if nxt[x] == 2:
                    rpos = x
                    break
            
            if rpos != -1:
                return rpos - (L + 3)
            else:
                return -10
        
        state = nxt
    
    return -10

def load_stage3_5_model():
    """Load the successful Stage 3.5 model."""
    try:
        with open('curriculum_checkpoint_stage3_5_final.pkl', 'rb') as f:
            checkpoint = pickle.load(f)
        return checkpoint['rule'], checkpoint['prog']
    except:
        # Fallback to stage 2 model
        try:
            with open('curriculum_checkpoint_stage2.pkl', 'rb') as f:
                checkpoint = pickle.load(f)
            return checkpoint['model']
        except:
            with open('addition_model.pkl', 'rb') as f:
                model_data = pickle.load(f)
            return model_data['rule'], model_data['prog']

def evaluate_detailed(rule, prog, test_cases):
    """Detailed evaluation with individual case analysis."""
    results = []
    
    for a, b in test_cases:
        expected = a + b
        result = simulate_addition_optimized(rule, prog, a, b)
        
        status = "✓" if result == expected else "✗"
        convergent = result != -10
        
        results.append({
            'a': a, 'b': b, 'expected': expected, 'result': result,
            'correct': result == expected, 'convergent': convergent, 'status': status
        })
    
    return results

def train_stage4():
    """Train Stage 4: 1-5 + 1-5 (25 cases)."""
    print("🚀 CURRICULUM STAGE 4: LARGER NUMBERS")
    print("=" * 60)
    
    # Stage 4 test cases: 1-5 + 1-5 = 25 cases
    stage4_cases = [(a, b) for a in range(1, 6) for b in range(1, 6)]
    
    # Categorize cases for analysis
    stage3_5_cases = [(1, 1), (1, 2), (2, 1), (2, 2), (3, 1), (1, 3), 
                      (3, 2), (2, 3), (3, 3), (4, 1), (1, 4),
                      (1, 5), (5, 1), (2, 4), (4, 2), (3, 4), (4, 3), (5, 2), (2, 5)]
    
    new_cases = [case for case in stage4_cases if case not in stage3_5_cases]
    
    print(f"Stage 4 total cases: {len(stage4_cases)}")
    print(f"  Known from Stage 3.5: {len(stage3_5_cases)} cases")
    print(f"  New cases: {len(new_cases)} cases - {new_cases}")
    print("Target accuracy: 80.0% (20/25 cases)")
    print("Target convergence: 95.0%")
    
    # Load Stage 3.5 model
    initial_rule, initial_prog = load_stage3_5_model()
    print("✅ Loaded Stage 3.5 model (89.47% accuracy)")
    
    # Evaluate initial performance
    print("\n📊 INITIAL PERFORMANCE ANALYSIS")
    initial_results = evaluate_detailed(initial_rule, initial_prog, stage4_cases)
    
    correct_count = sum(1 for r in initial_results if r['correct'])
    convergent_count = sum(1 for r in initial_results if r['convergent'])
    
    print(f"Initial accuracy: {correct_count}/{len(stage4_cases)} ({correct_count/len(stage4_cases)*100:.1f}%)")
    print(f"Convergence rate: {convergent_count}/{len(stage4_cases)} ({convergent_count/len(stage4_cases)*100:.1f}%)")
    
    # Analyze by subset
    known_results = [r for r in initial_results if (r['a'], r['b']) in stage3_5_cases]
    new_results = [r for r in initial_results if (r['a'], r['b']) in new_cases]
    
    known_correct = sum(1 for r in known_results if r['correct'])
    new_correct = sum(1 for r in new_results if r['correct'])
    
    print(f"\nSubset performance:")
    print(f"  Known cases (Stage 3.5): {known_correct}/{len(known_results)} ({known_correct/len(known_results)*100:.1f}%)")
    print(f"  New cases: {new_correct}/{len(new_results)} ({new_correct/len(new_results)*100:.1f}%)")
    
    # Show failing cases
    failing_cases = [r for r in initial_results if not r['correct']]
    print(f"\nFailing cases ({len(failing_cases)}):")
    for r in failing_cases[:10]:  # Show first 10
        print(f"  {r['a']}+{r['b']}={r['expected']} → {r['result']}")
    
    # Training parameters - optimized for Stage 4
    POP_SIZE = 80
    GENERATIONS = 300
    MUTATION_RATE = 0.004  # Even finer for larger problems
    
    print(f"\n🚀 STAGE 4 TRAINING")
    print(f"Population: {POP_SIZE}, Generations: {GENERATIONS}, Mutation: {MUTATION_RATE}")
    
    # Initialize population
    population = []
    rng = np.random.default_rng()
    
    for i in range(POP_SIZE):
        rule = initial_rule.copy()
        prog = initial_prog.copy()
        
        if i > 0:  # Keep one copy unchanged
            # Very small mutations for fine-tuning
            mask = rng.random(64) < MUTATION_RATE
            rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)
            
            mask = rng.random(len(prog)) < MUTATION_RATE * 2
            prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.8, 0.15, 0.05])
        
        rule = _sanitize_rule(rule)
        prog = _sanitize_programme(prog)
        population.append((rule, prog))
    
    # Training loop
    best_accuracy = correct_count / len(stage4_cases)
    best_model = (initial_rule, initial_prog)
    best_fitness = -1000
    
    print("Training...")
    
    for gen in tqdm(range(GENERATIONS), desc="Stage 4 Training"):
        # Evaluate population
        fitness_scores = []
        
        for rule, prog in population:
            results = evaluate_detailed(rule, prog, stage4_cases)
            
            correct = sum(1 for r in results if r['correct'])
            convergent = sum(1 for r in results if r['convergent'])
            
            accuracy = correct / len(stage4_cases)
            convergence_rate = convergent / len(stage4_cases)
            
            # Weighted fitness function
            known_results_current = [r for r in results if (r['a'], r['b']) in stage3_5_cases]
            new_results_current = [r for r in results if (r['a'], r['b']) in new_cases]
            
            known_correct_current = sum(1 for r in known_results_current if r['correct'])
            new_correct_current = sum(1 for r in new_results_current if r['correct'])
            
            # Weighted fitness: prioritize maintaining known cases, reward new cases
            fitness = (known_correct_current * 80) + (new_correct_current * 100) + (convergent * 20)
            
            # Bonus for high overall accuracy
            if accuracy >= 0.8:
                fitness += 800
            elif accuracy >= 0.7:
                fitness += 400
            elif accuracy >= 0.6:
                fitness += 200
            
            # Penalty for regression on known cases
            if known_correct_current < len(known_results) * 0.85:
                fitness -= 500
            
            fitness_scores.append(fitness)
        
        # Track best
        gen_best_idx = np.argmax(fitness_scores)
        gen_best_fitness = fitness_scores[gen_best_idx]
        
        gen_results = evaluate_detailed(population[gen_best_idx][0], population[gen_best_idx][1], stage4_cases)
        gen_accuracy = sum(1 for r in gen_results if r['correct']) / len(stage4_cases)
        gen_convergence = sum(1 for r in gen_results if r['convergent']) / len(stage4_cases)
        
        if gen_accuracy > best_accuracy:
            best_accuracy = gen_accuracy
            best_model = population[gen_best_idx]
            best_fitness = gen_best_fitness
        
        # Progress report
        if gen % 50 == 0:
            # Analyze current best
            gen_known_results = [r for r in gen_results if (r['a'], r['b']) in stage3_5_cases]
            gen_new_results = [r for r in gen_results if (r['a'], r['b']) in new_cases]
            
            gen_known_acc = sum(1 for r in gen_known_results if r['correct']) / len(gen_known_results)
            gen_new_acc = sum(1 for r in gen_new_results if r['correct']) / len(gen_new_results)
            
            print(f"  Gen {gen}: overall={gen_accuracy:.2%}, known={gen_known_acc:.2%}, new={gen_new_acc:.2%}, conv={gen_convergence:.2%}")
            
            # Show current failing cases
            current_failing = [r for r in gen_results if not r['correct']]
            if len(current_failing) <= 8:
                print(f"    Failing: {[(r['a'], r['b']) for r in current_failing]}")
            else:
                print(f"    Failing: {len(current_failing)} cases")
        
        # Early stopping if target reached
        if best_accuracy >= 0.80:
            print(f"  🎯 Target accuracy 80% reached at generation {gen}!")
            break
        
        # Selection and reproduction
        if gen < GENERATIONS - 1:
            # Elite selection
            sorted_indices = np.argsort(fitness_scores)[::-1]
            n_elite = POP_SIZE // 4
            
            new_population = []
            
            # Keep elites
            for i in range(n_elite):
                new_population.append(population[sorted_indices[i]])
            
            # Generate offspring
            while len(new_population) < POP_SIZE:
                # Tournament selection
                tournament_size = 4
                tournament_indices = rng.choice(sorted_indices[:POP_SIZE//2], size=tournament_size, replace=False)
                parent_idx = tournament_indices[0]
                
                rule, prog = population[parent_idx]
                
                # Adaptive mutation
                current_mutation_rate = MUTATION_RATE
                if gen > 150 and best_accuracy < 0.75:
                    current_mutation_rate *= 1.5  # Increase exploration if stuck
                
                # Mutate
                new_rule = rule.copy()
                new_prog = prog.copy()
                
                mask = rng.random(64) < current_mutation_rate
                new_rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)
                
                mask = rng.random(len(prog)) < current_mutation_rate * 2
                new_prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.8, 0.15, 0.05])
                
                new_population.append((_sanitize_rule(new_rule), _sanitize_programme(new_prog)))
            
            population = new_population
    
    return best_model, best_accuracy

def analyze_stage4_results(rule, prog):
    """Comprehensive analysis of Stage 4 results."""
    print("\n" + "=" * 60)
    print("🔬 COMPREHENSIVE STAGE 4 ANALYSIS")
    print("=" * 60)
    
    # Test different ranges
    stage4_cases = [(a, b) for a in range(1, 6) for b in range(1, 6)]
    results = evaluate_detailed(rule, prog, stage4_cases)
    
    correct = sum(1 for r in results if r['correct'])
    convergent = sum(1 for r in results if r['convergent'])
    
    print(f"Final accuracy: {correct}/{len(stage4_cases)} ({correct/len(stage4_cases)*100:.1f}%)")
    print(f"Convergence rate: {convergent}/{len(stage4_cases)} ({convergent/len(stage4_cases)*100:.1f}%)")
    
    # Show all results in a grid
    print("\nComplete results grid (a+b=expected → result):")
    for a in range(1, 6):
        row = []
        for b in range(1, 6):
            r = next(res for res in results if res['a'] == a and res['b'] == b)
            status = "✓" if r['correct'] else "✗"
            row.append(f"{a}+{b}={r['expected']}→{r['result']}{status}")
        print(f"  {' | '.join(row)}")
    
    # Analyze failures by pattern
    failures = [r for r in results if not r['correct']]
    if failures:
        print(f"\nFailure analysis ({len(failures)} cases):")
        for r in failures:
            error = r['result'] - r['expected'] if r['result'] != -10 else "NC"
            print(f"  {r['a']}+{r['b']}={r['expected']} → {r['result']} (error: {error})")
    
    # Test generalization
    print(f"\nGeneralization test:")
    gen_cases = [(6, 1), (1, 6), (5, 5), (6, 2), (2, 6)]
    gen_results = evaluate_detailed(rule, prog, gen_cases)
    
    for r in gen_results:
        print(f"  {r['a']}+{r['b']}={r['expected']} → {r['result']} {r['status']}")
    
    # Save Stage 4 model
    stage4_model = {
        'rule': rule,
        'prog': prog,
        'accuracy': correct/len(stage4_cases),
        'stage': 'stage4_complete',
        'test_cases': stage4_cases
    }
    
    with open('curriculum_checkpoint_stage4_final.pkl', 'wb') as f:
        pickle.dump(stage4_model, f)
    
    print(f"\n💾 Stage 4 model saved: curriculum_checkpoint_stage4_final.pkl")
    
    return correct/len(stage4_cases)

if __name__ == "__main__":
    start_time = time.time()
    
    print("🚀 STAGE 4: BUILDING ON SUCCESSFUL STAGE 3.5")
    print("Scaling from 19 cases (89.47%) to 25 cases")
    print("=" * 60)
    
    # Train Stage 4
    best_model, final_accuracy = train_stage4()
    
    print(f"\n🎯 STAGE 4 TRAINING COMPLETE!")
    print(f"Final accuracy: {final_accuracy:.2%}")
    
    # Analyze results
    rule, prog = best_model
    final_accuracy_detailed = analyze_stage4_results(rule, prog)
    
    end_time = time.time()
    
    print(f"\n⏱️  Stage 4 training time: {(end_time - start_time)/60:.1f} minutes")
    
    if final_accuracy >= 0.80:
        print("\n🎉 SUCCESS: Target accuracy achieved!")
        print("🚀 Ready for Stage 5: Extended range (1-7 + 1-7)!")
    elif final_accuracy >= 0.70:
        print("\n✅ GOOD: Strong performance, can proceed with caution!")
    else:
        print("\n⚠️  Needs more training or parameter adjustment")
    
    print(f"\n📈 COMPLETE CURRICULUM PROGRESS:")
    print(f"  Stage 1: 100% (3 cases)")
    print(f"  Stage 2: 100% (6 cases)")  
    print(f"  Stage 3: 90.91% (11 cases)")
    print(f"  Stage 3.5: 89.47% (19 cases)")
    print(f"  Stage 4: {final_accuracy:.1%} (25 cases)")
    print(f"  🎯 Curriculum learning successfully scaling emergent addition models!")
