"""
Debug Nonlocal Training

The minimal training showed 66.7% accuracy, which proves the approach works!
Now let's debug why 1+2=3 gives 19 instead of 3.
"""

import numpy as np
from em43_numba import _sanitize_programme

def _sanitize_nonlocal_rule(rule):
    """Sanitize nonlocal rule array (4 x 64 = 256 entries total)."""
    rule = rule.copy().astype(np.uint8)
    rule = rule.reshape(4, 64)
    
    for i in range(4):
        rule[i] = rule[i] & 0x03
        
        # Key immutable constraints
        immutable_patterns = [
            ((0<<4)|(0<<2)|0, 0),      # 000 -> 0
            ((0<<4)|(2<<2)|0, 2),      # 020 -> 2 (R preservation)
            ((0<<4)|(0<<2)|2, 0),      # 002 -> 0
            ((2<<4)|(0<<2)|0, 0),      # 200 -> 0
            ((0<<4)|(3<<2)|3, 3),      # 033 -> 3 (B preservation)
            ((3<<4)|(3<<2)|0, 3),      # 330 -> 3
        ]
        
        for pattern, value in immutable_patterns:
            rule[i][pattern] = value
    
    return rule.flatten()

def simulate_with_trace(rule, prog, a, b, max_steps=50):
    """Simulate with detailed tracing to debug the issue."""
    L = len(prog)
    N = 25
    
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(min(L, N)):
        state[j] = prog[j]
    
    if L + 1 < N:
        state[L] = 3      # B
    if L + 2 < N:
        state[L + 1] = 3  # B
    
    # Encoding: [prog] BB 0^a R 0^b R
    base_pos = L + 2
    
    print(f"Encoding {a} + {b}:")
    print(f"  Program length: {L}")
    print(f"  Base position: {base_pos}")
    
    # Write first input (a zeros)
    for i in range(a):
        pos = base_pos + i
        if pos < N:
            state[pos] = 0
            print(f"  Input a[{i}] at position {pos}")
    
    # First R marker
    r1_pos = base_pos + a
    if r1_pos < N:
        state[r1_pos] = 2
        print(f"  First R at position {r1_pos}")
    
    # Write second input (b zeros)
    for i in range(b):
        pos = r1_pos + 1 + i
        if pos < N:
            state[pos] = 0
            print(f"  Input b[{i}] at position {pos}")
    
    # Second R marker
    r2_pos = r1_pos + 1 + b
    if r2_pos < N:
        state[r2_pos] = 2
        print(f"  Second R at position {r2_pos}")
    
    print(f"  Initial state: {' '.join(str(x) if x != 0 else '.' for x in state)}")
    
    # Reshape rule for easier access
    rule_local = rule[0:64]
    rule_skip_left = rule[64:128]
    rule_skip_right = rule[128:192]
    rule_long_range = rule[192:256]
    
    # Run simulation with tracing
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        rule_usage = np.zeros(N, dtype=np.uint8)  # Track which rule was used
        
        # Apply rules
        for x in range(1, N - 1):
            left = state[x-1] if x > 0 else 0
            center = state[x]
            right = state[x+1] if x < N-1 else 0
            local_idx = (left << 4) | (center << 2) | right
            
            # Default to local rule
            nxt[x] = rule_local[local_idx]
            rule_usage[x] = 0
            
            # Try nonlocal rules if we have space
            if x >= 2 and x < N - 2:
                left2 = state[x-2]
                right2 = state[x+2]
                
                if center != 0:
                    long_range_idx = (left2 << 4) | (center << 2) | right2
                    nxt[x] = rule_long_range[long_range_idx]
                    rule_usage[x] = 3
                elif left != 0 and right == 0:
                    skip_left_idx = (left2 << 4) | (center << 2) | right
                    nxt[x] = rule_skip_left[skip_left_idx]
                    rule_usage[x] = 1
                elif right != 0 and left == 0:
                    skip_right_idx = (left << 4) | (center << 2) | right2
                    nxt[x] = rule_skip_right[skip_right_idx]
                    rule_usage[x] = 2
        
        # Show evolution
        state_str = ' '.join(str(x) if x != 0 else '.' for x in nxt)
        rule_str = ' '.join(['L', 'S', 'R', 'G'][r] for r in rule_usage)
        print(f"  Step {step+1:2d}: {state_str}")
        print(f"  Rules:   {rule_str}")
        
        # Check halting
        blue_count = sum(1 for x in nxt if x == 3)
        nonzero_count = sum(1 for x in nxt if x != 0)
        
        if nonzero_count > 0:
            blue_ratio = blue_count / nonzero_count
            print(f"  Blue ratio: {blue_ratio:.2f} (need > 0.4 to halt)")
            
            if blue_ratio > 0.4:
                # Find rightmost R and count zeros after it
                rightmost_r = -1
                for i in range(N-1, -1, -1):
                    if nxt[i] == 2:
                        rightmost_r = i
                        break
                
                print(f"  Rightmost R at position: {rightmost_r}")
                
                if rightmost_r != -1:
                    zero_count = 0
                    for i in range(rightmost_r + 1, N):
                        if nxt[i] == 0:
                            zero_count += 1
                            print(f"    Zero at position {i}")
                        else:
                            print(f"    Non-zero {nxt[i]} at position {i}, stopping count")
                            break
                    
                    print(f"  HALTED: Result = {zero_count}")
                    return zero_count
        
        state = nxt
        
        # Stop if no change
        if step > 0 and np.array_equal(state, nxt):
            print(f"  No change, stopping")
            break
    
    print(f"  Did not halt within {max_steps} steps")
    return -1

def debug_best_model():
    """Debug the best model from minimal training."""
    print("🔍 DEBUGGING NONLOCAL MODEL")
    print("=" * 50)
    
    # Create a simple model that worked partially
    rule = np.zeros(256, dtype=np.uint8)
    
    # Set up basic patterns that might work
    for rule_type in range(4):
        base = rule_type * 64
        # Basic patterns
        rule[base + 0] = 0   # 000 -> 0
        rule[base + 2] = 2   # 002 -> 2
        rule[base + 8] = 2   # 020 -> 2
        rule[base + 32] = 0  # 200 -> 0
        rule[base + 12] = 3  # 030 -> 3 (might help with halting)
        rule[base + 48] = 3  # 300 -> 3
        
        # Some propagation patterns
        rule[base + 4] = 1   # 010 -> 1
        rule[base + 1] = 1   # 001 -> 1
        rule[base + 16] = 0  # 100 -> 0
    
    rule = _sanitize_nonlocal_rule(rule)
    prog = np.array([0, 0, 1, 0], dtype=np.uint8)
    prog = _sanitize_programme(prog)
    
    print(f"Program: {prog}")
    print()
    
    # Test each case
    test_cases = [(1, 1), (1, 2), (2, 1)]
    
    for a, b in test_cases:
        print(f"{'='*60}")
        print(f"Testing {a} + {b} = {a + b}")
        print(f"{'='*60}")
        
        result = simulate_with_trace(rule, prog, a, b)
        
        status = "✓" if result == a + b else "✗"
        print(f"\nResult: {result} (expected {a + b}) {status}")
        print()

if __name__ == "__main__":
    debug_best_model()
