"""
Fast Commutative Nonlocal Addition Demo

Demonstrates the concept of commutative encoding with nonlocal rules
without the full training overhead.
"""

import numpy as np
from em43_numba import _sanitize_programme

def create_demo_commutative_rule():
    """Create a demonstration rule that shows commutative behavior."""
    rule = np.zeros(256, dtype=np.uint8)
    
    # Local rules (0-63)
    local = rule[0:64]
    local[0] = 0   # 000 -> 0
    local[2] = 2   # 002 -> 2 (preserve R)
    local[8] = 2   # 020 -> 2 (preserve R)
    local[32] = 0  # 200 -> 0
    local[4] = 1   # 010 -> 1 (basic propagation)
    local[1] = 1   # 001 -> 1 (propagation)
    local[16] = 0  # 100 -> 0
    
    # Skip-left rules (64-127) - same basic patterns
    skip_left = rule[64:128]
    skip_left[0] = 0
    skip_left[2] = 2
    skip_left[8] = 2
    skip_left[4] = 1
    skip_left[1] = 1
    
    # Skip-right rules (128-191)
    skip_right = rule[128:192]
    skip_right[0] = 0
    skip_right[2] = 2
    skip_right[8] = 2
    skip_right[4] = 1
    skip_right[1] = 1
    
    # Long-range rules (192-255)
    long_range = rule[192:256]
    long_range[0] = 0
    long_range[2] = 2
    long_range[8] = 2
    long_range[4] = 1  # Can propagate through blocking cells
    long_range[1] = 1
    
    return rule

def simulate_commutative_step_by_step(rule, prog, a, b, max_steps=15):
    """Simulate commutative addition step by step for visualization."""
    L = len(prog)
    N = 50  # Smaller for demo
    
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(L):
        state[j] = prog[j]
    state[L] = 3      # B
    state[L + 1] = 3  # B
    
    # Commutative encoding demonstration
    base_pos = L + 2
    
    print(f"Encoding {a} + {b} with commutative overlap:")
    
    # Show different encoding strategies
    if a == b:
        print(f"  Case a=b: inputs overlap completely")
        # Encode: 0^a R (single representation since a=b)
        for i in range(a):
            if base_pos + i < N:
                state[base_pos + i] = 0
        r_pos = base_pos + a
        if r_pos < N:
            state[r_pos] = 2
    else:
        print(f"  Case a≠b: encode both inputs")
        # Encode: 0^a R 0^b R
        for i in range(a):
            if base_pos + i < N:
                state[base_pos + i] = 0
        
        r1_pos = base_pos + a
        if r1_pos < N:
            state[r1_pos] = 2
        
        for i in range(b):
            pos = r1_pos + 1 + i
            if pos < N:
                state[pos] = 0
        
        r2_pos = r1_pos + 1 + b
        if r2_pos < N:
            state[r2_pos] = 2
    
    # Show initial encoding
    print(f"  Initial: {' '.join(str(x) if x != 0 else '.' for x in state[:20])}")
    
    # Simulate with nonlocal rules
    evolution = [state.copy()]
    rule_usage_history = []
    
    # Reshape rule for easier access
    rule_local = rule[0:64]
    rule_skip_left = rule[64:128]
    rule_skip_right = rule[128:192]
    rule_long_range = rule[192:256]
    
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        rule_usage = np.zeros(N, dtype=np.uint8)
        
        for x in range(2, N - 2):
            # Get neighborhoods
            left, center, right = state[x-1], state[x], state[x+1]
            left2, right2 = state[x-2], state[x+2]
            
            local_idx = (left << 4) | (center << 2) | right
            skip_left_idx = (left2 << 4) | (center << 2) | right
            skip_right_idx = (left << 4) | (center << 2) | right2
            long_range_idx = (left2 << 4) | (center << 2) | right2
            
            # Nonlocal rule selection
            if center != 0:
                nxt[x] = rule_long_range[long_range_idx]
                rule_usage[x] = 3
            elif left != 0 and right == 0:
                nxt[x] = rule_skip_left[skip_left_idx]
                rule_usage[x] = 1
            elif right != 0 and left == 0:
                nxt[x] = rule_skip_right[skip_right_idx]
                rule_usage[x] = 2
            else:
                nxt[x] = rule_local[local_idx]
                rule_usage[x] = 0
        
        # Handle boundaries
        for x in [1, N-2]:
            if 0 < x < N-1:
                left = state[x-1] if x > 0 else 0
                center = state[x]
                right = state[x+1] if x < N-1 else 0
                local_idx = (left << 4) | (center << 2) | right
                nxt[x] = rule_local[local_idx]
                rule_usage[x] = 0
        
        evolution.append(nxt.copy())
        rule_usage_history.append(rule_usage.copy())
        
        # Simple halting: if mostly blue
        blue_count = sum(1 for x in nxt if x == 3)
        total_nonzero = sum(1 for x in nxt if x != 0)
        
        if total_nonzero > 0 and blue_count / total_nonzero > 0.6:
            # Find result (count zeros after last R)
            last_r = -1
            for i in range(N-1, -1, -1):
                if nxt[i] == 2:
                    last_r = i
                    break
            
            if last_r != -1:
                result = 0
                for i in range(last_r + 1, N):
                    if nxt[i] == 0:
                        result += 1
                    elif nxt[i] != 0:
                        break
                return evolution, rule_usage_history, result
        
        state = nxt
        
        # Stop if no changes
        if step > 0 and np.array_equal(state, evolution[-2]):
            break
    
    return evolution, rule_usage_history, -1

def demonstrate_commutativity():
    """Demonstrate commutative encoding with nonlocal rules."""
    print("🔄 COMMUTATIVE NONLOCAL EM43 DEMONSTRATION")
    print("=" * 60)
    print("Testing commutative property: a + b = b + a")
    print("Special case: when a = b, inputs overlap naturally")
    print()
    
    # Create demo rule and program
    rule = create_demo_commutative_rule()
    prog = np.array([1, 0, 1, 0, 0, 0], dtype=np.uint8)
    prog = _sanitize_programme(prog)
    
    # Test cases to demonstrate commutativity
    test_cases = [
        (2, 2),  # Equal case - should show overlap
        (2, 3),  # Different case
        (3, 2),  # Commutative pair
    ]
    
    results = {}
    
    for a, b in test_cases:
        print(f"\n{'='*40}")
        print(f"Testing: {a} + {b} = {a + b}")
        print(f"{'='*40}")
        
        evolution, rule_usage, result = simulate_commutative_step_by_step(rule, prog, a, b)
        results[(a, b)] = result
        
        print(f"Result: {result} (expected: {a + b})")
        
        # Show first few steps
        print("\nEvolution:")
        for i, state in enumerate(evolution[:5]):
            state_str = ' '.join(str(x) if x != 0 else '.' for x in state[:20])
            print(f"  Step {i}: {state_str}")
            
            if i > 0 and i-1 < len(rule_usage):
                rule_str = ' '.join(['L', 'S', 'R', 'G'][r] for r in rule_usage[i-1][:20])
                print(f"  Rules:   {rule_str}")
        
        print()
    
    # Analyze commutativity
    print("\n🔍 COMMUTATIVITY ANALYSIS:")
    print("=" * 40)
    
    # Check if (2,3) and (3,2) give same result
    if (2, 3) in results and (3, 2) in results:
        result_23 = results[(2, 3)]
        result_32 = results[(3, 2)]
        
        print(f"2 + 3 = {result_23}")
        print(f"3 + 2 = {result_32}")
        
        if result_23 == result_32:
            print("✅ Commutativity preserved!")
        else:
            print("❌ Commutativity not preserved")
    
    # Check overlap case
    if (2, 2) in results:
        result_22 = results[(2, 2)]
        print(f"2 + 2 = {result_22} (overlap case)")
    
    print("\n🎯 KEY INSIGHTS:")
    print("1. Commutative encoding allows a+b and b+a to be treated equivalently")
    print("2. When a=b, inputs naturally overlap in the same positions")
    print("3. Nonlocal rules help information propagate efficiently")
    print("4. This reduces the training space by leveraging mathematical properties")

def create_commutative_html_demo():
    """Create HTML demo showing commutative encoding."""
    rule = create_demo_commutative_rule()
    prog = np.array([1, 0, 1, 0, 0, 0], dtype=np.uint8)
    
    html = """
<!DOCTYPE html>
<html>
<head>
    <title>Commutative Nonlocal EM43 Demo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .case { margin: 20px 0; border: 1px solid #ccc; padding: 15px; }
        .step { margin: 10px 0; }
        .grid { display: grid; grid-template-columns: repeat(20, 20px); gap: 1px; margin: 5px 0; }
        .cell { width: 20px; height: 20px; border: 1px solid #999; text-align: center; line-height: 20px; font-size: 12px; }
        .cell-0 { background-color: #ffffff; }
        .cell-1 { background-color: #000000; color: white; }
        .cell-2 { background-color: #ff2222; color: white; }
        .cell-3 { background-color: #1665c1; color: white; }
        .rule-L { border: 2px solid #00ff00; }
        .rule-S { border: 2px solid #ff8800; }
        .rule-R { border: 2px solid #8800ff; }
        .rule-G { border: 2px solid #ff0088; }
        .highlight { background-color: #ffff99; }
    </style>
</head>
<body>
    <h1>Commutative Nonlocal EM43 Demonstration</h1>
    <p>Shows how commutative encoding works with nonlocal rules: a + b = b + a</p>
"""
    
    test_cases = [(2, 2), (2, 3), (3, 2)]
    
    for a, b in test_cases:
        evolution, rule_usage, result = simulate_commutative_step_by_step(rule, prog, a, b, max_steps=5)
        
        html += f'    <div class="case">\n'
        html += f'        <h3>{a} + {b} = {a + b} (Result: {result})</h3>\n'
        
        if a == b:
            html += f'        <p><strong>Special case:</strong> a = b, so inputs overlap naturally</p>\n'
        else:
            html += f'        <p><strong>Commutative pair:</strong> Should give same result as {b} + {a}</p>\n'
        
        # Show evolution
        for i, state in enumerate(evolution[:4]):
            html += f'        <div class="step">Step {i}:<div class="grid">'
            for j, cell in enumerate(state[:20]):
                rule_class = ""
                if i > 0 and i-1 < len(rule_usage) and j < len(rule_usage[i-1]):
                    rule_types = ["rule-L", "rule-S", "rule-R", "rule-G"]
                    rule_class = rule_types[rule_usage[i-1][j]]
                
                # Highlight overlapping positions for a=b case
                highlight = "highlight" if a == b and i == 0 and 8 <= j < 8 + a else ""
                
                html += f'<div class="cell cell-{cell} {rule_class} {highlight}">{cell if cell != 0 else ""}</div>'
            html += '</div></div>\n'
        
        html += '    </div>\n'
    
    html += """
    <div style="margin-top: 30px;">
        <h3>Key Features:</h3>
        <ul>
            <li><strong>Commutative Encoding:</strong> a + b and b + a use equivalent representations</li>
            <li><strong>Natural Overlap:</strong> When a = b, inputs occupy the same cells automatically</li>
            <li><strong>Nonlocal Rules:</strong> Information can skip past blocking cells (colored borders)</li>
            <li><strong>Efficient Training:</strong> Reduces search space by leveraging mathematical properties</li>
        </ul>
        
        <h3>Rule Types:</h3>
        <div style="display: flex; gap: 20px;">
            <div><div class="cell rule-L" style="display: inline-block;">L</div> Local (standard)</div>
            <div><div class="cell rule-S" style="display: inline-block;">S</div> Skip-left</div>
            <div><div class="cell rule-R" style="display: inline-block;">R</div> Skip-right</div>
            <div><div class="cell rule-G" style="display: inline-block;">G</div> Long-range</div>
        </div>
    </div>
</body>
</html>
"""
    
    with open('commutative_demo.html', 'w') as f:
        f.write(html)
    
    print("📄 HTML demo saved as 'commutative_demo.html'")

if __name__ == "__main__":
    demonstrate_commutativity()
    create_commutative_html_demo()
    print("\n🎉 Commutative demonstration complete!")
    print("Open 'commutative_demo.html' to see the visual demonstration.")
