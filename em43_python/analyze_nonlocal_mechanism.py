"""
Analyze how the model can compute 1000+1000 in just 2 steps with only 2-cell nonlocal rules
"""

import pickle
import numpy as np
from adaptive_nonlocal_training import AdaptiveEM43

# Load the perfect model
with open('perfect_adaptive_model.pkl', 'rb') as f:
    model_data = pickle.load(f)

perfect_model = AdaptiveEM43(
    model_data['rule'],
    model_data['prog'],
    model_data['encoding_params'],
    model_data['decoding_params'],
    model_data['halting_params']
)

print("🔍 ANALYZING THE 2-STEP MIRACLE")
print("=" * 60)
print("How can 1000+1000 be computed in 2 steps with only 2-cell nonlocal rules?")

# Test with a large case
a, b = 100, 100  # Start with smaller case to see pattern
N = 500
expected = a + b

print(f"\nTesting {a}+{b}={expected} step by step:")

# Initial encoding
state = perfect_model.encode_inputs(a, b, N)
print(f"Initial state length: {len(state)}")

# Find where the inputs are encoded
nonzero_positions = [(i, state[i]) for i in range(len(state)) if state[i] != 0]
print(f"Non-zero positions: {nonzero_positions}")

# Extract rule components
rule_local = perfect_model.rule[0:64]
rule_skip_left = perfect_model.rule[64:128]
rule_skip_right = perfect_model.rule[128:192]
rule_long_range = perfect_model.rule[192:256]

print(f"\nStep-by-step analysis:")

trace = [state.copy()]
for step in range(5):
    print(f"\nStep {step}:")
    
    # Show current state (first 50 positions)
    state_str = ' '.join(str(x) if x != 0 else '.' for x in state[:50])
    print(f"  State: {state_str}")
    
    # Find all non-zero positions
    nonzero = [(i, state[i]) for i in range(len(state)) if state[i] != 0]
    print(f"  Non-zero: {nonzero}")
    
    # Check halting
    if perfect_model.check_halting(state):
        result = perfect_model.decode_result(state)
        print(f"  HALTED: Result = {result}")
        break
    
    # Apply one step and track what changes
    nxt = np.zeros(N, dtype=np.uint8)
    changes = []
    
    for x in range(1, N - 1):
        left = state[x-1] if x > 0 else 0
        center = state[x]
        right = state[x+1] if x < N-1 else 0
        local_idx = (left << 4) | (center << 2) | right
        
        # Default to local rule
        new_val = rule_local[local_idx]
        
        # Apply nonlocal rules
        if x >= 2 and x < N - 2:
            left2 = state[x-2]
            right2 = state[x+2]
            
            if center != 0:
                long_range_idx = (left2 << 4) | (center << 2) | right2
                new_val = rule_long_range[long_range_idx]
            elif left != 0 and right == 0:
                skip_left_idx = (left2 << 4) | (center << 2) | right
                new_val = rule_skip_left[skip_left_idx]
            elif right != 0 and left == 0:
                skip_right_idx = (left << 4) | (center << 2) | right2
                new_val = rule_skip_right[skip_right_idx]
        
        nxt[x] = new_val
        
        # Track changes
        if new_val != state[x]:
            changes.append((x, state[x], new_val))
    
    print(f"  Changes: {changes[:20]}")  # Show first 20 changes
    
    state = nxt
    trace.append(state.copy())

print(f"\n🧬 ANALYSIS OF THE MECHANISM:")

# Look at the encoding more carefully
print(f"\nEncoding analysis:")
print(f"Program: {perfect_model.prog}")
print(f"Encoding params: {perfect_model.encoding_params}")

# Test the encoding function directly
print(f"\nTesting encoding for different values:")
for test_val in [1, 10, 100, 1000]:
    test_state = perfect_model.encode_inputs(test_val, test_val, 200)
    nonzero = [(i, test_state[i]) for i in range(len(test_state)) if test_state[i] != 0]
    print(f"  {test_val}+{test_val}: {nonzero}")

print(f"\n🤔 POSSIBLE EXPLANATIONS:")
print("1. The encoding might be more compact than expected")
print("2. The nonlocal rules might create cascading effects")
print("3. The computation might not require long-range communication")
print("4. The halting condition might be triggered very early")

# Check if it's really computing or just pattern matching
print(f"\n🔬 TESTING COMPUTATION vs PATTERN MATCHING:")
test_cases = [(99, 101), (98, 102), (50, 150), (1, 199)]
for ta, tb in test_cases:
    result = perfect_model.simulate(ta, tb) if hasattr(perfect_model, 'simulate') else "N/A"
    expected = ta + tb
    print(f"  {ta}+{tb}={expected} → {result} {'✅' if result == expected else '❌'}")

print(f"\n💡 HYPOTHESIS:")
print("The model might be using a very efficient encoding where:")
print("- Numbers are encoded in a compact way")
print("- The computation happens through local interactions")
print("- The result emerges from the encoding structure itself")
print("- Nonlocal rules enable efficient information flow in the compact space")
