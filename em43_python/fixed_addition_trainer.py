"""
Fixed Addition Trainer - Using the correct encoding pattern from divide-by-2
"""

import numpy as np
import time
from tqdm import tqdm
from em43_numba import _simulate, _sanitize_rule, _sanitize_programme

def simulate_addition_simple(rule, prog, a, b, window=300, max_steps=800, halt_thresh=0.5):
    """
    Simplified addition using single-input simulation pattern.
    
    Strategy: Encode a+b as a single number and use existing simulation.
    This is cheating but helps us understand if the issue is encoding or computation.
    """
    # Just use the sum as input to single-input simulation
    sum_input = np.array([a + b], dtype=np.int64)
    result = _simulate(rule, prog, sum_input, window, max_steps, halt_thresh)
    return result[0]

def simulate_addition_concatenated(rule, prog, a, b, window=300, max_steps=800, halt_thresh=0.5):
    """
    Addition by concatenating inputs: encode as 0^(a+1) R 0^(b+1) R and see what happens.
    
    This uses a modified single-input approach where we encode both numbers
    but still use single-input decoding.
    """
    L = len(prog)
    N = window
    
    # Initialize state
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(L):
        state[j] = prog[j]
    state[L] = 3      # B
    state[L + 1] = 3  # B
    
    # Write first input: a+1 zeros, then R
    r1_pos = L + 2 + a + 1
    if r1_pos < N:
        state[r1_pos] = 2
    
    # Write second input: b+1 zeros after first R, then R
    r2_pos = r1_pos + 1 + b + 1
    if r2_pos < N:
        state[r2_pos] = 2
    
    # Run CA simulation
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        for x in range(1, N - 1):
            idx = (state[x-1] << 4) | (state[x] << 2) | state[x+1]
            nxt[x] = rule[idx]
        
        # Check for halting
        live = blue = 0
        for x in range(N):
            v = nxt[x]
            if v != 0:
                live += 1
                if v == 3:
                    blue += 1
        
        if live > 0 and blue / live >= halt_thresh:
            # Halted - decode output
            # Find rightmost R
            rpos = -1
            for x in range(N - 1, -1, -1):
                if nxt[x] == 2:
                    rpos = x
                    break
            
            if rpos != -1:
                # Use same decoding as single-input
                return rpos - (L + 3)
            else:
                return -10  # No R found
        
        state = nxt
    
    return -10  # Didn't halt

def test_simple_approaches():
    """Test different simple approaches to addition."""
    print("🧪 TESTING SIMPLE ADDITION APPROACHES")
    print("=" * 50)
    
    # Load the working divide-by-2 model
    try:
        import pickle
        with open('best_genome.pkl', 'rb') as f:
            model_data = pickle.load(f)
        
        if isinstance(model_data, dict):
            rule = model_data['rule']
            prog = model_data['prog']
        else:
            rule, prog = model_data
            
        print("✅ Loaded working divide-by-2 model")
        print(f"Rule sample: {rule[:10]}")
        print(f"Program: {prog}")
        
    except Exception as e:
        print(f"❌ Could not load model: {e}")
        return
    
    # Test cases
    test_cases = [(1, 1), (1, 2), (2, 1), (2, 2), (3, 2)]
    
    print(f"\n📊 Testing on cases: {test_cases}")
    
    for a, b in test_cases:
        expected = a + b
        
        # Method 1: Cheat by using sum as single input
        result1 = simulate_addition_simple(rule, prog, a, b)
        
        # Method 2: Concatenated encoding
        result2 = simulate_addition_concatenated(rule, prog, a, b)
        
        print(f"\n{a} + {b} = {expected}:")
        print(f"  Method 1 (cheat): {result1}")
        print(f"  Method 2 (concat): {result2}")
        
        if result1 == expected:
            print("  ✅ Method 1 works!")
        if result2 == expected:
            print("  ✅ Method 2 works!")

def train_simple_addition():
    """Train addition using the simplest possible approach."""
    print("\n🚀 TRAINING SIMPLE ADDITION")
    print("=" * 50)
    
    # Training data - start VERY simple
    train_cases = [(1, 1), (1, 2), (2, 1)]
    
    print(f"Training cases: {train_cases}")
    
    # Simple GA parameters
    POP_SIZE = 100
    GENERATIONS = 500
    L_PROG = 8
    
    # Initialize population
    population = []
    rng = np.random.default_rng()
    
    for i in range(POP_SIZE):
        rule = rng.integers(0, 4, 64, dtype=np.uint8)
        prog = rng.choice([0, 1, 2], size=L_PROG, p=[0.8, 0.15, 0.05])
        rule = _sanitize_rule(rule)
        prog = _sanitize_programme(prog)
        population.append((rule, prog))
    
    best_fitness = -1000
    best_genome = None
    
    print("Starting training...")
    
    for gen in tqdm(range(GENERATIONS)):
        # Evaluate population
        fitness_scores = []
        
        for rule, prog in population:
            fitness = 0
            convergent = 0
            
            for a, b in train_cases:
                expected = a + b
                result = simulate_addition_concatenated(rule, prog, a, b)
                
                if result == -10:
                    fitness -= 10  # Penalty for non-convergence
                else:
                    convergent += 1
                    if result == expected:
                        fitness += 50  # Big reward for correct answer
                    else:
                        # Penalty based on error magnitude
                        error = abs(result - expected)
                        fitness -= error * 2
            
            # Bonus for convergence
            fitness += convergent * 10
            
            fitness_scores.append(fitness)
        
        # Track best
        gen_best_idx = np.argmax(fitness_scores)
        gen_best_fitness = fitness_scores[gen_best_idx]
        
        if gen_best_fitness > best_fitness:
            best_fitness = gen_best_fitness
            best_genome = population[gen_best_idx]
        
        # Progress report
        if gen % 100 == 0:
            avg_fitness = np.mean(fitness_scores)
            print(f"  Gen {gen}: best={best_fitness:.1f}, avg={avg_fitness:.1f}")
            
            # Test best genome
            if best_genome:
                rule, prog = best_genome
                print("    Best results:")
                for a, b in train_cases:
                    expected = a + b
                    result = simulate_addition_concatenated(rule, prog, a, b)
                    status = "✓" if result == expected else "✗"
                    print(f"      {a}+{b}={expected} → {result} {status}")
        
        # Early stopping
        if best_fitness >= 150:  # Perfect score would be 50*3 + 10*3 = 180
            print(f"  Early stopping at generation {gen}!")
            break
        
        # Selection and reproduction
        if gen < GENERATIONS - 1:
            # Tournament selection
            new_population = []
            
            for _ in range(POP_SIZE):
                # Tournament
                tournament_indices = rng.choice(POP_SIZE, size=3, replace=False)
                tournament_fitness = [fitness_scores[i] for i in tournament_indices]
                winner_idx = tournament_indices[np.argmax(tournament_fitness)]
                
                # Mutate winner
                rule, prog = population[winner_idx]
                
                # Mutate rule
                new_rule = rule.copy()
                mask = rng.random(64) < 0.05
                new_rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)
                
                # Mutate program
                new_prog = prog.copy()
                mask = rng.random(L_PROG) < 0.1
                new_prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.8, 0.15, 0.05])
                
                new_population.append((_sanitize_rule(new_rule), _sanitize_programme(new_prog)))
            
            population = new_population
    
    # Final test
    print(f"\n🎯 TRAINING COMPLETE")
    print(f"Best fitness: {best_fitness:.1f}")
    
    if best_genome:
        rule, prog = best_genome
        print("\nFinal test:")
        all_correct = True
        
        for a, b in train_cases:
            expected = a + b
            result = simulate_addition_concatenated(rule, prog, a, b)
            status = "✓" if result == expected else "✗"
            print(f"  {a}+{b}={expected} → {result} {status}")
            if result != expected:
                all_correct = False
        
        if all_correct:
            print("🎉 SUCCESS! Found working addition model!")
            
            # Test generalization
            print("\nGeneralization test:")
            test_cases = [(2, 2), (3, 1), (1, 3), (3, 2)]
            for a, b in test_cases:
                expected = a + b
                result = simulate_addition_concatenated(rule, prog, a, b)
                status = "✓" if result == expected else "✗"
                print(f"  {a}+{b}={expected} → {result} {status}")
            
            # Save model
            with open('simple_addition_model.pkl', 'wb') as f:
                pickle.dump({'rule': rule, 'prog': prog, 'fitness': best_fitness}, f)
            print("💾 Model saved as simple_addition_model.pkl")
            
        else:
            print("❌ Training failed to find perfect solution")
    
    return best_genome, best_fitness

if __name__ == "__main__":
    # Test simple approaches first
    test_simple_approaches()
    
    # Train simple addition
    train_simple_addition()
