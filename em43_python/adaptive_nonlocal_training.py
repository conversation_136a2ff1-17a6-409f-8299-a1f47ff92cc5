"""
Adaptive Nonlocal EM43 Training

Key innovation: Let encoding, decoding, and halting schemes evolve!
- Multiple encoding strategies (standard, commutative, overlapping, etc.)
- Multiple decoding strategies (count zeros, find patterns, etc.) 
- Multiple halting conditions (blue ratio, stability, pattern-based, etc.)
- The GA learns which combination works best for each function
"""

import numpy as np
import time
import pickle
from tqdm import tqdm
from em43_numba import _sanitize_programme, _sanitize_rule

class AdaptiveEM43:
    """EM43 with learnable encoding, decoding, and halting schemes."""
    
    def __init__(self, rule, prog, encoding_params, decoding_params, halting_params):
        self.rule = rule
        self.prog = prog
        self.encoding_params = encoding_params
        self.decoding_params = decoding_params
        self.halting_params = halting_params
    
    def encode_inputs(self, a, b, N=50):
        """Encode inputs using learnable encoding scheme."""
        state = np.zeros(N, dtype=np.uint8)
        L = len(self.prog)
        
        # Write program
        for j in range(min(L, N)):
            state[j] = self.prog[j]
        
        # Separator strategy (learnable)
        sep_type = self.encoding_params['separator_type']
        sep_pos = L
        
        if sep_type == 0:  # BB
            if sep_pos + 1 < N:
                state[sep_pos] = 3
                state[sep_pos + 1] = 3
            base_pos = sep_pos + 2
        elif sep_type == 1:  # B
            if sep_pos < N:
                state[sep_pos] = 3
            base_pos = sep_pos + 1
        elif sep_type == 2:  # BBB (stronger separator)
            for i in range(3):
                if sep_pos + i < N:
                    state[sep_pos + i] = 3
            base_pos = sep_pos + 3
        else:  # No separator
            base_pos = sep_pos
        
        # Input encoding strategy (learnable)
        encoding_type = self.encoding_params['input_encoding']
        
        if encoding_type == 0:  # Standard: 0^a R 0^b R
            # First input
            for i in range(a):
                if base_pos + i < N:
                    state[base_pos + i] = 0
            
            r1_pos = base_pos + a
            if r1_pos < N:
                state[r1_pos] = 2
            
            # Second input
            for i in range(b):
                pos = r1_pos + 1 + i
                if pos < N:
                    state[pos] = 0
            
            r2_pos = r1_pos + 1 + b
            if r2_pos < N:
                state[r2_pos] = 2
                
        elif encoding_type == 1:  # Commutative overlap: inputs can share positions
            # Encode both inputs starting from same base, allowing overlap
            max_input = max(a, b)
            for i in range(max_input):
                if base_pos + i < N:
                    state[base_pos + i] = 0
            
            # Single R after overlapped region
            r_pos = base_pos + max_input
            if r_pos < N:
                state[r_pos] = 2
                
        elif encoding_type == 2:  # Unary with markers: 1^a R 1^b R
            # First input as 1s
            for i in range(a):
                if base_pos + i < N:
                    state[base_pos + i] = 1
            
            r1_pos = base_pos + a
            if r1_pos < N:
                state[r1_pos] = 2
            
            # Second input as 1s
            for i in range(b):
                pos = r1_pos + 1 + i
                if pos < N:
                    state[pos] = 1
            
            r2_pos = r1_pos + 1 + b
            if r2_pos < N:
                state[r2_pos] = 2
                
        elif encoding_type == 3:  # Mixed: 0^a R 1^b R (distinguish inputs)
            # First input as 0s
            for i in range(a):
                if base_pos + i < N:
                    state[base_pos + i] = 0
            
            r1_pos = base_pos + a
            if r1_pos < N:
                state[r1_pos] = 2
            
            # Second input as 1s
            for i in range(b):
                pos = r1_pos + 1 + i
                if pos < N:
                    state[pos] = 1
            
            r2_pos = r1_pos + 1 + b
            if r2_pos < N:
                state[r2_pos] = 2
        
        return state
    
    def check_halting(self, state):
        """Check if computation should halt using learnable condition."""
        N = len(state)
        
        # Count cell types
        counts = [0, 0, 0, 0]  # [blank, program, red, blue]
        for x in state:
            counts[x] += 1
        
        blank, prog, red, blue = counts
        nonzero = prog + red + blue
        
        if nonzero == 0:
            return False
        
        halting_type = self.halting_params['condition_type']
        threshold = self.halting_params['threshold'] / 100.0  # 0-100 -> 0-1
        
        if halting_type == 0:  # Blue ratio (standard)
            return blue / nonzero >= threshold
        elif halting_type == 1:  # Blue + Red ratio (markers dominant)
            return (blue + red) / nonzero >= threshold
        elif halting_type == 2:  # Blue absolute count
            return blue >= int(threshold * 10)  # threshold as absolute count
        elif halting_type == 3:  # Stability-based (low program cell ratio)
            return prog / nonzero <= (1.0 - threshold)
        else:  # Combined condition
            blue_ok = blue / nonzero >= threshold * 0.8
            stable_ok = prog / nonzero <= 0.3
            return blue_ok and stable_ok
    
    def decode_result(self, state):
        """Decode result using learnable decoding scheme."""
        N = len(state)
        decoding_type = self.decoding_params['method']
        
        if decoding_type == 0:  # Count zeros after rightmost R
            rightmost_r = -1
            for i in range(N-1, -1, -1):
                if state[i] == 2:
                    rightmost_r = i
                    break
            
            if rightmost_r == -1:
                return -10
            
            zero_count = 0
            for i in range(rightmost_r + 1, N):
                if state[i] == 0:
                    zero_count += 1
                elif state[i] != 0:
                    break
            return zero_count
            
        elif decoding_type == 1:  # Count zeros between first and last R
            first_r = last_r = -1
            for i in range(N):
                if state[i] == 2:
                    if first_r == -1:
                        first_r = i
                    last_r = i
            
            if first_r == -1 or last_r == -1 or first_r == last_r:
                return -10
            
            zero_count = 0
            for i in range(first_r + 1, last_r):
                if state[i] == 0:
                    zero_count += 1
            return zero_count
            
        elif decoding_type == 2:  # Count all zeros in final state
            return sum(1 for x in state if x == 0)
            
        elif decoding_type == 3:  # Count program cells (1s) in final state
            return sum(1 for x in state if x == 1)
            
        else:  # Count blue cells
            return sum(1 for x in state if x == 3)
    
    def simulate(self, a, b, max_steps=200):
        """Run full simulation with adaptive schemes."""
        N = 60  # Larger window for complex encodings
        state = self.encode_inputs(a, b, N)
        
        # Extract nonlocal rule components
        rule_local = self.rule[0:64]
        rule_skip_left = self.rule[64:128]
        rule_skip_right = self.rule[128:192]
        rule_long_range = self.rule[192:256]
        
        for step in range(max_steps):
            nxt = np.zeros(N, dtype=np.uint8)
            
            # Apply nonlocal rules
            for x in range(1, N - 1):
                left = state[x-1] if x > 0 else 0
                center = state[x]
                right = state[x+1] if x < N-1 else 0
                local_idx = (left << 4) | (center << 2) | right
                
                # Default to local rule
                nxt[x] = rule_local[local_idx]
                
                # Nonlocal rule selection
                if x >= 2 and x < N - 2:
                    left2 = state[x-2]
                    right2 = state[x+2]
                    
                    # Adaptive nonlocal usage based on encoding
                    use_nonlocal = False
                    
                    if center != 0:  # Non-zero center
                        use_nonlocal = True
                        long_range_idx = (left2 << 4) | (center << 2) | right2
                        nxt[x] = rule_long_range[long_range_idx]
                    elif left != 0 and right == 0:  # Left blocking
                        use_nonlocal = True
                        skip_left_idx = (left2 << 4) | (center << 2) | right
                        nxt[x] = rule_skip_left[skip_left_idx]
                    elif right != 0 and left == 0:  # Right blocking
                        use_nonlocal = True
                        skip_right_idx = (left << 4) | (center << 2) | right2
                        nxt[x] = rule_skip_right[skip_right_idx]
            
            # Check adaptive halting condition
            if self.check_halting(nxt):
                return self.decode_result(nxt)
            
            state = nxt
        
        return -10  # Timeout

def create_random_adaptive_em43():
    """Create random adaptive EM43 with all learnable components."""
    rng = np.random.default_rng()
    
    # Create nonlocal rule
    rule = np.zeros(256, dtype=np.uint8)
    for i in range(4):
        base = i * 64
        # Basic patterns
        rule[base + 0] = 0   # 000 -> 0
        rule[base + 2] = 2   # 002 -> 2
        rule[base + 8] = 2   # 020 -> 2
        rule[base + 32] = 0  # 200 -> 0
        
        # Random patterns
        for j in range(64):
            if rng.random() < 0.2:
                rule[base + j] = rng.integers(0, 4)
    
    # Sanitize base rule
    base_rule = rule[0:64]
    base_rule = _sanitize_rule(base_rule)
    rule[0:64] = base_rule
    
    # Program
    prog = rng.choice([0, 1], size=rng.integers(4, 12), p=[0.6, 0.4])
    prog = _sanitize_programme(prog)
    
    # Learnable encoding parameters
    encoding_params = {
        'separator_type': rng.integers(0, 4),  # 0=BB, 1=B, 2=BBB, 3=none
        'input_encoding': rng.integers(0, 4),  # 0=standard, 1=commutative, 2=unary, 3=mixed
    }
    
    # Learnable decoding parameters
    decoding_params = {
        'method': rng.integers(0, 5),  # 0=zeros after R, 1=zeros between Rs, 2=all zeros, 3=count 1s, 4=count blues
    }
    
    # Learnable halting parameters
    halting_params = {
        'condition_type': rng.integers(0, 5),  # Different halting strategies
        'threshold': rng.integers(30, 80),     # Threshold percentage
    }
    
    return AdaptiveEM43(rule, prog, encoding_params, decoding_params, halting_params)

def evaluate_adaptive(em43, test_cases):
    """Evaluate adaptive EM43 on test cases."""
    results = []
    for a, b in test_cases:
        expected = a + b
        result = em43.simulate(a, b)
        correct = (result == expected)
        convergent = (result != -10)
        results.append({
            'a': a, 'b': b, 'expected': expected, 'result': result,
            'correct': correct, 'convergent': convergent
        })
    return results

def extended_adaptive_training():
    """Extended training with adaptive schemes."""
    print("🧬 ADAPTIVE NONLOCAL EM43 TRAINING")
    print("=" * 60)
    print("Learning encoding, decoding, and halting schemes!")
    
    # Extended test cases
    test_cases = [
        (1, 1), (1, 2), (2, 1), (2, 2),
        (1, 3), (3, 1), (2, 3), (3, 2), (3, 3),
        (1, 4), (4, 1), (2, 4), (4, 2),
        (3, 4), (4, 3), (4, 4)
    ]
    
    print(f"Training on {len(test_cases)} cases: {test_cases}")
    
    POP_SIZE = 60
    GENERATIONS = 300
    MUTATION_RATE = 0.06
    
    print(f"Population: {POP_SIZE}, Generations: {GENERATIONS}")
    
    # Initialize population
    population = []
    for i in range(POP_SIZE):
        population.append(create_random_adaptive_em43())
    
    best_accuracy = 0
    best_model = None
    rng = np.random.default_rng()
    
    print("\nTraining adaptive schemes...")
    
    for gen in tqdm(range(GENERATIONS), desc="Adaptive Training"):
        fitness_scores = []
        
        for em43 in population:
            results = evaluate_adaptive(em43, test_cases)
            
            correct = sum(1 for r in results if r['correct'])
            convergent = sum(1 for r in results if r['convergent'])
            
            accuracy = correct / len(test_cases)
            convergence_rate = convergent / len(test_cases)
            
            # Advanced fitness function
            fitness = correct * 1000 + convergent * 200
            
            # Bonus for high accuracy
            if accuracy >= 0.8:
                fitness += 5000
            elif accuracy >= 0.6:
                fitness += 2000
            elif accuracy >= 0.4:
                fitness += 1000
            
            # Bonus for scheme diversity (encourage exploration)
            scheme_bonus = 0
            scheme_bonus += em43.encoding_params['separator_type'] * 10
            scheme_bonus += em43.encoding_params['input_encoding'] * 10
            scheme_bonus += em43.decoding_params['method'] * 10
            fitness += scheme_bonus
            
            # Penalty for timeout cases
            timeouts = sum(1 for r in results if r['result'] == -10)
            fitness -= timeouts * 50
            
            fitness_scores.append(fitness)
        
        # Track best
        best_idx = np.argmax(fitness_scores)
        gen_results = evaluate_adaptive(population[best_idx], test_cases)
        gen_accuracy = sum(1 for r in gen_results if r['correct']) / len(test_cases)
        
        if gen_accuracy > best_accuracy:
            best_accuracy = gen_accuracy
            best_model = population[best_idx]
        
        # Progress report
        if gen % 50 == 0:
            gen_convergence = sum(1 for r in gen_results if r['convergent']) / len(test_cases)
            print(f"  Gen {gen:3d}: acc={gen_accuracy:.1%}, conv={gen_convergence:.1%}, best={best_accuracy:.1%}")
            
            if best_model:
                print(f"    Best schemes: enc={best_model.encoding_params}, dec={best_model.decoding_params}")
        
        # Early stopping
        if best_accuracy >= 0.9:
            print(f"  🎯 Excellent accuracy reached at generation {gen}!")
            break
        
        # Advanced reproduction with scheme crossover
        if gen < GENERATIONS - 1:
            sorted_indices = np.argsort(fitness_scores)[::-1]
            n_elite = POP_SIZE // 4
            
            new_population = []
            
            # Keep elites
            for i in range(n_elite):
                new_population.append(population[sorted_indices[i]])
            
            # Generate offspring with scheme crossover
            while len(new_population) < POP_SIZE:
                # Select two parents
                parent1_idx = rng.choice(sorted_indices[:POP_SIZE//2])
                parent2_idx = rng.choice(sorted_indices[:POP_SIZE//2])
                
                parent1 = population[parent1_idx]
                parent2 = population[parent2_idx]
                
                # Create offspring
                child_rule = parent1.rule.copy()
                child_prog = parent1.prog.copy()
                
                # Crossover schemes
                child_encoding = parent1.encoding_params.copy()
                child_decoding = parent1.decoding_params.copy()
                child_halting = parent1.halting_params.copy()
                
                if rng.random() < 0.5:
                    child_encoding = parent2.encoding_params.copy()
                if rng.random() < 0.5:
                    child_decoding = parent2.decoding_params.copy()
                if rng.random() < 0.5:
                    child_halting = parent2.halting_params.copy()
                
                # Mutate rule
                for i in range(256):
                    if rng.random() < MUTATION_RATE:
                        child_rule[i] = rng.integers(0, 4)
                
                # Re-sanitize base rule
                base_rule = child_rule[0:64]
                base_rule = _sanitize_rule(base_rule)
                child_rule[0:64] = base_rule
                
                # Mutate program
                for i in range(len(child_prog)):
                    if rng.random() < MUTATION_RATE * 2:
                        child_prog[i] = rng.choice([0, 1], p=[0.6, 0.4])
                
                child_prog = _sanitize_programme(child_prog)
                
                # Mutate schemes
                if rng.random() < 0.1:
                    child_encoding['separator_type'] = rng.integers(0, 4)
                if rng.random() < 0.1:
                    child_encoding['input_encoding'] = rng.integers(0, 4)
                if rng.random() < 0.1:
                    child_decoding['method'] = rng.integers(0, 5)
                if rng.random() < 0.1:
                    child_halting['condition_type'] = rng.integers(0, 5)
                if rng.random() < 0.1:
                    child_halting['threshold'] = rng.integers(30, 80)
                
                child = AdaptiveEM43(child_rule, child_prog, child_encoding, child_decoding, child_halting)
                new_population.append(child)
            
            population = new_population
    
    return best_model, best_accuracy, test_cases

if __name__ == "__main__":
    start_time = time.time()
    
    print("🧬 ADAPTIVE NONLOCAL EM43 TRAINING")
    print("Evolving encoding, decoding, and halting schemes")
    print("=" * 60)
    
    # Run extended adaptive training
    best_model, final_accuracy, test_cases = extended_adaptive_training()
    
    total_time = time.time() - start_time
    
    print(f"\n🎯 ADAPTIVE TRAINING COMPLETE!")
    print(f"Final accuracy: {final_accuracy:.1%} on {len(test_cases)} cases")
    print(f"Total time: {total_time/60:.1f} minutes")
    
    if best_model and final_accuracy > 0.3:
        print(f"\n📊 BEST MODEL ANALYSIS:")
        print(f"Encoding scheme: {best_model.encoding_params}")
        print(f"Decoding scheme: {best_model.decoding_params}")
        print(f"Halting scheme: {best_model.halting_params}")
        print(f"Program length: {len(best_model.prog)}")
        
        # Test results
        results = evaluate_adaptive(best_model, test_cases)
        
        print(f"\n📋 DETAILED RESULTS:")
        correct_count = 0
        for r in results:
            status = "✓" if r['correct'] else "✗"
            conv = "C" if r['convergent'] else "NC"
            print(f"  {r['a']}+{r['b']}={r['expected']} → {r['result']} {status} {conv}")
            if r['correct']:
                correct_count += 1
        
        print(f"\nCorrect: {correct_count}/{len(test_cases)} ({correct_count/len(test_cases)*100:.1f}%)")
        
        # Save model
        model_data = {
            'rule': best_model.rule,
            'prog': best_model.prog,
            'encoding_params': best_model.encoding_params,
            'decoding_params': best_model.decoding_params,
            'halting_params': best_model.halting_params,
            'accuracy': final_accuracy,
            'type': 'adaptive_nonlocal',
            'test_cases': test_cases
        }
        
        with open('adaptive_nonlocal_model.pkl', 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"\n💾 Adaptive model saved: adaptive_nonlocal_model.pkl")
    
    print(f"\n🎉 Adaptive scheme evolution demonstrates the power of learnable components!")
    print(f"   Each EM43 function can now discover its optimal encoding/decoding strategy")
