"""
Test extreme generalization to very large numbers
"""

import pickle
import numpy as np
from adaptive_nonlocal_training import AdaptiveEM43
import time

# Load the perfect model
try:
    with open('perfect_adaptive_model.pkl', 'rb') as f:
        model_data = pickle.load(f)
    
    perfect_model = AdaptiveEM43(
        model_data['rule'],
        model_data['prog'],
        model_data['encoding_params'],
        model_data['decoding_params'],
        model_data['halting_params']
    )
    
    print("🚀 EXTREME GENERALIZATION TEST")
    print("=" * 50)
    print("Testing the model on very large numbers to find its limits...")
    
    # Test increasingly large numbers
    test_cases = [
        # Medium large
        (25, 25), (30, 30), (40, 40), (50, 50),
        # Large
        (75, 25), (100, 50), (150, 50), (200, 100),
        # Very large
        (300, 200), (500, 300), (750, 250), (1000, 500),
        # Extreme
        (1500, 500), (2000, 1000), (3000, 2000),
        # Ultra extreme (if you dare!)
        (5000, 2500), (7500, 2500), (10000, 5000)
    ]
    
    print(f"Testing {len(test_cases)} extreme cases...")
    print("(This may take a while for very large numbers)")
    
    results = []
    
    for i, (a, b) in enumerate(test_cases):
        print(f"\n[{i+1:2d}/{len(test_cases)}] Testing {a:5d} + {b:5d} = {a+b:5d}")
        
        start_time = time.time()
        try:
            result = perfect_model.simulate(a, b, max_steps=50000)  # Increase max steps for large numbers
            elapsed = time.time() - start_time
            
            expected = a + b
            correct = (result == expected)
            convergent = (result != -10)
            
            status = "✅" if correct else "❌" if convergent else "⏰"
            
            print(f"    Result: {result:5d} {status} ({elapsed:.2f}s)")
            
            results.append({
                'a': a, 'b': b, 'expected': expected, 'result': result,
                'correct': correct, 'convergent': convergent, 'time': elapsed
            })
            
            # Stop if we hit a failure or timeout
            if not correct:
                print(f"    ❌ First failure detected at {a}+{b}")
                break
            elif elapsed > 30:  # If it takes more than 30 seconds
                print(f"    ⏰ Computation getting slow, may be approaching limits")
                
        except Exception as e:
            print(f"    💥 Error: {e}")
            break
    
    # Analysis
    print(f"\n📊 EXTREME GENERALIZATION ANALYSIS:")
    print("=" * 50)
    
    if results:
        correct_count = sum(1 for r in results if r['correct'])
        convergent_count = sum(1 for r in results if r['convergent'])
        
        print(f"Cases tested: {len(results)}")
        print(f"Correct: {correct_count}/{len(results)} ({correct_count/len(results)*100:.1f}%)")
        print(f"Convergent: {convergent_count}/{len(results)} ({convergent_count/len(results)*100:.1f}%)")
        
        if correct_count == len(results):
            largest_sum = max(r['expected'] for r in results)
            print(f"🎉 PERFECT GENERALIZATION up to sum = {largest_sum}")
            print(f"   The model appears to have learned true addition!")
        else:
            # Find where it starts failing
            first_failure = next((r for r in results if not r['correct']), None)
            if first_failure:
                print(f"⚠️  First failure at {first_failure['a']}+{first_failure['b']}={first_failure['expected']}")
                print(f"   Model gave: {first_failure['result']}")
        
        # Time analysis
        avg_time = sum(r['time'] for r in results) / len(results)
        max_time = max(r['time'] for r in results)
        print(f"\n⏱️  Performance:")
        print(f"   Average time: {avg_time:.2f}s")
        print(f"   Maximum time: {max_time:.2f}s")
        
        # Show largest successful case
        largest_correct = max((r for r in results if r['correct']), key=lambda x: x['expected'], default=None)
        if largest_correct:
            print(f"   Largest successful: {largest_correct['a']}+{largest_correct['b']}={largest_correct['expected']}")
    
    print(f"\n🧬 CONCLUSION:")
    if correct_count == len(results) and len(results) >= 10:
        print("The model shows EXTRAORDINARY generalization capabilities!")
        print("It appears to have learned the fundamental concept of addition.")
        print("Test even larger numbers in the HTML viewer to find the true limits!")
    elif correct_count >= len(results) * 0.8:
        print("The model shows strong generalization with some limitations at extreme scales.")
    else:
        print("The model has generalization limits that become apparent at large scales.")

except FileNotFoundError:
    print("❌ Perfect model not found!")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
