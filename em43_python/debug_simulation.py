"""
Debug the simulation to see why it's only running 2 steps
"""

import pickle
import numpy as np
from adaptive_nonlocal_training import AdaptiveEM43

# Load the perfect model
with open('perfect_adaptive_model.pkl', 'rb') as f:
    model_data = pickle.load(f)

perfect_model = AdaptiveEM43(
    model_data['rule'],
    model_data['prog'],
    model_data['encoding_params'],
    model_data['decoding_params'],
    model_data['halting_params']
)

print("Testing 2+3 simulation step by step:")

# Test encoding
a, b = 2, 3
N = 30
state = perfect_model.encode_inputs(a, b, N)
print(f"Initial state: {' '.join(str(x) if x != 0 else '.' for x in state)}")

# Extract rule components
rule_local = perfect_model.rule[0:64]
rule_skip_left = perfect_model.rule[64:128]
rule_skip_right = perfect_model.rule[128:192]
rule_long_range = perfect_model.rule[192:256]

print(f"\nStep-by-step simulation:")

for step in range(10):
    print(f"Step {step}: {' '.join(str(x) if x != 0 else '.' for x in state[:20])}")
    
    # Check halting
    if perfect_model.check_halting(state):
        result = perfect_model.decode_result(state)
        print(f"HALTED at step {step}: Result = {result}")
        break
    
    # Apply one step
    nxt = np.zeros(N, dtype=np.uint8)
    
    for x in range(1, N - 1):
        left = state[x-1] if x > 0 else 0
        center = state[x]
        right = state[x+1] if x < N-1 else 0
        local_idx = (left << 4) | (center << 2) | right
        
        # Default to local rule
        nxt[x] = rule_local[local_idx]
        
        # Apply nonlocal rules
        if x >= 2 and x < N - 2:
            left2 = state[x-2]
            right2 = state[x+2]
            
            if center != 0:
                long_range_idx = (left2 << 4) | (center << 2) | right2
                nxt[x] = rule_long_range[long_range_idx]
            elif left != 0 and right == 0:
                skip_left_idx = (left2 << 4) | (center << 2) | right
                nxt[x] = rule_skip_left[skip_left_idx]
            elif right != 0 and left == 0:
                skip_right_idx = (left << 4) | (center << 2) | right2
                nxt[x] = rule_skip_right[skip_right_idx]
    
    state = nxt

print(f"\nFinal result using model.simulate(): {perfect_model.simulate(a, b)}")

# Test halting condition details
print(f"\nHalting condition analysis:")
print(f"Halting params: {perfect_model.halting_params}")

test_state = state
live = sum(1 for x in test_state if x > 0)
prog = sum(1 for x in test_state if x == 1)
blue = sum(1 for x in test_state if x == 3)
red = sum(1 for x in test_state if x == 2)

print(f"Live cells: {live}")
print(f"Program cells: {prog}")
print(f"Blue cells: {blue}")
print(f"Red cells: {red}")

if live > 0:
    prog_ratio = prog / live
    print(f"Program ratio: {prog_ratio:.3f}")
    print(f"Halting threshold: {1.0 - perfect_model.halting_params['threshold']/100:.3f}")
    print(f"Should halt: {prog_ratio <= 0.31}")
