"""
Test the generalization capabilities of the perfect addition model
"""

import pickle
import numpy as np
from adaptive_nonlocal_training import AdaptiveEM43, evaluate_adaptive

# Load the perfect model
try:
    with open('perfect_adaptive_model.pkl', 'rb') as f:
        model_data = pickle.load(f)
    
    perfect_model = AdaptiveEM43(
        model_data['rule'],
        model_data['prog'],
        model_data['encoding_params'],
        model_data['decoding_params'],
        model_data['halting_params']
    )
    
    print("🧪 TESTING GENERALIZATION OF PERFECT ADDITION MODEL")
    print("=" * 60)
    print(f"Trained on: 1-7 range (49 cases)")
    print(f"Testing generalization to larger numbers...")
    
    # Test cases beyond training range
    test_ranges = [
        ("Training range (1-7)", [(a, b) for a in range(1, 8) for b in range(1, 8)]),
        ("Extended range (1-10)", [(a, b) for a in range(8, 11) for b in range(1, 11)]),
        ("Large numbers (10-15)", [(a, b) for a in range(10, 16) for b in range(10, 16)]),
        ("Very large (15-20)", [(15, 15), (16, 16), (17, 17), (18, 18), (19, 19), (20, 20)]),
        ("Mixed large/small", [(1, 15), (15, 1), (2, 18), (18, 2), (5, 20), (20, 5)]),
    ]
    
    overall_results = {}
    
    for range_name, test_cases in test_ranges:
        print(f"\n📊 {range_name}:")
        print(f"Testing {len(test_cases)} cases...")
        
        results = evaluate_adaptive(perfect_model, test_cases)
        
        correct = sum(1 for r in results if r['correct'])
        convergent = sum(1 for r in results if r['convergent'])
        accuracy = correct / len(test_cases)
        convergence_rate = convergent / len(test_cases)
        
        print(f"  Accuracy: {correct}/{len(test_cases)} ({accuracy:.1%})")
        print(f"  Convergence: {convergent}/{len(test_cases)} ({convergence_rate:.1%})")
        
        overall_results[range_name] = {
            'accuracy': accuracy,
            'convergence': convergence_rate,
            'correct': correct,
            'total': len(test_cases)
        }
        
        # Show some examples
        if accuracy < 1.0:
            failing = [r for r in results if not r['correct']][:5]
            print(f"  Sample failures: {[(r['a'], r['b'], r['expected'], r['result']) for r in failing]}")
        else:
            sample_correct = results[:3]
            print(f"  Sample results: {[(r['a'], r['b'], r['result']) for r in sample_correct]}")
    
    print(f"\n🎯 GENERALIZATION SUMMARY:")
    print("=" * 40)
    
    for range_name, data in overall_results.items():
        status = "✅" if data['accuracy'] >= 0.9 else "⚠️" if data['accuracy'] >= 0.5 else "❌"
        print(f"{status} {range_name}: {data['accuracy']:.1%} ({data['correct']}/{data['total']})")
    
    # Test some specific interesting cases
    print(f"\n🔍 SPECIFIC INTERESTING CASES:")
    interesting_cases = [
        (8, 8), (9, 9), (10, 10),  # Large equal numbers
        (1, 20), (20, 1),          # Very asymmetric
        (12, 13), (13, 12),        # Large commutative pair
        (15, 16), (16, 15),        # Very large commutative pair
    ]
    
    for a, b in interesting_cases:
        result = perfect_model.simulate(a, b)
        expected = a + b
        status = "✅" if result == expected else "❌"
        convergent = "C" if result != -10 else "NC"
        print(f"  {a:2d}+{b:2d}={expected:2d} → {result:2d} {status} {convergent}")
    
    print(f"\n🧬 MODEL ANALYSIS:")
    print(f"The model was trained on 1-7 range (49 cases) with 100% accuracy.")
    print(f"Generalization testing reveals how well the learned nonlocal rules")
    print(f"extend to cases beyond the training distribution.")
    
    if overall_results.get("Extended range (1-10)", {}).get('accuracy', 0) >= 0.8:
        print(f"✅ Strong generalization: Model extends well beyond training range!")
    elif overall_results.get("Extended range (1-10)", {}).get('accuracy', 0) >= 0.5:
        print(f"⚠️  Partial generalization: Some success but degradation on larger numbers")
    else:
        print(f"❌ Limited generalization: Model may be overfitted to training range")

except FileNotFoundError:
    print("❌ Perfect model not found! Run the training first.")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
