"""
Extended Addition Trainer - Train on larger numbers to generalize past 6
"""

import numpy as np
import time
from tqdm import tqdm
import pickle
from em43_numba import _simulate, _sanitize_rule, _sanitize_programme

def simulate_addition_extended(rule, prog, a, b, window=400, max_steps=800):
    """
    Extended addition simulation with larger window and more steps for bigger numbers
    """
    L = len(prog)
    N = window
    
    # Initialize state
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(L):
        state[j] = prog[j]
    state[L] = 3      # B
    state[L + 1] = 3  # B
    
    # Write inputs: 0^(a+1) R 0^(b+1) R
    r1_pos = L + 2 + a + 1
    if r1_pos >= N:
        return -10
    state[r1_pos] = 2
    
    r2_pos = r1_pos + 1 + b + 1
    if r2_pos >= N:
        return -10
    state[r2_pos] = 2
    
    # Simulation range - extended for larger numbers
    sim_range = min(N - 1, r2_pos + max(a + b + 20, 50))
    
    # Run CA simulation
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(1, sim_range):
            idx = (state[x-1] << 4) | (state[x] << 2) | state[x+1]
            nxt[x] = rule[idx]
        
        # Pattern-based halting: R...R with only zeros/blues between
        r_positions = [x for x in range(sim_range) if nxt[x] == 2]
        
        if len(r_positions) >= 2:
            # Check if region between Rs contains only zeros and blues
            start, end = r_positions[0], r_positions[-1]
            between_valid = all(nxt[x] in [0, 3] for x in range(start + 1, end))
            
            if between_valid:
                # Decode: count zeros between R markers
                zero_count = sum(1 for x in range(start + 1, end) if nxt[x] == 0)
                return zero_count
        
        state = nxt
    
    return -10  # Didn't halt

def evaluate_extended_model(rule, prog, test_cases):
    """Evaluate model on extended test cases"""
    correct = 0
    total = len(test_cases)
    results = []
    
    for a, b in test_cases:
        result = simulate_addition_extended(rule, prog, a, b)
        expected = a + b
        is_correct = result == expected
        if is_correct:
            correct += 1
        results.append((a, b, expected, result, is_correct))
    
    return correct / total, results

def train_extended_addition():
    """Train a model on extended cases including larger numbers"""
    
    # Extended training cases - focus on the problematic range
    train_cases = [
        # Small cases (baseline)
        (1, 1), (1, 2), (2, 1), (2, 2), (1, 3), (3, 1), (2, 3), (3, 2),
        # Medium cases
        (3, 3), (1, 4), (4, 1), (2, 4), (4, 2), (3, 4), (4, 3), (4, 4),
        # Larger cases - focus on first number >= 6
        (5, 1), (1, 5), (5, 2), (2, 5), (5, 3), (3, 5),
        (6, 1), (1, 6), (6, 2), (2, 6), (6, 3), (3, 6),
        (7, 1), (1, 7), (7, 2), (2, 7), (8, 1), (1, 8)
    ]
    
    # Test cases - even more extended
    test_cases = [
        # All training cases
        *train_cases,
        # Additional test cases
        (9, 1), (1, 9), (10, 1), (1, 10), (5, 5), (6, 6), (7, 3), (3, 7)
    ]
    
    print("Training extended addition model...")
    print(f"Training cases: {len(train_cases)}")
    print(f"Test cases: {len(test_cases)}")
    
    best_accuracy = 0
    best_model = None
    best_results = None
    
    # Start from existing model if available
    try:
        with open('improved_addition_model.pkl', 'rb') as f:
            base_model = pickle.load(f)
        base_rule, base_prog = base_model['rule'], base_model['prog']
        print("Starting from existing improved model...")
    except:
        base_rule = np.random.randint(0, 4, 64, dtype=np.uint8)
        base_prog = np.random.randint(0, 4, 12, dtype=np.uint8)
        print("Starting from random model...")
    
    # Training loop with smaller batches
    for trial in tqdm(range(500), desc="Training"):  # Smaller batch size
        # Start from base model and mutate
        if trial == 0:
            rule, prog = base_rule.copy(), base_prog.copy()
        else:
            rule, prog = base_rule.copy(), base_prog.copy()
            
            # Mutate rule (small changes)
            if np.random.random() < 0.3:
                num_mutations = np.random.randint(1, 4)
                for _ in range(num_mutations):
                    idx = np.random.randint(64)
                    rule[idx] = np.random.randint(0, 4)
            
            # Mutate program (small changes)
            if np.random.random() < 0.2:
                num_mutations = np.random.randint(1, 3)
                for _ in range(num_mutations):
                    idx = np.random.randint(12)
                    prog[idx] = np.random.randint(0, 4)
        
        # Test on training cases
        train_accuracy, train_results = evaluate_extended_model(rule, prog, train_cases)
        
        if train_accuracy > best_accuracy:
            best_accuracy = train_accuracy
            best_model = (rule.copy(), prog.copy())
            
            print(f"\nTrial {trial}: New best training accuracy {train_accuracy:.3f}")
            
            # Test on extended cases
            test_accuracy, test_results = evaluate_extended_model(rule, prog, test_cases)
            print(f"Extended test accuracy: {test_accuracy:.3f}")
            best_results = test_results
            
            # Show some key results
            print("Key results:")
            key_cases = [(6, 1), (1, 6), (7, 1), (1, 7), (8, 1), (1, 8)]
            for a, b, expected, result, correct in test_results:
                if (a, b) in key_cases:
                    status = '✓' if correct else '✗'
                    print(f"  {a}+{b} = {result} (expected {expected}) {status}")
            
            # Save intermediate results
            if train_accuracy >= 0.8:  # Good threshold
                model_data = {
                    'rule': rule,
                    'prog': prog,
                    'train_accuracy': train_accuracy,
                    'test_accuracy': test_accuracy,
                    'train_cases': train_cases,
                    'test_cases': test_cases,
                    'results': test_results
                }
                
                with open('extended_addition_model.pkl', 'wb') as f:
                    pickle.dump(model_data, f)
                
                print(f"Intermediate model saved with {train_accuracy:.3f} accuracy")
                
                if train_accuracy >= 0.95:  # Very good threshold
                    print("Excellent accuracy reached, stopping training")
                    break
    
    if best_model:
        rule, prog = best_model
        
        # Final evaluation
        print(f"\nFinal model - Training accuracy: {best_accuracy:.3f}")
        
        # Show detailed results for problematic cases
        print("\nDetailed results for first number >= 6:")
        for a, b, expected, result, correct in best_results:
            if a >= 6:
                status = '✓' if correct else '✗'
                print(f"  {a}+{b} = {result} (expected {expected}) {status}")
        
        # Save final model
        model_data = {
            'rule': rule,
            'prog': prog,
            'train_accuracy': best_accuracy,
            'train_cases': train_cases,
            'test_cases': test_cases,
            'results': best_results,
            'encoding': 'original',
            'strategy': 'extended_training'
        }
        
        with open('extended_addition_model.pkl', 'wb') as f:
            pickle.dump(model_data, f)
        
        print("Final model saved as 'extended_addition_model.pkl'")
        
        return rule, prog
    else:
        print("No good model found!")
        return None, None

if __name__ == "__main__":
    train_extended_addition()
