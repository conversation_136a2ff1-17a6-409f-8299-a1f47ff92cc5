"""
Test generalization using the corrected manual simulation (like HTML viewer)
"""

import pickle
import numpy as np
from adaptive_nonlocal_training import AdaptiveEM43
import time

def manual_simulate(model, a, b, N=300, max_steps=15000):
    """Manual simulation that matches the HTML viewer logic"""
    state = model.encode_inputs(a, b, N)
    
    # Extract rule components
    rule_local = model.rule[0:64]
    rule_skip_left = model.rule[64:128]
    rule_skip_right = model.rule[128:192]
    rule_long_range = model.rule[192:256]
    
    steps = 0
    while steps < max_steps:
        # Check halting
        if model.check_halting(state):
            return model.decode_result(state)
        
        # Apply step
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(1, N - 1):
            left = state[x-1] if x > 0 else 0
            center = state[x]
            right = state[x+1] if x < N-1 else 0
            local_idx = (left << 4) | (center << 2) | right
            
            # Default to local rule
            nxt[x] = rule_local[local_idx]
            
            # Apply nonlocal rules
            if x >= 2 and x < N - 2:
                left2 = state[x-2]
                right2 = state[x+2]
                
                if center != 0:
                    long_range_idx = (left2 << 4) | (center << 2) | right2
                    nxt[x] = rule_long_range[long_range_idx]
                elif left != 0 and right == 0:
                    skip_left_idx = (left2 << 4) | (center << 2) | right
                    nxt[x] = rule_skip_left[skip_left_idx]
                elif right != 0 and left == 0:
                    skip_right_idx = (left << 4) | (center << 2) | right2
                    nxt[x] = rule_skip_right[skip_right_idx]
        
        state = nxt
        steps += 1
    
    return -10  # Timeout

# Load the perfect model
try:
    with open('perfect_adaptive_model.pkl', 'rb') as f:
        model_data = pickle.load(f)
    
    perfect_model = AdaptiveEM43(
        model_data['rule'],
        model_data['prog'],
        model_data['encoding_params'],
        model_data['decoding_params'],
        model_data['halting_params']
    )
    
    print("🚀 CORRECTED EXTREME GENERALIZATION TEST")
    print("=" * 60)
    print("Using manual simulation that matches HTML viewer logic")
    
    # Test increasingly large numbers
    test_cases = [
        # Verify the fix first
        (30, 30), (35, 35), (40, 40), (45, 45), (50, 50),
        # Medium large
        (60, 40), (75, 25), (100, 50), (150, 50), (200, 100),
        # Large
        (300, 200), (400, 300), (500, 400), (750, 250),
        # Very large
        (1000, 500), (1500, 500), (2000, 1000),
        # Extreme (if it gets this far!)
        (3000, 2000), (5000, 2500), (7500, 2500), (10000, 5000)
    ]
    
    print(f"Testing {len(test_cases)} cases with corrected simulation...")
    
    results = []
    
    for i, (a, b) in enumerate(test_cases):
        print(f"\n[{i+1:2d}/{len(test_cases)}] Testing {a:5d} + {b:5d} = {a+b:5d}")
        
        start_time = time.time()
        try:
            result = manual_simulate(perfect_model, a, b)
            elapsed = time.time() - start_time
            
            expected = a + b
            correct = (result == expected)
            convergent = (result != -10)
            
            status = "✅" if correct else "❌" if convergent else "⏰"
            
            print(f"    Result: {result:5d} {status} ({elapsed:.2f}s)")
            
            results.append({
                'a': a, 'b': b, 'expected': expected, 'result': result,
                'correct': correct, 'convergent': convergent, 'time': elapsed
            })
            
            # Stop if we hit a failure or very slow computation
            if not correct:
                print(f"    ❌ First failure detected at {a}+{b}")
                break
            elif elapsed > 60:  # If it takes more than 1 minute
                print(f"    ⏰ Computation very slow, may be approaching limits")
                break
                
        except Exception as e:
            print(f"    💥 Error: {e}")
            break
    
    # Analysis
    print(f"\n📊 CORRECTED GENERALIZATION ANALYSIS:")
    print("=" * 60)
    
    if results:
        correct_count = sum(1 for r in results if r['correct'])
        convergent_count = sum(1 for r in results if r['convergent'])
        
        print(f"Cases tested: {len(results)}")
        print(f"Correct: {correct_count}/{len(results)} ({correct_count/len(results)*100:.1f}%)")
        print(f"Convergent: {convergent_count}/{len(results)} ({convergent_count/len(results)*100:.1f}%)")
        
        if correct_count == len(results):
            largest_sum = max(r['expected'] for r in results)
            largest_case = max(results, key=lambda x: x['expected'])
            print(f"🎉 PERFECT GENERALIZATION up to sum = {largest_sum}")
            print(f"   Largest case: {largest_case['a']}+{largest_case['b']}={largest_case['expected']}")
            print(f"   The model has learned TRUE ADDITION!")
        else:
            # Find where it starts failing
            first_failure = next((r for r in results if not r['correct']), None)
            if first_failure:
                print(f"⚠️  First failure at {first_failure['a']}+{first_failure['b']}={first_failure['expected']}")
                print(f"   Model gave: {first_failure['result']}")
        
        # Time analysis
        avg_time = sum(r['time'] for r in results) / len(results)
        max_time = max(r['time'] for r in results)
        print(f"\n⏱️  Performance:")
        print(f"   Average time: {avg_time:.2f}s")
        print(f"   Maximum time: {max_time:.2f}s")
    
    print(f"\n🧬 CONCLUSION:")
    if results and correct_count == len(results):
        print("🎉 EXTRAORDINARY GENERALIZATION CONFIRMED!")
        print("The HTML viewer was correct - the model generalizes far beyond initial tests!")
        print("The Python simulate() method had a bug that made it appear limited.")
        print("Test even larger numbers in the HTML viewer to find the true limits!")
    else:
        print("The model shows impressive but finite generalization capabilities.")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
