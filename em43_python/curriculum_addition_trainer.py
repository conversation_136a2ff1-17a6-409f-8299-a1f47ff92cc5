"""
Curriculum Addition Trainer - Scaling to larger numbers systematically
"""

import numpy as np
import time
import pickle
from tqdm import tqdm
from pathlib import Path
from em43_numba import _sanitize_rule, _sanitize_programme

def simulate_addition_optimized(rule, prog, a, b, window=300, max_steps=600, halt_thresh=0.5):
    """
    Optimized addition simulation with adaptive parameters.
    """
    L = len(prog)
    N = window
    
    # Initialize state
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(L):
        state[j] = prog[j]
    state[L] = 3      # B
    state[L + 1] = 3  # B
    
    # Write inputs: 0^(a+1) R 0^(b+1) R
    r1_pos = L + 2 + a + 1
    if r1_pos >= N:
        return -10  # Not enough space
    state[r1_pos] = 2
    
    r2_pos = r1_pos + 1 + b + 1
    if r2_pos >= N:
        return -10  # Not enough space
    state[r2_pos] = 2
    
    # Adaptive simulation range
    sim_range = min(N - 1, r2_pos + max(a + b, 20))
    
    # Run CA simulation
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        
        # Update cells in active range
        for x in range(1, sim_range):
            idx = (state[x-1] << 4) | (state[x] << 2) | state[x+1]
            nxt[x] = rule[idx]
        
        # Halting check in active range
        live = blue = 0
        for x in range(sim_range):
            v = nxt[x]
            if v != 0:
                live += 1
                if v == 3:
                    blue += 1
        
        if live > 0 and blue / live >= halt_thresh:
            # Find rightmost R
            rpos = -1
            for x in range(sim_range - 1, -1, -1):
                if nxt[x] == 2:
                    rpos = x
                    break
            
            if rpos != -1:
                return rpos - (L + 3)
            else:
                return -10
        
        state = nxt
    
    return -10

# Curriculum stages with progressive difficulty
CURRICULUM_STAGES = {
    'stage1': {
        'name': 'Basic Addition',
        'cases': [(1, 1), (1, 2), (2, 1)],
        'target_accuracy': 1.0,
        'window': 200,
        'max_steps': 400,
        'generations': 100,
        'mutation_rate': 0.02
    },
    'stage2': {
        'name': 'Small Numbers',
        'cases': [(1, 1), (1, 2), (2, 1), (2, 2), (3, 1), (1, 3)],
        'target_accuracy': 1.0,
        'window': 250,
        'max_steps': 500,
        'generations': 150,
        'mutation_rate': 0.015
    },
    'stage3': {
        'name': 'Medium Numbers',
        'cases': [(1, 1), (1, 2), (2, 1), (2, 2), (3, 1), (1, 3), (3, 2), (2, 3), (3, 3), (4, 1), (1, 4)],
        'target_accuracy': 0.95,
        'window': 300,
        'max_steps': 600,
        'generations': 200,
        'mutation_rate': 0.01
    },
    'stage4': {
        'name': 'Larger Numbers',
        'cases': [(i, j) for i in range(1, 6) for j in range(1, 6)],
        'target_accuracy': 0.9,
        'window': 400,
        'max_steps': 800,
        'generations': 250,
        'mutation_rate': 0.008
    },
    'stage5': {
        'name': 'Extended Range',
        'cases': [(i, j) for i in range(1, 8) for j in range(1, 8) if i + j <= 12],
        'target_accuracy': 0.85,
        'window': 500,
        'max_steps': 1000,
        'generations': 300,
        'mutation_rate': 0.005
    }
}

def evaluate_model(rule, prog, test_cases, stage_params):
    """Evaluate model on test cases."""
    correct = 0
    convergent = 0
    total = len(test_cases)
    
    results = []
    
    for a, b in test_cases:
        expected = a + b
        result = simulate_addition_optimized(rule, prog, a, b, 
                                           stage_params['window'], 
                                           stage_params['max_steps'])
        
        results.append((a, b, expected, result))
        
        if result != -10:
            convergent += 1
            if result == expected:
                correct += 1
    
    accuracy = correct / total if total > 0 else 0
    convergence_rate = convergent / total if total > 0 else 0
    
    return accuracy, convergence_rate, results

def train_curriculum_stage(stage_name, stage_config, initial_model=None):
    """Train a single curriculum stage."""
    print(f"\n{'='*60}")
    print(f"🎯 CURRICULUM STAGE: {stage_name.upper()}")
    print(f"{'='*60}")
    print(f"Description: {stage_config['name']}")
    print(f"Test cases: {len(stage_config['cases'])}")
    print(f"Target accuracy: {stage_config['target_accuracy']:.1%}")
    print(f"Parameters: window={stage_config['window']}, steps={stage_config['max_steps']}")
    
    # Initialize population
    POP_SIZE = 50
    rng = np.random.default_rng()
    
    if initial_model is None:
        # Load the basic addition model from previous success
        try:
            with open('addition_model.pkl', 'rb') as f:
                model_data = pickle.load(f)
            initial_rule = model_data['rule']
            initial_prog = model_data['prog']
            print("✅ Starting from previous successful model")
        except:
            print("❌ No previous model found, starting from divide-by-2 model")
            with open('best_genome.pkl', 'rb') as f:
                model_data = pickle.load(f)
            if isinstance(model_data, dict):
                initial_rule = model_data['rule']
                initial_prog = model_data['prog']
            else:
                initial_rule, initial_prog = model_data
    else:
        initial_rule, initial_prog = initial_model
        print("✅ Starting from provided model")
    
    # Create population around initial model
    population = []
    for i in range(POP_SIZE):
        rule = initial_rule.copy()
        prog = initial_prog.copy()
        
        if i > 0:  # Keep one copy unchanged
            # Small mutations
            mask = rng.random(64) < stage_config['mutation_rate']
            rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)
            
            mask = rng.random(len(prog)) < stage_config['mutation_rate'] * 2
            prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.8, 0.15, 0.05])
        
        rule = _sanitize_rule(rule)
        prog = _sanitize_programme(prog)
        population.append((rule, prog))
    
    # Training loop
    best_accuracy = 0
    best_model = None
    best_fitness = -1000
    
    generations = stage_config['generations']
    target_accuracy = stage_config['target_accuracy']
    
    print(f"Training for up to {generations} generations...")
    
    for gen in tqdm(range(generations), desc=f"Stage {stage_name}"):
        # Evaluate population
        fitness_scores = []
        
        for rule, prog in population:
            accuracy, convergence_rate, _ = evaluate_model(rule, prog, stage_config['cases'], stage_config)
            
            # Fitness function: prioritize accuracy, then convergence
            fitness = accuracy * 1000 + convergence_rate * 100
            
            # Bonus for perfect accuracy
            if accuracy == 1.0:
                fitness += 500
            
            fitness_scores.append(fitness)
        
        # Track best
        gen_best_idx = np.argmax(fitness_scores)
        gen_best_fitness = fitness_scores[gen_best_idx]
        gen_best_accuracy, gen_best_convergence, gen_best_results = evaluate_model(
            population[gen_best_idx][0], population[gen_best_idx][1], 
            stage_config['cases'], stage_config)
        
        if gen_best_accuracy > best_accuracy:
            best_accuracy = gen_best_accuracy
            best_model = population[gen_best_idx]
            best_fitness = gen_best_fitness
        
        # Progress report
        if gen % 50 == 0 or gen < 10:
            avg_fitness = np.mean(fitness_scores)
            print(f"  Gen {gen}: best_acc={best_accuracy:.2%}, "
                  f"conv={gen_best_convergence:.2%}, fitness={gen_best_fitness:.1f}")
            
            # Show some examples
            if gen % 100 == 0:
                print("    Sample results:")
                for i, (a, b, expected, result) in enumerate(gen_best_results[:5]):
                    status = "✓" if result == expected else "✗"
                    print(f"      {a}+{b}={expected} → {result} {status}")
        
        # Early stopping if target reached
        if best_accuracy >= target_accuracy:
            print(f"  🎯 Target accuracy {target_accuracy:.1%} reached at generation {gen}!")
            break
        
        # Selection and reproduction
        if gen < generations - 1:
            # Elite selection
            sorted_indices = np.argsort(fitness_scores)[::-1]
            n_elite = POP_SIZE // 4
            
            new_population = []
            
            # Keep elites
            for i in range(n_elite):
                new_population.append(population[sorted_indices[i]])
            
            # Generate offspring
            while len(new_population) < POP_SIZE:
                # Tournament selection
                tournament_size = 3
                tournament_indices = rng.choice(sorted_indices[:POP_SIZE//2], size=tournament_size, replace=False)
                parent_idx = tournament_indices[0]  # Best in tournament
                
                rule, prog = population[parent_idx]
                
                # Mutate
                new_rule = rule.copy()
                new_prog = prog.copy()
                
                # Adaptive mutation rate
                mutation_rate = stage_config['mutation_rate']
                if gen > generations // 2:
                    mutation_rate *= 0.5  # Reduce mutation in later generations
                
                mask = rng.random(64) < mutation_rate
                new_rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)
                
                mask = rng.random(len(prog)) < mutation_rate * 2
                new_prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.8, 0.15, 0.05])
                
                new_population.append((_sanitize_rule(new_rule), _sanitize_programme(new_prog)))
            
            population = new_population
    
    # Final evaluation
    final_accuracy, final_convergence, final_results = evaluate_model(
        best_model[0], best_model[1], stage_config['cases'], stage_config)
    
    print(f"\n📊 STAGE {stage_name.upper()} RESULTS:")
    print(f"  Final accuracy: {final_accuracy:.2%}")
    print(f"  Convergence rate: {final_convergence:.2%}")
    print(f"  Best fitness: {best_fitness:.1f}")
    
    # Show detailed results
    correct_count = sum(1 for _, _, expected, result in final_results if result == expected)
    print(f"  Correct: {correct_count}/{len(final_results)}")
    
    # Show some examples
    print("  Sample results:")
    for i, (a, b, expected, result) in enumerate(final_results[:8]):
        status = "✓" if result == expected else "✗"
        print(f"    {a}+{b}={expected} → {result} {status}")
    
    # Save stage checkpoint
    checkpoint = {
        'stage': stage_name,
        'model': best_model,
        'accuracy': final_accuracy,
        'convergence': final_convergence,
        'config': stage_config,
        'results': final_results
    }
    
    checkpoint_path = f"curriculum_checkpoint_{stage_name}.pkl"
    with open(checkpoint_path, 'wb') as f:
        pickle.dump(checkpoint, f)
    print(f"  💾 Checkpoint saved: {checkpoint_path}")
    
    return best_model, final_accuracy, final_convergence

def run_full_curriculum():
    """Run the complete curriculum training."""
    print("🚀 CURRICULUM ADDITION TRAINING")
    print("=" * 80)
    print("Training addition models with progressive difficulty")
    print("Starting from successful basic model and scaling up")
    
    start_time = time.time()
    current_model = None
    all_results = {}
    
    # Train through all curriculum stages
    for stage_name, stage_config in CURRICULUM_STAGES.items():
        model, accuracy, convergence = train_curriculum_stage(stage_name, stage_config, current_model)
        
        all_results[stage_name] = {
            'model': model,
            'accuracy': accuracy,
            'convergence': convergence,
            'config': stage_config
        }
        
        # Use this model as starting point for next stage
        current_model = model
        
        # Check if we should continue
        if accuracy < stage_config['target_accuracy'] * 0.8:  # If we're far from target
            print(f"⚠️  Stage {stage_name} didn't reach sufficient accuracy. Consider:")
            print(f"   - Increasing generations")
            print(f"   - Adjusting mutation rate")
            print(f"   - Simplifying the problem")
            break
    
    end_time = time.time()
    
    # Final comprehensive test
    print(f"\n{'='*80}")
    print("🧪 COMPREHENSIVE FINAL EVALUATION")
    print(f"{'='*80}")
    
    if current_model:
        final_rule, final_prog = current_model
        
        # Test on various ranges
        test_ranges = [
            ("Basic", [(1, 1), (1, 2), (2, 1), (2, 2)]),
            ("Small", [(i, j) for i in range(1, 4) for j in range(1, 4)]),
            ("Medium", [(i, j) for i in range(1, 6) for j in range(1, 6) if i + j <= 8]),
            ("Large", [(i, j) for i in range(1, 8) for j in range(1, 8) if i + j <= 12]),
            ("Generalization", [(8, 2), (9, 1), (6, 6), (10, 2), (7, 5)])
        ]
        
        for range_name, test_cases in test_ranges:
            accuracy, convergence, results = evaluate_model(final_rule, final_prog, test_cases, 
                                                           CURRICULUM_STAGES['stage5'])
            
            correct = sum(1 for _, _, expected, result in results if result == expected)
            print(f"\n{range_name} Range: {correct}/{len(test_cases)} correct ({accuracy:.1%})")
            
            # Show some examples
            for i, (a, b, expected, result) in enumerate(results[:5]):
                status = "✓" if result == expected else "✗"
                print(f"  {a}+{b}={expected} → {result} {status}")
        
        # Save final model
        final_model = {
            'rule': final_rule,
            'prog': final_prog,
            'curriculum_results': all_results,
            'training_time': end_time - start_time
        }
        
        with open('final_curriculum_addition_model.pkl', 'wb') as f:
            pickle.dump(final_model, f)
        
        print(f"\n🎉 CURRICULUM TRAINING COMPLETE!")
        print(f"⏱️  Total training time: {(end_time - start_time)/60:.1f} minutes")
        print(f"💾 Final model saved: final_curriculum_addition_model.pkl")
        
        return final_model
    
    else:
        print("❌ Training failed - no model produced")
        return None

if __name__ == "__main__":
    final_model = run_full_curriculum()
    
    if final_model:
        print("\n✅ SUCCESS: Curriculum learning successfully scaled addition model!")
        print("🎯 Key achievements:")
        print("   - Progressive difficulty scaling")
        print("   - Transfer learning between stages")
        print("   - Adaptive parameters per stage")
        print("   - Comprehensive evaluation")
        print("\n🚀 Ready for next challenge: Apply to more complex operations!")
    else:
        print("\n⚠️  Training needs refinement, but approach is validated!")
