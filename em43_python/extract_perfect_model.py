"""
Extract the perfect model data for the HTML viewer
"""

import pickle
import numpy as np

try:
    with open('perfect_adaptive_model.pkl', 'rb') as f:
        model_data = pickle.load(f)
    
    print("Perfect model found!")
    print(f"Rule shape: {model_data['rule'].shape if hasattr(model_data['rule'], 'shape') else len(model_data['rule'])}")
    print(f"Program: {model_data['prog']}")
    print(f"Encoding: {model_data['encoding_params']}")
    print(f"Decoding: {model_data['decoding_params']}")
    print(f"Halting: {model_data['halting_params']}")
    print(f"Accuracy: {model_data['accuracy']}")
    
    # Convert rule to list for JavaScript
    rule_list = model_data['rule'].tolist()
    prog_list = model_data['prog'].tolist()
    
    print(f"\nRule (first 20): {rule_list[:20]}")
    print(f"Program: {prog_list}")
    
except FileNotFoundError:
    print("Perfect model not found!")
