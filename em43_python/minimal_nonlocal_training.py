"""
Minimal Nonlocal Commutative Training

Focus on getting basic improvement with very simple cases.
Start with just 1+1=2 and 1+2=3 to see if the approach works at all.
"""

import numpy as np
import time
from tqdm import tqdm
from em43_numba import _sanitize_programme

def _sanitize_nonlocal_rule(rule):
    """Sanitize nonlocal rule array (4 x 64 = 256 entries total)."""
    rule = rule.copy().astype(np.uint8)
    rule = rule.reshape(4, 64)
    
    # Each sub-rule follows standard EM43 constraints
    for i in range(4):
        rule[i] = rule[i] & 0x03  # Ensure values are 0-3
        
        # Apply key immutable constraints - STRONGER R preservation
        immutable_patterns = [
            ((0<<4)|(0<<2)|0, 0),      # 000 -> 0
            ((0<<4)|(2<<2)|0, 2),      # 020 -> 2 (R preservation)
            ((0<<4)|(0<<2)|2, 0),      # 002 -> 0
            ((2<<4)|(0<<2)|0, 0),      # 200 -> 0
            ((2<<4)|(2<<2)|0, 2),      # 220 -> 2 (stronger R preservation)
            ((0<<4)|(2<<2)|2, 2),      # 022 -> 2 (stronger R preservation)
            ((2<<4)|(2<<2)|2, 2),      # 222 -> 2 (strongest R preservation)
            ((0<<4)|(3<<2)|3, 3),      # 033 -> 3 (B preservation)
            ((3<<4)|(3<<2)|0, 3),      # 330 -> 3
            ((3<<4)|(3<<2)|3, 3),      # 333 -> 3 (stronger B preservation)
        ]
        
        for pattern, value in immutable_patterns:
            rule[i][pattern] = value
    
    return rule.flatten()

def simulate_minimal_addition(rule, prog, a, b, max_steps=100):
    """Simplified simulation focused on getting basic cases right."""
    L = len(prog)
    N = 30  # Small window for speed
    
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(min(L, N)):
        state[j] = prog[j]
    
    if L + 1 < N:
        state[L] = 3      # B
    if L + 2 < N:
        state[L + 1] = 3  # B
    
    # Simple encoding: [prog] BB 0^a R 0^b R
    base_pos = L + 2
    
    # Write first input
    for i in range(a):
        pos = base_pos + i
        if pos < N:
            state[pos] = 0
    
    # First R marker
    r1_pos = base_pos + a
    if r1_pos < N:
        state[r1_pos] = 2
    
    # Write second input
    for i in range(b):
        pos = r1_pos + 1 + i
        if pos < N:
            state[pos] = 0
    
    # Second R marker
    r2_pos = r1_pos + 1 + b
    if r2_pos < N:
        state[r2_pos] = 2
    
    # Reshape rule for easier access
    rule_local = rule[0:64]
    rule_skip_left = rule[64:128]
    rule_skip_right = rule[128:192]
    rule_long_range = rule[192:256]
    
    # Run simulation
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        
        # Apply rules with nonlocal logic
        for x in range(1, N - 1):
            # Get basic neighborhood
            left = state[x-1] if x > 0 else 0
            center = state[x]
            right = state[x+1] if x < N-1 else 0
            local_idx = (left << 4) | (center << 2) | right
            
            # Default to local rule
            nxt[x] = rule_local[local_idx]
            
            # Try nonlocal rules if we have space
            if x >= 2 and x < N - 2:
                left2 = state[x-2]
                right2 = state[x+2]
                
                # Simple nonlocal selection: use long-range if center is non-zero
                if center != 0:
                    long_range_idx = (left2 << 4) | (center << 2) | right2
                    nxt[x] = rule_long_range[long_range_idx]
                elif left != 0 and right == 0:
                    skip_left_idx = (left2 << 4) | (center << 2) | right
                    nxt[x] = rule_skip_left[skip_left_idx]
                elif right != 0 and left == 0:
                    skip_right_idx = (left << 4) | (center << 2) | right2
                    nxt[x] = rule_skip_right[skip_right_idx]
        
        # Improved halting: check for stable pattern with R markers
        blue_count = sum(1 for x in nxt if x == 3)
        red_count = sum(1 for x in nxt if x == 2)
        nonzero_count = sum(1 for x in nxt if x != 0)

        # Need both blue dominance AND preserved R markers
        if nonzero_count > 0 and blue_count / nonzero_count > 0.3 and red_count >= 1:
            # Find rightmost R and count zeros after it
            rightmost_r = -1
            for i in range(N-1, -1, -1):
                if nxt[i] == 2:
                    rightmost_r = i
                    break

            if rightmost_r != -1:
                # Count consecutive zeros after R
                zero_count = 0
                for i in range(rightmost_r + 1, N):
                    if nxt[i] == 0:
                        zero_count += 1
                    else:
                        break
                return zero_count
        
        state = nxt
        
        # Stop if no change
        if step > 0 and np.array_equal(state, nxt):
            break
    
    return -1  # Failed to converge

def evaluate_minimal(rule, prog, test_cases):
    """Evaluate on minimal test cases."""
    results = []
    for a, b in test_cases:
        expected = a + b
        result = simulate_minimal_addition(rule, prog, a, b)
        correct = (result == expected)
        convergent = (result != -1)
        results.append({
            'a': a, 'b': b, 'expected': expected, 'result': result,
            'correct': correct, 'convergent': convergent
        })
    return results

def minimal_training():
    """Minimal training on very simple cases."""
    print("🔬 MINIMAL NONLOCAL TRAINING")
    print("=" * 50)
    print("Goal: Just get 1+1=2, 1+2=3, 2+1=3 working")
    
    # Very simple test cases
    test_cases = [(1, 1), (1, 2), (2, 1)]
    print(f"Test cases: {test_cases}")
    
    # Small training parameters
    POP_SIZE = 20
    GENERATIONS = 50
    MUTATION_RATE = 0.1
    
    print(f"Population: {POP_SIZE}, Generations: {GENERATIONS}")
    
    # Initialize population
    population = []
    rng = np.random.default_rng(42)  # Fixed seed for reproducibility
    
    for i in range(POP_SIZE):
        # Start with better initialized rules
        rule = np.zeros(256, dtype=np.uint8)

        # Set some basic patterns for each rule type
        for rule_type in range(4):
            base = rule_type * 64
            # Basic patterns that preserve structure
            rule[base + 0] = 0   # 000 -> 0
            rule[base + 2] = 2   # 002 -> 2
            rule[base + 8] = 2   # 020 -> 2
            rule[base + 32] = 0  # 200 -> 0
            rule[base + 12] = 3  # 030 -> 3 (help with blue generation)
            rule[base + 48] = 3  # 300 -> 3

            # Some propagation patterns
            rule[base + 4] = 1   # 010 -> 1 (program propagation)
            rule[base + 1] = 1   # 001 -> 1

            # Add some random variation
            for j in range(64):
                if rng.random() < 0.2:  # 20% chance to randomize (less chaos)
                    rule[base + j] = rng.integers(0, 4)
        
        rule = _sanitize_nonlocal_rule(rule)
        
        # Simple program
        prog = rng.choice([0, 1], size=4, p=[0.8, 0.2])
        prog = _sanitize_programme(prog)
        
        population.append((rule, prog))
    
    best_accuracy = 0
    best_model = None
    
    print("\nTraining...")
    
    for gen in range(GENERATIONS):
        # Evaluate population
        fitness_scores = []
        
        for rule, prog in population:
            results = evaluate_minimal(rule, prog, test_cases)
            
            correct = sum(1 for r in results if r['correct'])
            convergent = sum(1 for r in results if r['convergent'])
            
            # Simple fitness: correct answers + convergence bonus
            fitness = correct * 100 + convergent * 10
            
            # Bonus for perfect accuracy
            if correct == len(test_cases):
                fitness += 500
            
            fitness_scores.append(fitness)
        
        # Track best
        best_idx = np.argmax(fitness_scores)
        gen_results = evaluate_minimal(population[best_idx][0], population[best_idx][1], test_cases)
        gen_accuracy = sum(1 for r in gen_results if r['correct']) / len(test_cases)
        gen_convergence = sum(1 for r in gen_results if r['convergent']) / len(test_cases)
        
        if gen_accuracy > best_accuracy:
            best_accuracy = gen_accuracy
            best_model = population[best_idx]
        
        # Progress report
        if gen % 10 == 0 or gen_accuracy > 0:
            print(f"Gen {gen:2d}: acc={gen_accuracy:.1%}, conv={gen_convergence:.1%}, best={best_accuracy:.1%}")
            
            if gen_accuracy > 0:
                print("  Current results:")
                for r in gen_results:
                    status = "✓" if r['correct'] else "✗"
                    conv = "C" if r['convergent'] else "NC"
                    print(f"    {r['a']}+{r['b']}={r['expected']} → {r['result']} {status} {conv}")
        
        # Early stopping
        if best_accuracy >= 1.0:
            print(f"🎯 Perfect accuracy reached at generation {gen}!")
            break
        
        # Simple reproduction
        if gen < GENERATIONS - 1:
            # Keep top 25%
            sorted_indices = np.argsort(fitness_scores)[::-1]
            n_keep = POP_SIZE // 4
            
            new_population = []
            
            # Keep best
            for i in range(n_keep):
                new_population.append(population[sorted_indices[i]])
            
            # Generate offspring from best
            while len(new_population) < POP_SIZE:
                parent_idx = rng.choice(sorted_indices[:n_keep])
                rule, prog = population[parent_idx]
                
                # Mutate
                new_rule = rule.copy()
                new_prog = prog.copy()
                
                # Mutate rule
                for i in range(256):
                    if rng.random() < MUTATION_RATE:
                        new_rule[i] = rng.integers(0, 4)
                
                new_rule = _sanitize_nonlocal_rule(new_rule)
                
                # Mutate program
                for i in range(len(prog)):
                    if rng.random() < MUTATION_RATE * 2:
                        new_prog[i] = rng.choice([0, 1], p=[0.8, 0.2])
                
                new_prog = _sanitize_programme(new_prog)
                
                new_population.append((new_rule, new_prog))
            
            population = new_population
    
    return best_model, best_accuracy, test_cases

if __name__ == "__main__":
    start_time = time.time()
    
    print("🧪 MINIMAL NONLOCAL COMMUTATIVE TRAINING")
    print("Testing if the basic approach can work at all")
    print("=" * 60)
    
    # Run minimal training
    best_model, final_accuracy, test_cases = minimal_training()
    
    print(f"\n🎯 TRAINING COMPLETE!")
    print(f"Final accuracy: {final_accuracy:.1%}")
    
    if best_model and final_accuracy > 0:
        rule, prog = best_model
        
        print("\n📊 FINAL RESULTS:")
        results = evaluate_minimal(rule, prog, test_cases)
        
        for r in results:
            status = "✓" if r['correct'] else "✗"
            conv = "C" if r['convergent'] else "NC"
            print(f"  {r['a']}+{r['b']}={r['expected']} → {r['result']} {status} {conv}")
        
        # Test if nonlocal rules are being used
        print(f"\n🔍 ANALYSIS:")
        print(f"Program: {prog}")
        print(f"Rule size: {len(rule)} entries (256 = 4 × 64)")
        
        # Check if different rule types have different patterns
        rule_reshaped = rule.reshape(4, 64)
        for i, name in enumerate(['Local', 'Skip-left', 'Skip-right', 'Long-range']):
            nonzero = np.sum(rule_reshaped[i] != 0)
            print(f"  {name} rule: {nonzero}/64 non-zero entries")
    
    else:
        print("\n❌ No improvement found - need to debug the approach")
        print("Possible issues:")
        print("  - Encoding might be wrong")
        print("  - Halting condition too strict")
        print("  - Rule selection logic needs work")
        print("  - Need more generations or larger population")
    
    end_time = time.time()
    print(f"\n⏱️  Training time: {(end_time - start_time):.1f} seconds")
    
    if final_accuracy >= 1.0:
        print("\n🎉 SUCCESS: Basic nonlocal approach works!")
    elif final_accuracy > 0:
        print(f"\n✅ PROGRESS: {final_accuracy:.1%} accuracy shows the approach has potential")
    else:
        print("\n⚠️  Need to debug the basic implementation")
