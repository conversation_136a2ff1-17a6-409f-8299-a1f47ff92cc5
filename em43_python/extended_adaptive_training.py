"""
Extended Adaptive Nonlocal Training

Continue training the successful adaptive model on increasingly complex cases:
- Stage 1: Current 16 cases (1-4 range)
- Stage 2: Extend to 1-5 range (25 cases)
- Stage 3: Extend to 1-6 range (36 cases) 
- Stage 4: Extend to 1-7 range (49 cases)
- Stage 5: Challenge cases with larger sums

This progressive approach should fix the 1+1 issue while scaling to more complex addition.
"""

import numpy as np
import time
import pickle
from tqdm import tqdm
from adaptive_nonlocal_training import AdaptiveEM43, evaluate_adaptive, create_random_adaptive_em43
from em43_numba import _sanitize_programme, _sanitize_rule

def load_best_adaptive_model():
    """Load the best model from previous training."""
    try:
        with open('adaptive_nonlocal_model.pkl', 'rb') as f:
            model_data = pickle.load(f)
        
        # Reconstruct AdaptiveEM43 object
        best_model = AdaptiveEM43(
            model_data['rule'],
            model_data['prog'],
            model_data['encoding_params'],
            model_data['decoding_params'],
            model_data['halting_params']
        )
        
        print(f"✅ Loaded previous best model: {model_data['accuracy']:.1%} accuracy")
        print(f"   Schemes: enc={model_data['encoding_params']}, dec={model_data['decoding_params']}")
        
        return best_model, model_data['accuracy']
    
    except FileNotFoundError:
        print("⚠️  No previous model found, starting from scratch")
        return None, 0.0

def create_extended_test_cases(max_val):
    """Create comprehensive test cases up to max_val."""
    cases = []
    for a in range(1, max_val + 1):
        for b in range(1, max_val + 1):
            cases.append((a, b))
    return cases

def create_challenge_cases():
    """Create challenging edge cases."""
    return [
        # Large equal sums
        (5, 5), (6, 6), (7, 7), (8, 8),
        # Large asymmetric sums
        (1, 8), (8, 1), (2, 7), (7, 2),
        (3, 6), (6, 3), (4, 5), (5, 4),
        # Very large cases
        (1, 9), (9, 1), (2, 8), (8, 2),
        (3, 7), (7, 3), (4, 6), (6, 4),
        (5, 5), (9, 9)
    ]

def extended_stage_training(stage_name, test_cases, initial_population=None, generations=200):
    """Train on extended test cases with population seeding."""
    print(f"\n🎯 EXTENDED STAGE: {stage_name}")
    print(f"Cases: {len(test_cases)} total")
    print(f"Range: {min(min(tc) for tc in test_cases)}-{max(max(tc) for tc in test_cases)}")
    print(f"Target: 85%+ accuracy")
    
    POP_SIZE = 80  # Larger population for complex cases
    MUTATION_RATE = 0.05
    
    # Initialize population
    population = []
    rng = np.random.default_rng()
    
    if initial_population:
        # Start with successful models and add diversity
        print("  📈 Seeding with successful models...")
        
        # Keep best models
        for i in range(min(POP_SIZE // 3, len(initial_population))):
            population.append(initial_population[i])
        
        # Create variations of best models
        best_model = initial_population[0]
        for i in range(POP_SIZE // 3):
            # Create variation
            new_rule = best_model.rule.copy()
            new_prog = best_model.prog.copy()
            
            # Light mutation
            for j in range(256):
                if rng.random() < 0.02:
                    new_rule[j] = rng.integers(0, 4)
            
            # Re-sanitize base rule
            base_rule = new_rule[0:64]
            base_rule = _sanitize_rule(base_rule)
            new_rule[0:64] = base_rule
            
            # Slight program variation
            for j in range(len(new_prog)):
                if rng.random() < 0.05:
                    new_prog[j] = rng.choice([0, 1], p=[0.6, 0.4])
            
            new_prog = _sanitize_programme(new_prog)
            
            # Vary schemes slightly
            new_encoding = best_model.encoding_params.copy()
            new_decoding = best_model.decoding_params.copy()
            new_halting = best_model.halting_params.copy()
            
            if rng.random() < 0.1:
                new_encoding['separator_type'] = rng.integers(0, 4)
            if rng.random() < 0.1:
                new_encoding['input_encoding'] = rng.integers(0, 4)
            if rng.random() < 0.1:
                new_decoding['method'] = rng.integers(0, 5)
            if rng.random() < 0.1:
                new_halting['condition_type'] = rng.integers(0, 5)
            if rng.random() < 0.1:
                new_halting['threshold'] = rng.integers(40, 80)
            
            variant = AdaptiveEM43(new_rule, new_prog, new_encoding, new_decoding, new_halting)
            population.append(variant)
    
    # Fill remaining slots with random individuals
    while len(population) < POP_SIZE:
        population.append(create_random_adaptive_em43())
    
    best_accuracy = 0
    best_model = None
    
    print(f"  🚀 Training {len(population)} models for {generations} generations...")
    
    for gen in range(generations):
        fitness_scores = []
        
        for em43 in population:
            results = evaluate_adaptive(em43, test_cases)
            
            correct = sum(1 for r in results if r['correct'])
            convergent = sum(1 for r in results if r['convergent'])
            
            accuracy = correct / len(test_cases)
            convergence_rate = convergent / len(test_cases)
            
            # Enhanced fitness function for complex cases
            fitness = correct * 1000 + convergent * 300
            
            # Strong bonuses for high accuracy
            if accuracy >= 0.9:
                fitness += 10000
            elif accuracy >= 0.8:
                fitness += 5000
            elif accuracy >= 0.7:
                fitness += 2500
            elif accuracy >= 0.6:
                fitness += 1000
            
            # Special bonus for fixing 1+1=2
            for r in results:
                if r['a'] == 1 and r['b'] == 1 and r['correct']:
                    fitness += 2000  # Big bonus for fixing this case
            
            # Bonus for handling large cases correctly
            for r in results:
                if r['correct'] and (r['a'] >= 5 or r['b'] >= 5):
                    fitness += 100  # Bonus for complex cases
            
            # Penalty for timeouts
            timeouts = sum(1 for r in results if r['result'] == -10)
            fitness -= timeouts * 100
            
            fitness_scores.append(fitness)
        
        # Track best
        best_idx = np.argmax(fitness_scores)
        gen_results = evaluate_adaptive(population[best_idx], test_cases)
        gen_accuracy = sum(1 for r in gen_results if r['correct']) / len(test_cases)
        gen_convergence = sum(1 for r in gen_results if r['convergent']) / len(test_cases)
        
        if gen_accuracy > best_accuracy:
            best_accuracy = gen_accuracy
            best_model = population[best_idx]
        
        # Progress report
        if gen % 40 == 0 or gen_accuracy >= 0.85:
            print(f"    Gen {gen:3d}: acc={gen_accuracy:.1%}, conv={gen_convergence:.1%}, best={best_accuracy:.1%}")
            
            # Check 1+1 status
            one_plus_one = next((r for r in gen_results if r['a'] == 1 and r['b'] == 1), None)
            if one_plus_one:
                status = "✓" if one_plus_one['correct'] else "✗"
                print(f"      1+1=2 → {one_plus_one['result']} {status}")
            
            # Show some challenging cases
            if gen_accuracy > 0:
                failing = [r for r in gen_results if not r['correct']]
                if failing:
                    print(f"      Failing: {[(r['a'], r['b'], r['result']) for r in failing[:3]]}")
        
        # Early stopping
        if best_accuracy >= 0.9:
            print(f"    🎯 Excellent accuracy reached at generation {gen}!")
            break
        
        # Advanced reproduction
        if gen < generations - 1:
            sorted_indices = np.argsort(fitness_scores)[::-1]
            n_elite = POP_SIZE // 5
            
            new_population = []
            
            # Keep elites
            for i in range(n_elite):
                new_population.append(population[sorted_indices[i]])
            
            # Generate offspring with advanced crossover
            while len(new_population) < POP_SIZE:
                # Select parents with tournament selection
                tournament_size = 5
                tournament_indices = rng.choice(sorted_indices[:POP_SIZE//2], size=tournament_size, replace=False)
                parent1_idx = tournament_indices[0]
                parent2_idx = tournament_indices[1]
                
                parent1 = population[parent1_idx]
                parent2 = population[parent2_idx]
                
                # Create offspring with crossover
                child_rule = parent1.rule.copy()
                child_prog = parent1.prog.copy()
                
                # Rule crossover
                crossover_point = rng.integers(0, 256)
                child_rule[crossover_point:] = parent2.rule[crossover_point:]
                
                # Program crossover
                if len(parent1.prog) == len(parent2.prog):
                    prog_crossover = rng.integers(0, len(parent1.prog))
                    child_prog[prog_crossover:] = parent2.prog[prog_crossover:]
                
                # Scheme crossover
                child_encoding = parent1.encoding_params.copy()
                child_decoding = parent1.decoding_params.copy()
                child_halting = parent1.halting_params.copy()
                
                if rng.random() < 0.5:
                    child_encoding = parent2.encoding_params.copy()
                if rng.random() < 0.5:
                    child_decoding = parent2.decoding_params.copy()
                if rng.random() < 0.5:
                    child_halting = parent2.halting_params.copy()
                
                # Adaptive mutation
                current_mutation = MUTATION_RATE
                if gen > 100 and best_accuracy < 0.7:
                    current_mutation *= 1.5  # Increase exploration if stuck
                
                # Mutate rule
                for i in range(256):
                    if rng.random() < current_mutation:
                        child_rule[i] = rng.integers(0, 4)
                
                # Re-sanitize base rule
                base_rule = child_rule[0:64]
                base_rule = _sanitize_rule(base_rule)
                child_rule[0:64] = base_rule
                
                # Mutate program
                for i in range(len(child_prog)):
                    if rng.random() < current_mutation * 2:
                        child_prog[i] = rng.choice([0, 1], p=[0.6, 0.4])
                
                child_prog = _sanitize_programme(child_prog)
                
                # Mutate schemes
                if rng.random() < 0.08:
                    child_encoding['separator_type'] = rng.integers(0, 4)
                if rng.random() < 0.08:
                    child_encoding['input_encoding'] = rng.integers(0, 4)
                if rng.random() < 0.08:
                    child_decoding['method'] = rng.integers(0, 5)
                if rng.random() < 0.08:
                    child_halting['condition_type'] = rng.integers(0, 5)
                if rng.random() < 0.08:
                    child_halting['threshold'] = rng.integers(35, 85)
                
                child = AdaptiveEM43(child_rule, child_prog, child_encoding, child_decoding, child_halting)
                new_population.append(child)
            
            population = new_population
    
    return best_model, best_accuracy, population

def extended_training_pipeline():
    """Run extended training pipeline with progressive complexity."""
    print("🚀 EXTENDED ADAPTIVE TRAINING PIPELINE")
    print("=" * 60)
    print("Goal: Scale to complex addition while fixing remaining issues")
    
    # Load previous best model
    initial_best, initial_accuracy = load_best_adaptive_model()
    
    # Define progressive stages
    stages = [
        ("Stage 1: Consolidate 1-4", create_extended_test_cases(4)),
        ("Stage 2: Extend to 1-5", create_extended_test_cases(5)),
        ("Stage 3: Extend to 1-6", create_extended_test_cases(6)),
        ("Stage 4: Challenge Cases", create_extended_test_cases(5) + create_challenge_cases()[:10]),
    ]
    
    population = [initial_best] if initial_best else None
    stage_results = {}
    
    for stage_name, test_cases in stages:
        start_time = time.time()
        
        best_model, accuracy, population = extended_stage_training(
            stage_name, test_cases, population, generations=250
        )
        
        stage_time = time.time() - start_time
        stage_results[stage_name] = {
            'accuracy': accuracy,
            'test_cases': test_cases,
            'time': stage_time,
            'model': best_model
        }
        
        print(f"  ✅ {stage_name} completed: {accuracy:.1%} accuracy in {stage_time/60:.1f}m")
        
        # Show detailed results for key cases
        if best_model:
            results = evaluate_adaptive(best_model, test_cases)
            
            # Check 1+1 specifically
            one_plus_one = next((r for r in results if r['a'] == 1 and r['b'] == 1), None)
            if one_plus_one:
                status = "✓ FIXED!" if one_plus_one['correct'] else "✗ still failing"
                print(f"    🔍 1+1=2 → {one_plus_one['result']} {status}")
            
            # Show sample of complex cases
            complex_cases = [r for r in results if (r['a'] >= 4 or r['b'] >= 4)]
            if complex_cases:
                correct_complex = sum(1 for r in complex_cases if r['correct'])
                print(f"    🧮 Complex cases: {correct_complex}/{len(complex_cases)} ({correct_complex/len(complex_cases)*100:.1f}%)")
            
            # Show overall stats
            correct_total = sum(1 for r in results if r['correct'])
            convergent_total = sum(1 for r in results if r['convergent'])
            print(f"    📊 Total: {correct_total}/{len(test_cases)} correct, {convergent_total}/{len(test_cases)} convergent")
        
        # Stop if stage failed
        if accuracy < 0.5:
            print(f"    ⚠️  Stage performance too low, stopping pipeline")
            break
        
        print()
    
    return stage_results

if __name__ == "__main__":
    start_time = time.time()
    
    print("🧮 EXTENDED ADAPTIVE NONLOCAL TRAINING")
    print("Scaling to complex addition cases")
    print("=" * 60)
    
    # Run extended training pipeline
    results = extended_training_pipeline()
    
    total_time = time.time() - start_time
    
    print(f"\n🎯 EXTENDED TRAINING COMPLETE!")
    print(f"Total time: {total_time/60:.1f} minutes")
    
    # Summary
    print(f"\n📈 EXTENDED TRAINING SUMMARY:")
    for stage_name, data in results.items():
        print(f"  {stage_name}: {data['accuracy']:.1%} on {len(data['test_cases'])} cases ({data['time']/60:.1f}m)")
    
    # Save final best model
    if results:
        final_stage = list(results.keys())[-1]
        final_data = results[final_stage]
        
        if final_data['model'] and final_data['accuracy'] > 0.6:
            best_model = final_data['model']
            
            model_data = {
                'rule': best_model.rule,
                'prog': best_model.prog,
                'encoding_params': best_model.encoding_params,
                'decoding_params': best_model.decoding_params,
                'halting_params': best_model.halting_params,
                'accuracy': final_data['accuracy'],
                'type': 'extended_adaptive_nonlocal',
                'test_cases': final_data['test_cases'],
                'extended_results': results
            }
            
            with open('extended_adaptive_model.pkl', 'wb') as f:
                pickle.dump(model_data, f)
            
            print(f"\n💾 Extended model saved: extended_adaptive_model.pkl")
            print(f"   Final accuracy: {final_data['accuracy']:.1%} on {len(final_data['test_cases'])} cases")
            
            # Test the final model on a comprehensive set
            print(f"\n🔬 FINAL MODEL ANALYSIS:")
            final_results = evaluate_adaptive(best_model, final_data['test_cases'])
            
            # Check if 1+1 is fixed
            one_plus_one = next((r for r in final_results if r['a'] == 1 and r['b'] == 1), None)
            if one_plus_one and one_plus_one['correct']:
                print(f"   🎉 1+1=2 ISSUE FIXED! → {one_plus_one['result']}")
            
            # Show learned schemes
            print(f"   📋 Final schemes:")
            print(f"     Encoding: {best_model.encoding_params}")
            print(f"     Decoding: {best_model.decoding_params}")
            print(f"     Halting: {best_model.halting_params}")
    
    print(f"\n🎉 Extended training demonstrates scalability of adaptive nonlocal approach!")
    print(f"   Ready for even more complex mathematical operations!")
