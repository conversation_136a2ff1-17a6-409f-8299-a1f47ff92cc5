"""
Resume Curriculum Training - Stage 4: Larger Numbers
Building on the successful 90.91% Stage 3 model
"""

import numpy as np
import time
import pickle
from tqdm import tqdm
from em43_numba import _sanitize_rule, _sanitize_programme

def simulate_addition_optimized(rule, prog, a, b, window=400, max_steps=800, halt_thresh=0.5):
    """Optimized addition simulation with larger parameters for Stage 4."""
    L = len(prog)
    N = window
    
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(L):
        state[j] = prog[j]
    state[L] = 3      # B
    state[L + 1] = 3  # B
    
    # Write inputs: 0^(a+1) R 0^(b+1) R
    r1_pos = L + 2 + a + 1
    if r1_pos >= N:
        return -10
    state[r1_pos] = 2
    
    r2_pos = r1_pos + 1 + b + 1
    if r2_pos >= N:
        return -10
    state[r2_pos] = 2
    
    # Adaptive simulation range
    sim_range = min(N - 1, r2_pos + max(a + b + 10, 30))
    
    # Run CA simulation
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(1, sim_range):
            idx = (state[x-1] << 4) | (state[x] << 2) | state[x+1]
            nxt[x] = rule[idx]
        
        # Halting check
        live = blue = 0
        for x in range(sim_range):
            v = nxt[x]
            if v != 0:
                live += 1
                if v == 3:
                    blue += 1
        
        if live > 0 and blue / live >= halt_thresh:
            # Find rightmost R
            rpos = -1
            for x in range(sim_range - 1, -1, -1):
                if nxt[x] == 2:
                    rpos = x
                    break
            
            if rpos != -1:
                return rpos - (L + 3)
            else:
                return -10
        
        state = nxt
    
    return -10

def load_stage3_model():
    """Load the best Stage 3 model."""
    try:
        # Try to load from the checkpoint if it exists
        with open('curriculum_checkpoint_stage3_final.pkl', 'rb') as f:
            checkpoint = pickle.load(f)
        return checkpoint['rule'], checkpoint['prog']
    except:
        try:
            # Fallback to stage 2 model
            with open('curriculum_checkpoint_stage2.pkl', 'rb') as f:
                checkpoint = pickle.load(f)
            return checkpoint['model']
        except:
            # Final fallback
            with open('addition_model.pkl', 'rb') as f:
                model_data = pickle.load(f)
            return model_data['rule'], model_data['prog']

def evaluate_detailed(rule, prog, test_cases, window=400, max_steps=800):
    """Detailed evaluation with individual case analysis."""
    results = []
    
    for a, b in test_cases:
        expected = a + b
        result = simulate_addition_optimized(rule, prog, a, b, window, max_steps, 0.5)
        
        status = "✓" if result == expected else "✗"
        convergent = result != -10
        
        results.append({
            'a': a, 'b': b, 'expected': expected, 'result': result,
            'correct': result == expected, 'convergent': convergent, 'status': status
        })
    
    return results

def train_stage4():
    """Train Stage 4: Larger Numbers (1-5 + 1-5)."""
    print("🚀 CURRICULUM STAGE 4: LARGER NUMBERS")
    print("=" * 60)
    
    # Stage 4 test cases: 1-5 + 1-5 = 25 cases
    stage4_cases = [(a, b) for a in range(1, 6) for b in range(1, 6)]
    
    print(f"Stage 4 cases ({len(stage4_cases)}): 1-5 + 1-5")
    print("Target accuracy: 90.0% (23/25 cases)")
    print("Target convergence: 95.0%")
    
    # Load Stage 3 model
    initial_rule, initial_prog = load_stage3_model()
    print("✅ Loaded Stage 3 model as starting point")
    
    # Evaluate initial performance
    print("\n📊 INITIAL PERFORMANCE ANALYSIS")
    initial_results = evaluate_detailed(initial_rule, initial_prog, stage4_cases)
    
    correct_count = sum(1 for r in initial_results if r['correct'])
    convergent_count = sum(1 for r in initial_results if r['convergent'])
    
    print(f"Initial accuracy: {correct_count}/{len(stage4_cases)} ({correct_count/len(stage4_cases)*100:.1f}%)")
    print(f"Convergence rate: {convergent_count}/{len(stage4_cases)} ({convergent_count/len(stage4_cases)*100:.1f}%)")
    
    # Show sample results
    print("\nSample results:")
    for i, r in enumerate(initial_results[:10]):
        print(f"  {r['a']}+{r['b']}={r['expected']} → {r['result']} {r['status']}")
    
    # Identify failing cases
    failing_cases = [r for r in initial_results if not r['correct']]
    non_convergent = [r for r in initial_results if not r['convergent']]
    
    print(f"\nFailing cases ({len(failing_cases)}):")
    for r in failing_cases[:10]:  # Show first 10
        print(f"  {r['a']}+{r['b']}={r['expected']} → {r['result']}")
    
    if non_convergent:
        print(f"\nNon-convergent cases ({len(non_convergent)}):")
        for r in non_convergent[:5]:
            print(f"  {r['a']}+{r['b']}={r['expected']} → {r['result']}")
    
    # Training parameters
    POP_SIZE = 80
    GENERATIONS = 400
    MUTATION_RATE = 0.005  # Even finer tuning for larger problems
    
    print(f"\n🚀 STAGE 4 TRAINING")
    print(f"Population: {POP_SIZE}, Generations: {GENERATIONS}, Mutation: {MUTATION_RATE}")
    
    # Initialize population
    population = []
    rng = np.random.default_rng()
    
    for i in range(POP_SIZE):
        rule = initial_rule.copy()
        prog = initial_prog.copy()
        
        if i > 0:  # Keep one copy unchanged
            # Very small mutations for fine-tuning
            mask = rng.random(64) < MUTATION_RATE
            rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)
            
            mask = rng.random(len(prog)) < MUTATION_RATE * 2
            prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.8, 0.15, 0.05])
        
        rule = _sanitize_rule(rule)
        prog = _sanitize_programme(prog)
        population.append((rule, prog))
    
    # Training loop
    best_accuracy = correct_count / len(stage4_cases)
    best_model = (initial_rule, initial_prog)
    best_fitness = -1000
    
    print("Training...")
    
    for gen in tqdm(range(GENERATIONS), desc="Stage 4 Training"):
        # Evaluate population
        fitness_scores = []
        
        for rule, prog in population:
            results = evaluate_detailed(rule, prog, stage4_cases)
            
            correct = sum(1 for r in results if r['correct'])
            convergent = sum(1 for r in results if r['convergent'])
            
            accuracy = correct / len(stage4_cases)
            convergence_rate = convergent / len(stage4_cases)
            
            # Enhanced fitness function for larger problems
            fitness = accuracy * 1000 + convergence_rate * 200
            
            # Bonus for high accuracy
            if accuracy >= 0.9:
                fitness += 500
            elif accuracy >= 0.8:
                fitness += 200
            
            # Penalty for non-convergence
            fitness -= (len(stage4_cases) - convergent) * 10
            
            fitness_scores.append(fitness)
        
        # Track best
        gen_best_idx = np.argmax(fitness_scores)
        gen_best_fitness = fitness_scores[gen_best_idx]
        
        gen_results = evaluate_detailed(population[gen_best_idx][0], population[gen_best_idx][1], stage4_cases)
        gen_accuracy = sum(1 for r in gen_results if r['correct']) / len(stage4_cases)
        gen_convergence = sum(1 for r in gen_results if r['convergent']) / len(stage4_cases)
        
        if gen_accuracy > best_accuracy:
            best_accuracy = gen_accuracy
            best_model = population[gen_best_idx]
            best_fitness = gen_best_fitness
        
        # Progress report
        if gen % 50 == 0:
            avg_fitness = np.mean(fitness_scores)
            print(f"  Gen {gen}: best_acc={best_accuracy:.2%}, conv={gen_convergence:.2%}, fitness={best_fitness:.1f}")
            
            # Show current status
            current_failing = [r for r in gen_results if not r['correct']]
            current_non_conv = [r for r in gen_results if not r['convergent']]
            
            if len(current_failing) <= 5:
                print(f"    Failing: {[(r['a'], r['b']) for r in current_failing]}")
            else:
                print(f"    Failing: {len(current_failing)} cases")
            
            if current_non_conv:
                print(f"    Non-convergent: {len(current_non_conv)} cases")
        
        # Early stopping if target reached
        if best_accuracy >= 0.90:
            print(f"  🎯 Target accuracy 90% reached at generation {gen}!")
            break
        
        # Selection and reproduction
        if gen < GENERATIONS - 1:
            # Elite selection
            sorted_indices = np.argsort(fitness_scores)[::-1]
            n_elite = POP_SIZE // 4
            
            new_population = []
            
            # Keep elites
            for i in range(n_elite):
                new_population.append(population[sorted_indices[i]])
            
            # Generate offspring
            while len(new_population) < POP_SIZE:
                # Tournament selection
                tournament_size = 4
                tournament_indices = rng.choice(sorted_indices[:POP_SIZE//2], size=tournament_size, replace=False)
                parent_idx = tournament_indices[0]
                
                rule, prog = population[parent_idx]
                
                # Adaptive mutation
                current_mutation_rate = MUTATION_RATE
                if gen > 200 and best_accuracy < 0.85:
                    current_mutation_rate *= 1.5  # Increase exploration if stuck
                
                # Mutate
                new_rule = rule.copy()
                new_prog = prog.copy()
                
                mask = rng.random(64) < current_mutation_rate
                new_rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)
                
                mask = rng.random(len(prog)) < current_mutation_rate * 2
                new_prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.8, 0.15, 0.05])
                
                new_population.append((_sanitize_rule(new_rule), _sanitize_programme(new_prog)))
            
            population = new_population
    
    return best_model, best_accuracy

def comprehensive_stage4_analysis(rule, prog):
    """Comprehensive analysis of Stage 4 results."""
    print("\n" + "=" * 60)
    print("🔬 COMPREHENSIVE STAGE 4 ANALYSIS")
    print("=" * 60)
    
    # Test different ranges
    test_ranges = {
        'Stage 1-3 (Regression)': [(1, 1), (1, 2), (2, 1), (2, 2), (3, 1), (1, 3), 
                                   (3, 2), (2, 3), (3, 3), (4, 1), (1, 4)],
        'Stage 4 (Current)': [(a, b) for a in range(1, 6) for b in range(1, 6)],
        'Generalization': [(6, 1), (1, 6), (5, 5), (6, 2), (2, 6), (7, 1), (1, 7)]
    }
    
    for range_name, test_cases in test_ranges.items():
        print(f"\n📊 {range_name}:")
        results = evaluate_detailed(rule, prog, test_cases)
        
        correct = sum(1 for r in results if r['correct'])
        convergent = sum(1 for r in results if r['convergent'])
        
        print(f"  Accuracy: {correct}/{len(test_cases)} ({correct/len(test_cases)*100:.1f}%)")
        print(f"  Convergence: {convergent}/{len(test_cases)} ({convergent/len(test_cases)*100:.1f}%)")
        
        # Show failures
        failures = [r for r in results if not r['correct']]
        if failures and len(failures) <= 10:
            print(f"  Failures: {[(r['a'], r['b'], r['expected'], r['result']) for r in failures]}")
        elif failures:
            print(f"  Failures: {len(failures)} cases (showing first 5)")
            for r in failures[:5]:
                print(f"    {r['a']}+{r['b']}={r['expected']} → {r['result']}")
    
    # Save Stage 4 model
    stage4_model = {
        'rule': rule,
        'prog': prog,
        'stage': 'stage4_complete',
        'test_cases': [(a, b) for a in range(1, 6) for b in range(1, 6)]
    }
    
    with open('curriculum_checkpoint_stage4_final.pkl', 'wb') as f:
        pickle.dump(stage4_model, f)
    
    print(f"\n💾 Stage 4 model saved: curriculum_checkpoint_stage4_final.pkl")

if __name__ == "__main__":
    start_time = time.time()
    
    print("🎯 RESUMING CURRICULUM TRAINING")
    print("Building on successful 90.91% Stage 3 model")
    print("=" * 60)
    
    # Train Stage 4
    best_model, final_accuracy = train_stage4()
    
    print(f"\n🎯 STAGE 4 TRAINING COMPLETE!")
    print(f"Final accuracy: {final_accuracy:.2%}")
    
    if final_accuracy >= 0.90:
        print("🎉 SUCCESS: Target accuracy achieved!")
    elif final_accuracy >= 0.80:
        print("✅ GOOD: Strong performance, ready for Stage 5!")
    else:
        print("⚠️  Needs more training or parameter adjustment")
    
    # Comprehensive analysis
    rule, prog = best_model
    comprehensive_stage4_analysis(rule, prog)
    
    end_time = time.time()
    
    print(f"\n⏱️  Stage 4 training time: {(end_time - start_time)/60:.1f} minutes")
    
    if final_accuracy >= 0.80:
        print("\n🚀 READY FOR STAGE 5: Extended range (1-7 + 1-7)!")
        print("💡 Curriculum learning successfully scaling to larger problems!")
    else:
        print("\n💡 Consider: Longer training, larger programs, or different encoding strategies")
    
    print(f"\n📈 CURRICULUM PROGRESS SUMMARY:")
    print(f"  Stage 1: 100% (3 cases)")
    print(f"  Stage 2: 100% (6 cases)")  
    print(f"  Stage 3: 90.91% (11 cases)")
    print(f"  Stage 4: {final_accuracy:.1%} (25 cases)")
    print(f"  🎯 Successfully scaling emergent addition models!")
