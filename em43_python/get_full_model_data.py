"""
Get the complete perfect model data for the HTML viewer
"""

import pickle
import numpy as np
from adaptive_nonlocal_training import AdaptiveEM43, evaluate_adaptive

try:
    with open('perfect_adaptive_model.pkl', 'rb') as f:
        model_data = pickle.load(f)
    
    # Reconstruct the model
    perfect_model = AdaptiveEM43(
        model_data['rule'],
        model_data['prog'],
        model_data['encoding_params'],
        model_data['decoding_params'],
        model_data['halting_params']
    )
    
    print("Perfect model loaded successfully!")
    print(f"Rule length: {len(perfect_model.rule)}")
    print(f"Program: {perfect_model.prog}")
    print(f"Encoding params: {perfect_model.encoding_params}")
    print(f"Decoding params: {perfect_model.decoding_params}")
    print(f"Halting params: {perfect_model.halting_params}")
    
    # Test the model
    print("\nTesting model:")
    test_cases = [(2, 3), (1, 1), (4, 4)]
    for a, b in test_cases:
        result = perfect_model.simulate(a, b)
        expected = a + b
        status = "✓" if result == expected else "✗"
        print(f"  {a}+{b}={expected} → {result} {status}")
    
    # Output the complete rule and program for JavaScript
    print(f"\nComplete rule array (256 entries):")
    rule_str = ",".join(map(str, perfect_model.rule.tolist()))
    print(f"[{rule_str}]")
    
    print(f"\nProgram array:")
    prog_str = ",".join(map(str, perfect_model.prog.tolist()))
    print(f"[{prog_str}]")
    
    # Test encoding
    print(f"\nTesting encoding for 2+3:")
    state = perfect_model.encode_inputs(2, 3, N=30)
    print(f"Encoded state: {state[:20]}")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
