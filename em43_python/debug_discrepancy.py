"""
Debug the discrepancy between HTML viewer (correct) and Python test (incorrect) for 30+30
"""

import pickle
import numpy as np
from adaptive_nonlocal_training import AdaptiveEM43

# Load the perfect model
with open('perfect_adaptive_model.pkl', 'rb') as f:
    model_data = pickle.load(f)

perfect_model = AdaptiveEM43(
    model_data['rule'],
    model_data['prog'],
    model_data['encoding_params'],
    model_data['decoding_params'],
    model_data['halting_params']
)

print("🔍 DEBUGGING 30+30 DISCREPANCY")
print("=" * 50)
print("HTML viewer shows: 30+30=60 ✓ CORRECT")
print("Python test shows: 30+30=31 ❌ WRONG")
print("Let's find the difference...")

a, b = 30, 30
expected = 60

print(f"\n1. Testing with Python AdaptiveEM43.simulate():")
result_python = perfect_model.simulate(a, b)
print(f"   Result: {result_python} (expected: {expected})")

print(f"\n2. Testing with manual step-by-step (like HTML):")

# Replicate the HTML viewer logic exactly
N = 300  # Larger buffer like HTML might use
state = perfect_model.encode_inputs(a, b, N)
print(f"   Initial encoding: {' '.join(str(x) if x != 0 else '.' for x in state[:50])}")

# Extract rule components
rule_local = perfect_model.rule[0:64]
rule_skip_left = perfect_model.rule[64:128]
rule_skip_right = perfect_model.rule[128:192]
rule_long_range = perfect_model.rule[192:256]

trace = [state.copy()]
steps = 0
MAX_STEPS = 15000

while steps < MAX_STEPS:
    # Check halting first (like HTML)
    if perfect_model.check_halting(state):
        result_manual = perfect_model.decode_result(state)
        print(f"   Manual result: {result_manual} after {steps} steps")
        break
    
    # Apply step
    nxt = np.zeros(N, dtype=np.uint8)
    
    for x in range(1, N - 1):
        left = state[x-1] if x > 0 else 0
        center = state[x]
        right = state[x+1] if x < N-1 else 0
        local_idx = (left << 4) | (center << 2) | right
        
        # Default to local rule
        nxt[x] = rule_local[local_idx]
        
        # Apply nonlocal rules
        if x >= 2 and x < N - 2:
            left2 = state[x-2]
            right2 = state[x+2]
            
            if center != 0:
                long_range_idx = (left2 << 4) | (center << 2) | right2
                nxt[x] = rule_long_range[long_range_idx]
            elif left != 0 and right == 0:
                skip_left_idx = (left2 << 4) | (center << 2) | right
                nxt[x] = rule_skip_left[skip_left_idx]
            elif right != 0 and left == 0:
                skip_right_idx = (left << 4) | (center << 2) | right2
                nxt[x] = rule_skip_right[skip_right_idx]
    
    state = nxt
    steps += 1
    trace.append(state.copy())
    
    if steps > 10:  # Prevent infinite loop
        print(f"   Manual: Did not halt within {steps} steps")
        break

print(f"\n3. Comparing halting conditions:")
print(f"   Python model halting params: {perfect_model.halting_params}")

# Test halting on final state
final_state = trace[-1] if trace else state
live = sum(1 for x in final_state if x > 0)
prog = sum(1 for x in final_state if x == 1)
blue = sum(1 for x in final_state if x == 3)
red = sum(1 for x in final_state if x == 2)

print(f"   Final state analysis:")
print(f"     Live cells: {live}")
print(f"     Program cells: {prog}")
print(f"     Blue cells: {blue}")
print(f"     Red cells: {red}")

if live > 0:
    prog_ratio = prog / live
    print(f"     Program ratio: {prog_ratio:.3f}")
    print(f"     Halting threshold: 0.31")
    print(f"     Should halt: {prog_ratio <= 0.31}")

print(f"\n4. Testing different buffer sizes:")
for buffer_size in [100, 200, 300, 400]:
    try:
        test_state = perfect_model.encode_inputs(a, b, buffer_size)
        # Quick test - just check if encoding looks right
        nonzero_count = sum(1 for x in test_state if x != 0)
        print(f"   Buffer {buffer_size}: {nonzero_count} non-zero cells")
    except Exception as e:
        print(f"   Buffer {buffer_size}: Error - {e}")

print(f"\n5. Checking if it's a timeout issue:")
result_long = perfect_model.simulate(a, b, max_steps=50000)
print(f"   With 50k max steps: {result_long}")

print(f"\n🎯 CONCLUSION:")
if result_manual == expected and result_python != expected:
    print("The manual simulation (like HTML) gives correct result!")
    print("The AdaptiveEM43.simulate() method may have a bug.")
elif result_python == expected:
    print("Both methods now give correct results - may have been a transient issue.")
else:
    print("Both methods give wrong results - need deeper investigation.")
