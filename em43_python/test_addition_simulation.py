"""
Test the addition simulation to understand why training is failing.
"""

import numpy as np
from em43_numba import EM43AdditionBatch, _simulate_addition

def test_random_addition():
    """Test addition simulation with random genome."""
    print("Testing addition simulation with random genome...")
    
    # Create random genome
    rng = np.random.default_rng(42)  # Fixed seed for reproducibility
    rule = rng.integers(0, 4, 64, dtype=np.uint8)
    prog = rng.choice([0, 1, 2], size=16, p=[0.7, 0.2, 0.1])
    
    # Test simple addition cases
    test_a = np.array([1, 2, 3, 4, 5], dtype=np.int64)
    test_b = np.array([1, 2, 3, 4, 5], dtype=np.int64)
    expected = test_a + test_b
    
    print(f"Test inputs A: {test_a}")
    print(f"Test inputs B: {test_b}")
    print(f"Expected outputs: {expected}")
    
    # Test with direct function
    outputs = _simulate_addition(rule, prog, test_a, test_b, 400, 800, 0.5)
    print(f"Actual outputs: {outputs}")
    
    # Count convergent results
    convergent = np.sum(outputs != -10)
    print(f"Convergent results: {convergent}/{len(outputs)}")
    
    if convergent > 0:
        correct = np.sum(outputs[outputs != -10] == expected[outputs != -10])
        print(f"Correct among convergent: {correct}/{convergent}")
    
    return outputs

def test_simple_addition_case():
    """Test the simplest case: 1 + 1 = 2"""
    print("\nTesting simplest case: 1 + 1 = 2")
    
    # Create a very simple rule that might work
    rule = np.zeros(64, dtype=np.uint8)
    # Set some basic transitions
    rule[0] = 0  # 000 -> 0
    rule[1] = 0  # 001 -> 0  
    rule[2] = 2  # 002 -> 2 (preserve R)
    rule[3] = 3  # 003 -> 3 (preserve B)
    
    # Simple program
    prog = np.array([1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], dtype=np.uint8)
    
    test_a = np.array([1], dtype=np.int64)
    test_b = np.array([1], dtype=np.int64)
    
    print(f"Rule sample: {rule[:10]}")
    print(f"Program: {prog}")
    
    output = _simulate_addition(rule, prog, test_a, test_b, 400, 800, 0.5)
    print(f"1 + 1 = {output[0]} (expected: 2)")
    
    return output

def analyze_tape_structure():
    """Analyze the tape structure for addition."""
    print("\nAnalyzing tape structure for addition...")
    
    # For inputs a=2, b=3, the tape should be:
    # [program(16)] BB 0^(2+1) R 0^(3+1) R 0
    # [prog......] BB 000 R 0000 R 0
    
    L = 16  # program length
    a, b = 2, 3
    
    print(f"Program length: {L}")
    print(f"Input a: {a}, Input b: {b}")
    print(f"Expected tape structure:")
    print(f"  Positions 0-{L-1}: Program")
    print(f"  Positions {L}-{L+1}: BB separator")
    print(f"  Positions {L+2}-{L+2+a}: {a+1} zeros for first input")
    print(f"  Position {L+2+a+1}: First R marker")
    print(f"  Positions {L+2+a+2}-{L+2+a+2+b}: {b+1} zeros for second input") 
    print(f"  Position {L+2+a+2+b+1}: Second R marker")
    print(f"  Position {L+2+a+2+b+2}+: Output space")
    
    r1_idx = L + 2 + a + 1
    r2_idx = r1_idx + 1 + b + 1
    
    print(f"First R at position: {r1_idx}")
    print(f"Second R at position: {r2_idx}")
    print(f"Output should start at position: {r2_idx + 1}")

def test_fitness_calculation():
    """Test the fitness calculation logic."""
    print("\nTesting fitness calculation...")
    
    # Simulate some outputs
    predicted = np.array([2, 4, -10, 8, 10])  # Some correct, some wrong, one non-convergent
    targets = np.array([2, 4, 6, 8, 10])
    
    correct = 0
    total_error = 0.0
    convergence_penalty = 0.0
    
    for pred, target in zip(predicted, targets):
        if pred == -10:  # Non-convergent
            convergence_penalty += 5.0
            print(f"Non-convergent: penalty +5.0")
        elif pred == target:
            correct += 1
            print(f"Correct: {pred} == {target}")
        else:
            error = abs(pred - target)
            total_error += error * 1.5
            print(f"Wrong: {pred} != {target}, error penalty +{error * 1.5}")
    
    accuracy = correct / len(predicted)
    avg_error = total_error / len(predicted)
    convergence_penalty /= len(predicted)
    
    fitness = accuracy * 100 - avg_error - convergence_penalty
    
    print(f"Accuracy: {accuracy:.2f} ({correct}/{len(predicted)})")
    print(f"Average error: {avg_error:.2f}")
    print(f"Convergence penalty: {convergence_penalty:.2f}")
    print(f"Final fitness: {fitness:.2f}")

if __name__ == "__main__":
    print("=" * 50)
    print("ADDITION SIMULATION ANALYSIS")
    print("=" * 50)
    
    # Test 1: Random genome
    test_random_addition()
    
    # Test 2: Simple case
    test_simple_addition_case()
    
    # Test 3: Tape structure analysis
    analyze_tape_structure()
    
    # Test 4: Fitness calculation
    test_fitness_calculation()
    
    print("\n" + "=" * 50)
    print("ANALYSIS COMPLETE")
    print("=" * 50)
