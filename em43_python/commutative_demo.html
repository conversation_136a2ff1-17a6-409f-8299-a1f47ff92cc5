
<!DOCTYPE html>
<html>
<head>
    <title>Commutative Nonlocal EM43 Demo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .case { margin: 20px 0; border: 1px solid #ccc; padding: 15px; }
        .step { margin: 10px 0; }
        .grid { display: grid; grid-template-columns: repeat(20, 20px); gap: 1px; margin: 5px 0; }
        .cell { width: 20px; height: 20px; border: 1px solid #999; text-align: center; line-height: 20px; font-size: 12px; }
        .cell-0 { background-color: #ffffff; }
        .cell-1 { background-color: #000000; color: white; }
        .cell-2 { background-color: #ff2222; color: white; }
        .cell-3 { background-color: #1665c1; color: white; }
        .rule-L { border: 2px solid #00ff00; }
        .rule-S { border: 2px solid #ff8800; }
        .rule-R { border: 2px solid #8800ff; }
        .rule-G { border: 2px solid #ff0088; }
        .highlight { background-color: #ffff99; }
    </style>
</head>
<body>
    <h1>Commutative Nonlocal EM43 Demonstration</h1>
    <p>Shows how commutative encoding works with nonlocal rules: a + b = b + a</p>
    <div class="case">
        <h3>2 + 2 = 4 (Result: -1)</h3>
        <p><strong>Special case:</strong> a = b, so inputs overlap naturally</p>
        <div class="step">Step 0:<div class="grid"><div class="cell cell-1  ">1</div><div class="cell cell-0  "></div><div class="cell cell-1  ">1</div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-3  ">3</div><div class="cell cell-3  ">3</div><div class="cell cell-0  highlight"></div><div class="cell cell-0  highlight"></div><div class="cell cell-2  ">2</div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div></div></div>
        <div class="step">Step 1:<div class="grid"><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-G "></div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-R "></div><div class="cell cell-0 rule-G "></div><div class="cell cell-0 rule-G "></div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-R "></div><div class="cell cell-2 rule-G ">2</div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div></div></div>
        <div class="step">Step 2:<div class="grid"><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-R "></div><div class="cell cell-2 rule-G ">2</div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div></div></div>
    </div>
    <div class="case">
        <h3>2 + 3 = 5 (Result: -1)</h3>
        <p><strong>Commutative pair:</strong> Should give same result as 3 + 2</p>
        <div class="step">Step 0:<div class="grid"><div class="cell cell-1  ">1</div><div class="cell cell-0  "></div><div class="cell cell-1  ">1</div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-3  ">3</div><div class="cell cell-3  ">3</div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-2  ">2</div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-2  ">2</div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div></div></div>
        <div class="step">Step 1:<div class="grid"><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-G "></div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-R "></div><div class="cell cell-0 rule-G "></div><div class="cell cell-0 rule-G "></div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-R "></div><div class="cell cell-2 rule-G ">2</div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-R "></div><div class="cell cell-2 rule-G ">2</div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div></div></div>
        <div class="step">Step 2:<div class="grid"><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-R "></div><div class="cell cell-2 rule-G ">2</div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-R "></div><div class="cell cell-2 rule-G ">2</div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div></div></div>
    </div>
    <div class="case">
        <h3>3 + 2 = 5 (Result: -1)</h3>
        <p><strong>Commutative pair:</strong> Should give same result as 2 + 3</p>
        <div class="step">Step 0:<div class="grid"><div class="cell cell-1  ">1</div><div class="cell cell-0  "></div><div class="cell cell-1  ">1</div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-3  ">3</div><div class="cell cell-3  ">3</div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-2  ">2</div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-2  ">2</div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div><div class="cell cell-0  "></div></div></div>
        <div class="step">Step 1:<div class="grid"><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-G "></div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-R "></div><div class="cell cell-0 rule-G "></div><div class="cell cell-0 rule-G "></div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-R "></div><div class="cell cell-2 rule-G ">2</div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-R "></div><div class="cell cell-2 rule-G ">2</div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div></div></div>
        <div class="step">Step 2:<div class="grid"><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-R "></div><div class="cell cell-2 rule-G ">2</div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-R "></div><div class="cell cell-2 rule-G ">2</div><div class="cell cell-0 rule-S "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div><div class="cell cell-0 rule-L "></div></div></div>
    </div>

    <div style="margin-top: 30px;">
        <h3>Key Features:</h3>
        <ul>
            <li><strong>Commutative Encoding:</strong> a + b and b + a use equivalent representations</li>
            <li><strong>Natural Overlap:</strong> When a = b, inputs occupy the same cells automatically</li>
            <li><strong>Nonlocal Rules:</strong> Information can skip past blocking cells (colored borders)</li>
            <li><strong>Efficient Training:</strong> Reduces search space by leveraging mathematical properties</li>
        </ul>
        
        <h3>Rule Types:</h3>
        <div style="display: flex; gap: 20px;">
            <div><div class="cell rule-L" style="display: inline-block;">L</div> Local (standard)</div>
            <div><div class="cell rule-S" style="display: inline-block;">S</div> Skip-left</div>
            <div><div class="cell rule-R" style="display: inline-block;">R</div> Skip-right</div>
            <div><div class="cell rule-G" style="display: inline-block;">G</div> Long-range</div>
        </div>
    </div>
</body>
</html>
