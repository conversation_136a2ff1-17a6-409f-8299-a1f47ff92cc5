
<!DOCTYPE html>
<html>
<head>
    <title>Nonlocal EM43 Concept Demo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .scenario { margin: 20px 0; border: 1px solid #ccc; padding: 15px; }
        .step { margin: 10px 0; }
        .grid { display: grid; grid-template-columns: repeat(16, 25px); gap: 2px; margin: 5px 0; }
        .cell { width: 25px; height: 25px; border: 1px solid #999; text-align: center; line-height: 25px; }
        .cell-0 { background-color: #ffffff; }
        .cell-1 { background-color: #000000; color: white; }
        .cell-2 { background-color: #ff2222; color: white; }
        .cell-3 { background-color: #1665c1; color: white; }
        .rule-L { border: 2px solid #00ff00; }
        .rule-S { border: 2px solid #ff8800; }
        .rule-R { border: 2px solid #8800ff; }
        .rule-G { border: 2px solid #ff0088; }
    </style>
</head>
<body>
    <h1>Nonlocal EM43 Concept Demonstration</h1>
    <p>This demonstrates how nonlocal rules can help information propagate past blocking cells.</p>
    
    <div style="margin: 20px 0;">
        <h3>Legend:</h3>
        <div style="display: flex; gap: 20px;">
            <div><div class="cell cell-0" style="display: inline-block;">0</div> Blank</div>
            <div><div class="cell cell-1" style="display: inline-block;">1</div> Program</div>
            <div><div class="cell cell-2" style="display: inline-block;">2</div> Red marker</div>
            <div><div class="cell cell-3" style="display: inline-block;">3</div> Blue marker</div>
        </div>
        <h3>Rule Types:</h3>
        <div style="display: flex; gap: 20px;">
            <div><div class="cell rule-L" style="display: inline-block;">L</div> Local</div>
            <div><div class="cell rule-S" style="display: inline-block;">S</div> Skip-left</div>
            <div><div class="cell rule-R" style="display: inline-block;">R</div> Skip-right</div>
            <div><div class="cell rule-G" style="display: inline-block;">G</div> Long-range</div>
        </div>
    </div>
    <div class="scenario"><h3>Blocked propagation</h3>
        <div class="step">Step 0:<div class="grid"><div class="cell cell-0"></div><div class="cell cell-1">1</div><div class="cell cell-0"></div><div class="cell cell-2">2</div><div class="cell cell-0"></div><div class="cell cell-1">1</div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div></div></div>
        <div class="step">Step 1:<div class="grid"><div class="cell cell-0 rule-L"></div><div class="cell cell-1 rule-L">1</div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-G"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-G"></div><div class="cell cell-0 rule-S"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div></div></div>
        <div class="step">Step 2:<div class="grid"><div class="cell cell-0 rule-L"></div><div class="cell cell-1 rule-L">1</div><div class="cell cell-0 rule-S"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div></div></div>
    </div>
    <div class="scenario"><h3>Skip through blocker</h3>
        <div class="step">Step 0:<div class="grid"><div class="cell cell-0"></div><div class="cell cell-1">1</div><div class="cell cell-0"></div><div class="cell cell-3">3</div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-1">1</div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div></div></div>
        <div class="step">Step 1:<div class="grid"><div class="cell cell-0 rule-L"></div><div class="cell cell-1 rule-L">1</div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-G"></div><div class="cell cell-0 rule-S"></div><div class="cell cell-0 rule-R"></div><div class="cell cell-1 rule-G">1</div><div class="cell cell-0 rule-S"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div></div></div>
        <div class="step">Step 2:<div class="grid"><div class="cell cell-0 rule-L"></div><div class="cell cell-1 rule-L">1</div><div class="cell cell-0 rule-S"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-R"></div><div class="cell cell-1 rule-G">1</div><div class="cell cell-0 rule-S"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div></div></div>
    </div>
    <div class="scenario"><h3>Long-range needed</h3>
        <div class="step">Step 0:<div class="grid"><div class="cell cell-0"></div><div class="cell cell-1">1</div><div class="cell cell-0"></div><div class="cell cell-2">2</div><div class="cell cell-0"></div><div class="cell cell-3">3</div><div class="cell cell-0"></div><div class="cell cell-1">1</div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div><div class="cell cell-0"></div></div></div>
        <div class="step">Step 1:<div class="grid"><div class="cell cell-0 rule-L"></div><div class="cell cell-1 rule-L">1</div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-G"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-G"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-G"></div><div class="cell cell-0 rule-S"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div></div></div>
        <div class="step">Step 2:<div class="grid"><div class="cell cell-0 rule-L"></div><div class="cell cell-1 rule-L">1</div><div class="cell cell-0 rule-S"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div><div class="cell cell-0 rule-L"></div></div></div>
    </div>

    <div style="margin-top: 30px;">
        <h3>Key Insights:</h3>
        <ul>
            <li>Standard EM43 (radius-1) can get 'blocked' by non-zero cells</li>
            <li>Nonlocal rules allow information to 'skip past' blocking cells</li>
            <li>This enables better information propagation in complex computations</li>
            <li>The combinatorial space remains manageable (256 total rules vs 64 standard)</li>
        </ul>
    </div>
</body>
</html>
