"""
Commutative Addition Trainer - Overlapping encoding for commutativity
Based on the idea that a+b should have the same encoding as b+a
"""

import numpy as np
import time
from tqdm import tqdm
import pickle
from em43_numba import _simulate, _sanitize_rule, _sanitize_programme

def create_commutative_encoding(a, b, prog, window=300):
    """
    Create a commutative encoding where a+b and b+a have the same representation.
    
    Strategy: Encode both numbers in overlapping positions so that:
    - The larger number determines the overall structure
    - The smaller number overlaps/modifies the larger one
    - When a=b, they perfectly overlap
    """
    L = len(prog)
    N = window
    
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(L):
        state[j] = prog[j]
    state[L] = 3      # B
    state[L + 1] = 3  # B
    
    # Ensure a >= b for consistent encoding (swap if needed)
    larger, smaller = max(a, b), min(a, b)
    
    # Base position after program and separator
    base_pos = L + 2
    
    # Strategy 1: Overlapping zeros with markers
    # Layout: [prog] [BB] [0^larger] [R] [overlap_region] [R]
    
    # Write larger number as base pattern
    for i in range(larger + 1):
        if base_pos + i < N:
            state[base_pos + i] = 0
    
    # First R marker after larger number
    r1_pos = base_pos + larger + 1
    if r1_pos < N:
        state[r1_pos] = 2
    
    # Overlap region: encode smaller number by modifying pattern
    overlap_start = r1_pos + 1
    
    # Method: Use blue markers (3) to encode the smaller number
    for i in range(smaller + 1):
        if overlap_start + i < N:
            state[overlap_start + i] = 3  # Blue markers for smaller number
    
    # Final R marker
    r2_pos = overlap_start + max(smaller + 1, 3)  # Ensure minimum spacing
    if r2_pos < N:
        state[r2_pos] = 2
    
    return state, r1_pos, r2_pos

def simulate_commutative_addition(rule, prog, a, b, window=300, max_steps=600):
    """
    Simulate addition with commutative encoding
    """
    state, r1_pos, r2_pos = create_commutative_encoding(a, b, prog, window)
    
    N = len(state)
    sim_range = min(N - 1, r2_pos + max(a + b + 10, 30))
    
    # Run CA simulation
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(1, sim_range):
            if x < N - 1:
                idx = (state[x-1] << 4) | (state[x] << 2) | state[x+1]
                nxt[x] = rule[idx]
        
        # Check for halting: R...R pattern with valid content between
        r_positions = [x for x in range(sim_range) if nxt[x] == 2]
        
        if len(r_positions) >= 2:
            start, end = r_positions[0], r_positions[-1]
            # Check if region between Rs contains only zeros and blues
            between_valid = all(nxt[x] in [0, 3] for x in range(start + 1, end))
            
            if between_valid:
                # Decode: count zeros between R markers
                zero_count = sum(1 for x in range(start + 1, end) if nxt[x] == 0)
                return zero_count, step + 1
        
        state = nxt
    
    return -10, max_steps  # Failed to halt

def evaluate_commutative_model(rule, prog, test_cases):
    """Evaluate model on test cases"""
    correct = 0
    total = len(test_cases)
    
    for a, b in test_cases:
        result, steps = simulate_commutative_addition(rule, prog, a, b)
        expected = a + b
        if result == expected:
            correct += 1
        else:
            print(f"  {a}+{b} = {result} (expected {expected}) - Steps: {steps}")
    
    return correct / total

def train_commutative_addition():
    """Train a model with commutative encoding"""
    
    # Training cases - include both orders to test commutativity
    train_cases = [
        (1, 1), (1, 2), (2, 1), (2, 2),
        (1, 3), (3, 1), (2, 3), (3, 2),
        (3, 3), (1, 4), (4, 1), (2, 4), (4, 2)
    ]
    
    # Test cases - including larger numbers
    test_cases = [
        (1, 1), (2, 1), (1, 2), (3, 1), (1, 3),
        (2, 2), (3, 2), (2, 3), (3, 3),
        (4, 1), (1, 4), (4, 2), (2, 4),
        (5, 1), (1, 5), (6, 1), (1, 6)  # Test larger numbers
    ]
    
    print("Training commutative addition model...")
    print(f"Training cases: {len(train_cases)}")
    print(f"Test cases: {len(test_cases)}")
    
    best_accuracy = 0
    best_model = None
    
    # Generate random programs and rules
    for trial in tqdm(range(100), desc="Training"):
        # Random program (length 12)
        prog = np.random.randint(0, 4, 12, dtype=np.uint8)
        
        # Random rule (64 entries for 4^3 combinations)
        rule = np.random.randint(0, 4, 64, dtype=np.uint8)
        
        # Test on training cases
        train_accuracy = evaluate_commutative_model(rule, prog, train_cases)
        
        if train_accuracy > best_accuracy:
            best_accuracy = train_accuracy
            best_model = (rule.copy(), prog.copy())
            
            print(f"\nTrial {trial}: New best accuracy {train_accuracy:.3f}")
            
            # Test on larger numbers
            print("Testing on extended cases:")
            test_accuracy = evaluate_commutative_model(rule, prog, test_cases)
            print(f"Extended test accuracy: {test_accuracy:.3f}")
            
            # Test commutativity explicitly
            print("Testing commutativity:")
            comm_cases = [(2, 3), (3, 2), (1, 4), (4, 1), (2, 5), (5, 2)]
            for a, b in comm_cases:
                result_ab, _ = simulate_commutative_addition(rule, prog, a, b)
                result_ba, _ = simulate_commutative_addition(rule, prog, b, a)
                comm_ok = result_ab == result_ba == (a + b)
                print(f"  {a}+{b}={result_ab}, {b}+{a}={result_ba} {'✓' if comm_ok else '✗'}")
            
            if train_accuracy >= 0.9:  # Good enough threshold
                break
    
    if best_model:
        rule, prog = best_model
        
        # Final evaluation
        print(f"\nFinal model - Training accuracy: {best_accuracy:.3f}")
        
        # Save model
        model_data = {
            'rule': rule,
            'prog': prog,
            'accuracy': best_accuracy,
            'encoding': 'commutative_overlapping',
            'train_cases': train_cases,
            'test_cases': test_cases
        }
        
        with open('commutative_addition_model.pkl', 'wb') as f:
            pickle.dump(model_data, f)
        
        print("Model saved as 'commutative_addition_model.pkl'")
        
        return rule, prog
    else:
        print("No good model found!")
        return None, None

if __name__ == "__main__":
    train_commutative_addition()
