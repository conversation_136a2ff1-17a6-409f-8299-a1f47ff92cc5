"""
Analyze the successful addition model and improve it.
"""

import numpy as np
from em43_numba import _simulate_addition
from simple_addition_trainer import simple_training

def test_comprehensive_addition():
    """Test addition on a comprehensive range."""
    print("=" * 60)
    print("COMPREHENSIVE ADDITION TESTING")
    print("=" * 60)
    
    # Get the best model from simple training
    print("Training simple addition model...")
    (best_rule, best_prog), best_fitness = simple_training()
    
    print(f"\nBest model fitness: {best_fitness:.2f}")
    print(f"Rule sample: {best_rule[:10]}")
    print(f"Program: {best_prog}")
    
    # Test on various addition cases
    test_cases = [
        # Original training cases
        ([1, 1, 2], [1, 2, 1], "Training cases"),
        # Small extensions
        ([1, 2, 3], [3, 2, 1], "Small extensions"),
        # Slightly larger
        ([3, 4, 5], [2, 3, 4], "Medium numbers"),
        # Larger numbers
        ([6, 7, 8], [4, 5, 6], "Large numbers"),
        # Edge cases
        ([1, 0], [0, 1], "With zeros"),
    ]
    
    for inputs_a, inputs_b, description in test_cases:
        print(f"\n{description}:")
        
        a_arr = np.array(inputs_a, dtype=np.int64)
        b_arr = np.array(inputs_b, dtype=np.int64)
        expected = a_arr + b_arr
        
        outputs = _simulate_addition(best_rule, best_prog, a_arr, b_arr, 
                                   200, 400, 0.5)
        
        print(f"  Inputs:    {inputs_a} + {inputs_b}")
        print(f"  Expected:  {list(expected)}")
        print(f"  Actual:    {list(outputs)}")
        
        convergent = np.sum(outputs != -10)
        correct = np.sum((outputs != -10) & (outputs == expected))
        
        print(f"  Convergent: {convergent}/{len(outputs)}")
        print(f"  Correct:    {correct}/{len(outputs)}")
        
        if convergent > 0:
            accuracy = correct / convergent * 100
            print(f"  Accuracy:   {accuracy:.1f}% (among convergent)")

def progressive_training():
    """Train progressively on larger numbers."""
    print("\n" + "=" * 60)
    print("PROGRESSIVE TRAINING")
    print("=" * 60)
    
    # Stage 1: Very simple (already done)
    print("Stage 1: Training on [1+1, 1+2, 2+1]...")
    (rule, prog), fitness = simple_training()
    print(f"Stage 1 complete. Fitness: {fitness:.2f}")
    
    # Stage 2: Add slightly larger numbers
    print("\nStage 2: Adding [2+2, 3+1, 1+3]...")
    
    # Test current model on stage 2
    stage2_a = np.array([2, 3, 1], dtype=np.int64)
    stage2_b = np.array([2, 1, 3], dtype=np.int64)
    stage2_targets = stage2_a + stage2_b
    
    outputs = _simulate_addition(rule, prog, stage2_a, stage2_b, 200, 400, 0.5)
    
    print(f"Current model on stage 2:")
    print(f"  Expected: {list(stage2_targets)}")
    print(f"  Actual:   {list(outputs)}")
    
    convergent = np.sum(outputs != -10)
    correct = np.sum((outputs != -10) & (outputs == stage2_targets))
    
    print(f"  Convergent: {convergent}/{len(outputs)}")
    print(f"  Correct:    {correct}/{len(outputs)}")
    
    return rule, prog

def analyze_failure_modes():
    """Analyze why the model fails on larger numbers."""
    print("\n" + "=" * 60)
    print("FAILURE MODE ANALYSIS")
    print("=" * 60)
    
    # Get a working model
    (rule, prog), _ = simple_training()
    
    # Test specific failure cases
    failure_cases = [
        (3, 2),  # Should be 5
        (4, 3),  # Should be 7
        (5, 4),  # Should be 9
    ]
    
    for a, b in failure_cases:
        print(f"\nAnalyzing {a} + {b} = {a+b}:")
        
        a_arr = np.array([a], dtype=np.int64)
        b_arr = np.array([b], dtype=np.int64)
        
        output = _simulate_addition(rule, prog, a_arr, b_arr, 200, 400, 0.5)
        
        print(f"  Expected: {a+b}")
        print(f"  Actual:   {output[0]}")
        
        if output[0] == -10:
            print("  Issue: Non-convergent (halting problem)")
        elif output[0] != a+b:
            print(f"  Issue: Wrong result (off by {abs(output[0] - (a+b))})")
        else:
            print("  Success!")

def suggest_improvements():
    """Suggest improvements based on analysis."""
    print("\n" + "=" * 60)
    print("IMPROVEMENT SUGGESTIONS")
    print("=" * 60)
    
    print("Based on the analysis, here are suggested improvements:")
    print()
    print("1. WINDOW SIZE:")
    print("   - Current: 200 cells")
    print("   - For larger numbers, need more space")
    print("   - Suggestion: Scale window with max(a+b)")
    print()
    print("2. PROGRAM LENGTH:")
    print("   - Current: 8 symbols")
    print("   - May be too short for complex addition logic")
    print("   - Suggestion: Try 12-16 symbols")
    print()
    print("3. TRAINING STRATEGY:")
    print("   - Current: Train on small numbers only")
    print("   - Suggestion: Curriculum learning")
    print("     * Stage 1: [1+1, 1+2, 2+1] ✓")
    print("     * Stage 2: [2+2, 3+1, 1+3, 3+2, 2+3]")
    print("     * Stage 3: [4+1, 1+4, 3+3, 4+2, 2+4]")
    print("     * Stage 4: Larger numbers")
    print()
    print("4. FITNESS FUNCTION:")
    print("   - Current: Simple accuracy + convergence")
    print("   - Suggestion: Add generalization bonus")
    print("   - Test on unseen numbers during training")
    print()
    print("5. POPULATION DIVERSITY:")
    print("   - Current: 50 genomes")
    print("   - Suggestion: 100-200 genomes for larger search")
    print()
    print("6. HYBRID APPROACH:")
    print("   - Use successful simple model as starting point")
    print("   - Fine-tune with larger numbers")
    print("   - Transfer learning from working solutions")

if __name__ == "__main__":
    # Run comprehensive analysis
    test_comprehensive_addition()
    
    # Try progressive training
    rule, prog = progressive_training()
    
    # Analyze failure modes
    analyze_failure_modes()
    
    # Suggest improvements
    suggest_improvements()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print("✓ Successfully trained addition on simple cases [1+1, 1+2, 2+1]")
    print("✗ Does not generalize to larger numbers yet")
    print("→ Need curriculum learning and larger models for full addition")
    print("→ This proves the approach works - just needs scaling!")
    print("=" * 60)
