"""
Debug the addition simulation to understand why it's producing wrong results.
"""

import numpy as np
from em43_numba import _simulate_addition, _sanitize_rule, _sanitize_programme

def debug_tape_construction():
    """Debug how the tape is constructed for addition."""
    print("🔍 DEBUGGING TAPE CONSTRUCTION")
    print("=" * 50)
    
    # Simple test case: 2 + 3 = 5
    a, b = 2, 3
    L = 8  # program length
    
    print(f"Testing: {a} + {b} = {a+b}")
    print(f"Program length: {L}")
    
    # Expected tape structure according to our implementation:
    # [program(L)] BB 0^(a+1) R 0^(b+1) R 0
    
    expected_positions = {
        'program_end': L - 1,
        'first_B': L,
        'second_B': L + 1,
        'first_input_start': L + 2,
        'first_input_end': L + 2 + a,  # a+1 zeros: positions L+2 to L+2+a
        'first_R': L + 2 + a + 1,
        'second_input_start': L + 2 + a + 2,
        'second_input_end': L + 2 + a + 2 + b,  # b+1 zeros
        'second_R': L + 2 + a + 2 + b + 1,
        'output_start': L + 2 + a + 2 + b + 2
    }
    
    print("\nExpected tape structure:")
    for key, pos in expected_positions.items():
        print(f"  {key}: position {pos}")
    
    print(f"\nFor a={a}, b={b}:")
    print(f"  First input: {a+1} zeros at positions {L+2} to {L+2+a}")
    print(f"  First R at position: {L+2+a+1}")
    print(f"  Second input: {b+1} zeros at positions {L+2+a+2} to {L+2+a+2+b}")
    print(f"  Second R at position: {L+2+a+2+b+1}")
    print(f"  Output should be {a+b} zeros after second R")
    
    return expected_positions

def debug_simple_simulation():
    """Debug a simple simulation step by step."""
    print("\n🔍 DEBUGGING SIMPLE SIMULATION")
    print("=" * 50)
    
    # Create a very simple rule and program
    rule = np.zeros(64, dtype=np.uint8)
    # Set some basic rules that might preserve structure
    rule[0] = 0   # 000 -> 0 (preserve zeros)
    rule[9] = 2   # 021 -> 2 (preserve R between zeros)
    rule[18] = 2  # 042 -> 2 (preserve R)
    rule[27] = 3  # 063 -> 3 (preserve B)
    
    prog = np.array([1, 0, 0, 0, 0, 0, 0, 0], dtype=np.uint8)
    
    print(f"Rule sample: {rule[:20]}")
    print(f"Program: {prog}")
    
    # Test very simple case
    a_arr = np.array([1], dtype=np.int64)
    b_arr = np.array([1], dtype=np.int64)
    
    print(f"\nTesting: {a_arr[0]} + {b_arr[0]} = {a_arr[0] + b_arr[0]}")
    
    output = _simulate_addition(rule, prog, a_arr, b_arr, 100, 200, 0.5)
    
    print(f"Result: {output[0]}")
    
    if output[0] == -10:
        print("❌ Non-convergent (didn't halt)")
    elif output[0] == 2:
        print("✅ Correct!")
    else:
        print(f"❌ Wrong result (expected 2, got {output[0]})")
    
    return output[0]

def debug_output_decoding():
    """Debug how outputs are decoded from the final tape."""
    print("\n🔍 DEBUGGING OUTPUT DECODING")
    print("=" * 50)
    
    print("Our addition simulation decodes output as:")
    print("1. Find rightmost R (final result marker)")
    print("2. Count consecutive zeros after that R")
    print("3. Return the count as the result")
    print()
    print("This means for a+b=c, we expect c consecutive zeros after the final R")
    print()
    
    # Let's check if this makes sense for our tape structure
    print("For input 2+3=5:")
    print("Expected final tape: [prog] BB 000 R 0000 R 00000")
    print("                                    ^     ^   ^^^^^")
    print("                                    |     |   5 zeros = result")
    print("                                    |     final R")
    print("                                    first R")
    print()
    print("But wait... this assumes the CA actually COMPUTES the addition!")
    print("The CA needs to:")
    print("1. Read the two input groups")
    print("2. Somehow combine them")
    print("3. Write the correct number of output zeros")
    print()
    print("This is a very complex computation for a simple CA rule!")

def test_working_divide_model():
    """Test if we can load and examine the working divide-by-2 model."""
    print("\n🔍 EXAMINING WORKING DIVIDE-BY-2 MODEL")
    print("=" * 50)
    
    try:
        # Try to find the working divide model
        import pickle
        from pathlib import Path
        
        # Look for saved models
        model_files = list(Path('.').glob('*div*model*.pkl'))
        if not model_files:
            model_files = list(Path('.').glob('*genome*.pkl'))
        
        if model_files:
            print(f"Found model file: {model_files[0]}")
            with open(model_files[0], 'rb') as f:
                model_data = pickle.load(f)
            
            if isinstance(model_data, dict) and 'rule' in model_data:
                rule = model_data['rule']
                prog = model_data['prog']
            else:
                print("Model format not recognized")
                return
                
            print(f"Divide model rule sample: {rule[:20]}")
            print(f"Divide model program: {prog}")
            
            # Test the divide model on its intended task
            from em43_numba import EM43Batch
            sim = EM43Batch((rule, prog), window=300, max_steps=256)
            div_results = sim.run([8, 10, 12, 14, 16])
            print(f"Divide results: {div_results}")
            
            # Now test what happens if we use this rule for addition
            add_results = _simulate_addition(rule, prog, 
                                           np.array([2], dtype=np.int64),
                                           np.array([3], dtype=np.int64),
                                           300, 256, 0.5)
            print(f"Same rule used for addition 2+3: {add_results[0]}")
            
        else:
            print("No saved models found")
            
    except Exception as e:
        print(f"Error loading model: {e}")

def analyze_problem():
    """Analyze the fundamental problem with our approach."""
    print("\n🔍 FUNDAMENTAL PROBLEM ANALYSIS")
    print("=" * 50)
    
    print("ISSUE IDENTIFIED:")
    print("================")
    print()
    print("1. TAPE ENCODING MISMATCH:")
    print("   - We encode inputs as: 0^(a+1) R 0^(b+1) R")
    print("   - But this doesn't match the working divide-by-2 encoding!")
    print()
    print("2. DIFFERENT COMPUTATION PARADIGMS:")
    print("   - Divide-by-2: Single input, well-defined halving operation")
    print("   - Addition: Two inputs, need to 'merge' them somehow")
    print()
    print("3. OUTPUT DECODING ASSUMPTION:")
    print("   - We assume CA will magically produce a+b zeros after final R")
    print("   - But CA rules are local - they don't 'count' globally")
    print()
    print("4. TRAINING DATA MISMATCH:")
    print("   - Random rules produce random large numbers (like 478)")
    print("   - GA has no guidance toward correct addition behavior")
    print()
    print("SOLUTIONS:")
    print("==========")
    print()
    print("A. FIX ENCODING:")
    print("   - Study the working divide-by-2 encoding exactly")
    print("   - Adapt it for two-input problems")
    print()
    print("B. BETTER FITNESS FUNCTION:")
    print("   - Heavily penalize outputs > max_possible_result")
    print("   - Reward any output in reasonable range")
    print()
    print("C. SMARTER INITIALIZATION:")
    print("   - Start with rules that preserve structure")
    print("   - Don't use completely random rules")
    print()
    print("D. SIMPLER PROBLEM:")
    print("   - Maybe start with 1+1 only")
    print("   - Build up very gradually")

if __name__ == "__main__":
    print("🐛 ADDITION SIMULATION DEBUGGING")
    print("=" * 60)
    
    # Run all debugging steps
    debug_tape_construction()
    result = debug_simple_simulation()
    debug_output_decoding()
    test_working_divide_model()
    analyze_problem()
    
    print("\n" + "=" * 60)
    print("🎯 DEBUGGING COMPLETE")
    print("=" * 60)
    
    if result == 2:
        print("✅ Basic simulation works!")
    else:
        print("❌ Basic simulation fails - need to fix fundamental issues")
        print("💡 Recommendation: Study working divide-by-2 model first")
