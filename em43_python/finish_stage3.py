"""
Finish Stage 3 curriculum training and analyze performance in detail.
"""

import numpy as np
import time
import pickle
from tqdm import tqdm
from em43_numba import _sanitize_rule, _sanitize_programme

def simulate_addition_optimized(rule, prog, a, b, window=300, max_steps=600, halt_thresh=0.5):
    """Optimized addition simulation."""
    L = len(prog)
    N = window
    
    state = np.zeros(N, dtype=np.uint8)
    
    # Write program and separator
    for j in range(L):
        state[j] = prog[j]
    state[L] = 3      # B
    state[L + 1] = 3  # B
    
    # Write inputs: 0^(a+1) R 0^(b+1) R
    r1_pos = L + 2 + a + 1
    if r1_pos >= N:
        return -10
    state[r1_pos] = 2
    
    r2_pos = r1_pos + 1 + b + 1
    if r2_pos >= N:
        return -10
    state[r2_pos] = 2
    
    # Adaptive simulation range
    sim_range = min(N - 1, r2_pos + max(a + b, 20))
    
    # Run CA simulation
    for step in range(max_steps):
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(1, sim_range):
            idx = (state[x-1] << 4) | (state[x] << 2) | state[x+1]
            nxt[x] = rule[idx]
        
        # Halting check
        live = blue = 0
        for x in range(sim_range):
            v = nxt[x]
            if v != 0:
                live += 1
                if v == 3:
                    blue += 1
        
        if live > 0 and blue / live >= halt_thresh:
            # Find rightmost R
            rpos = -1
            for x in range(sim_range - 1, -1, -1):
                if nxt[x] == 2:
                    rpos = x
                    break
            
            if rpos != -1:
                return rpos - (L + 3)
            else:
                return -10
        
        state = nxt
    
    return -10

def load_stage2_model():
    """Load the successful Stage 2 model."""
    try:
        with open('curriculum_checkpoint_stage2.pkl', 'rb') as f:
            checkpoint = pickle.load(f)
        return checkpoint['model']
    except:
        # Fallback to basic addition model
        with open('addition_model.pkl', 'rb') as f:
            model_data = pickle.load(f)
        return (model_data['rule'], model_data['prog'])

def evaluate_detailed(rule, prog, test_cases):
    """Detailed evaluation with individual case analysis."""
    results = []
    
    for a, b in test_cases:
        expected = a + b
        result = simulate_addition_optimized(rule, prog, a, b, 300, 600, 0.5)
        
        status = "✓" if result == expected else "✗"
        convergent = result != -10
        
        results.append({
            'a': a, 'b': b, 'expected': expected, 'result': result,
            'correct': result == expected, 'convergent': convergent, 'status': status
        })
    
    return results

def finish_stage3_training():
    """Complete Stage 3 training with focused approach."""
    print("🎯 FINISHING STAGE 3 CURRICULUM TRAINING")
    print("=" * 60)
    
    # Stage 3 test cases
    stage3_cases = [(1, 1), (1, 2), (2, 1), (2, 2), (3, 1), (1, 3), 
                    (3, 2), (2, 3), (3, 3), (4, 1), (1, 4)]
    
    print(f"Stage 3 cases ({len(stage3_cases)}): {stage3_cases}")
    print("Target accuracy: 95.0%")
    
    # Load starting model
    initial_rule, initial_prog = load_stage2_model()
    print("✅ Loaded Stage 2 model as starting point")
    
    # Evaluate initial performance
    print("\n📊 INITIAL PERFORMANCE ANALYSIS")
    initial_results = evaluate_detailed(initial_rule, initial_prog, stage3_cases)
    
    correct_count = sum(1 for r in initial_results if r['correct'])
    convergent_count = sum(1 for r in initial_results if r['convergent'])
    
    print(f"Initial accuracy: {correct_count}/{len(stage3_cases)} ({correct_count/len(stage3_cases)*100:.1f}%)")
    print(f"Convergence rate: {convergent_count}/{len(stage3_cases)} ({convergent_count/len(stage3_cases)*100:.1f}%)")
    
    print("\nDetailed results:")
    for r in initial_results:
        print(f"  {r['a']}+{r['b']}={r['expected']} → {r['result']} {r['status']}")
    
    # Identify failing cases
    failing_cases = [r for r in initial_results if not r['correct']]
    print(f"\nFailing cases ({len(failing_cases)}):")
    for r in failing_cases:
        print(f"  {r['a']}+{r['b']}={r['expected']} → {r['result']}")
    
    # Training parameters
    POP_SIZE = 60
    GENERATIONS = 300
    MUTATION_RATE = 0.008  # Very fine-tuning
    
    print(f"\n🚀 FOCUSED TRAINING")
    print(f"Population: {POP_SIZE}, Generations: {GENERATIONS}, Mutation: {MUTATION_RATE}")
    
    # Initialize population around best model
    population = []
    rng = np.random.default_rng()
    
    for i in range(POP_SIZE):
        rule = initial_rule.copy()
        prog = initial_prog.copy()
        
        if i > 0:  # Keep one copy unchanged
            # Very small mutations
            mask = rng.random(64) < MUTATION_RATE
            rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)
            
            mask = rng.random(len(prog)) < MUTATION_RATE * 2
            prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.8, 0.15, 0.05])
        
        rule = _sanitize_rule(rule)
        prog = _sanitize_programme(prog)
        population.append((rule, prog))
    
    # Training loop
    best_accuracy = correct_count / len(stage3_cases)
    best_model = (initial_rule, initial_prog)
    best_fitness = -1000
    
    print("Training...")
    
    for gen in tqdm(range(GENERATIONS), desc="Stage 3 Completion"):
        # Evaluate population
        fitness_scores = []
        
        for rule, prog in population:
            results = evaluate_detailed(rule, prog, stage3_cases)
            
            correct = sum(1 for r in results if r['correct'])
            convergent = sum(1 for r in results if r['convergent'])
            
            accuracy = correct / len(stage3_cases)
            convergence_rate = convergent / len(stage3_cases)
            
            # Enhanced fitness function
            fitness = accuracy * 1000 + convergence_rate * 100
            
            # Bonus for perfect accuracy
            if accuracy == 1.0:
                fitness += 500
            
            # Bonus for improvement on failing cases
            for r in results:
                if r['a'] in [case['a'] for case in failing_cases] and \
                   r['b'] in [case['b'] for case in failing_cases] and r['correct']:
                    fitness += 50  # Extra reward for fixing failing cases
            
            fitness_scores.append(fitness)
        
        # Track best
        gen_best_idx = np.argmax(fitness_scores)
        gen_best_fitness = fitness_scores[gen_best_idx]
        
        gen_results = evaluate_detailed(population[gen_best_idx][0], population[gen_best_idx][1], stage3_cases)
        gen_accuracy = sum(1 for r in gen_results if r['correct']) / len(stage3_cases)
        
        if gen_accuracy > best_accuracy:
            best_accuracy = gen_accuracy
            best_model = population[gen_best_idx]
            best_fitness = gen_best_fitness
        
        # Progress report
        if gen % 50 == 0:
            avg_fitness = np.mean(fitness_scores)
            print(f"  Gen {gen}: best_acc={best_accuracy:.2%}, fitness={best_fitness:.1f}")
            
            # Show current failing cases
            current_failing = [r for r in gen_results if not r['correct']]
            if current_failing:
                print(f"    Still failing: {[(r['a'], r['b']) for r in current_failing]}")
            else:
                print("    🎉 ALL CASES CORRECT!")
        
        # Early stopping if target reached
        if best_accuracy >= 0.95:
            print(f"  🎯 Target accuracy 95% reached at generation {gen}!")
            break
        
        # Selection and reproduction
        if gen < GENERATIONS - 1:
            # Elite selection with focus on diversity
            sorted_indices = np.argsort(fitness_scores)[::-1]
            n_elite = POP_SIZE // 3
            
            new_population = []
            
            # Keep elites
            for i in range(n_elite):
                new_population.append(population[sorted_indices[i]])
            
            # Generate offspring with adaptive mutation
            while len(new_population) < POP_SIZE:
                # Tournament selection
                tournament_size = 3
                tournament_indices = rng.choice(sorted_indices[:POP_SIZE//2], size=tournament_size, replace=False)
                parent_idx = tournament_indices[0]
                
                rule, prog = population[parent_idx]
                
                # Adaptive mutation - higher rate if stuck
                current_mutation_rate = MUTATION_RATE
                if gen > 100 and best_accuracy < 0.92:
                    current_mutation_rate *= 2  # Increase exploration
                
                # Mutate
                new_rule = rule.copy()
                new_prog = prog.copy()
                
                mask = rng.random(64) < current_mutation_rate
                new_rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)
                
                mask = rng.random(len(prog)) < current_mutation_rate * 2
                new_prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.8, 0.15, 0.05])
                
                new_population.append((_sanitize_rule(new_rule), _sanitize_programme(new_prog)))
            
            population = new_population
    
    return best_model, best_accuracy

def comprehensive_analysis(rule, prog):
    """Comprehensive analysis of the final model."""
    print("\n" + "=" * 60)
    print("🔬 COMPREHENSIVE PERFORMANCE ANALYSIS")
    print("=" * 60)
    
    # Test on all curriculum stages
    test_ranges = {
        'Stage 1 (Basic)': [(1, 1), (1, 2), (2, 1)],
        'Stage 2 (Small)': [(1, 1), (1, 2), (2, 1), (2, 2), (3, 1), (1, 3)],
        'Stage 3 (Medium)': [(1, 1), (1, 2), (2, 1), (2, 2), (3, 1), (1, 3), 
                             (3, 2), (2, 3), (3, 3), (4, 1), (1, 4)],
        'Generalization': [(4, 2), (2, 4), (5, 1), (1, 5), (3, 4), (4, 3), (5, 2), (2, 5)]
    }
    
    for range_name, test_cases in test_ranges.items():
        print(f"\n📊 {range_name}:")
        results = evaluate_detailed(rule, prog, test_cases)
        
        correct = sum(1 for r in results if r['correct'])
        convergent = sum(1 for r in results if r['convergent'])
        
        print(f"  Accuracy: {correct}/{len(test_cases)} ({correct/len(test_cases)*100:.1f}%)")
        print(f"  Convergence: {convergent}/{len(test_cases)} ({convergent/len(test_cases)*100:.1f}%)")
        
        # Show results
        for r in results:
            print(f"    {r['a']}+{r['b']}={r['expected']} → {r['result']} {r['status']}")
        
        # Analyze failures
        failures = [r for r in results if not r['correct']]
        if failures:
            print(f"  ❌ Failures: {[(r['a'], r['b']) for r in failures]}")
    
    # Pattern analysis
    print(f"\n🔍 PATTERN ANALYSIS:")
    all_cases = [(a, b) for a in range(1, 6) for b in range(1, 6)]
    all_results = evaluate_detailed(rule, prog, all_cases)
    
    # Analyze by sum
    sum_analysis = {}
    for r in all_results:
        target_sum = r['expected']
        if target_sum not in sum_analysis:
            sum_analysis[target_sum] = {'correct': 0, 'total': 0}
        sum_analysis[target_sum]['total'] += 1
        if r['correct']:
            sum_analysis[target_sum]['correct'] += 1
    
    print("  Performance by target sum:")
    for target_sum in sorted(sum_analysis.keys()):
        data = sum_analysis[target_sum]
        accuracy = data['correct'] / data['total'] * 100
        print(f"    Sum {target_sum}: {data['correct']}/{data['total']} ({accuracy:.1f}%)")

if __name__ == "__main__":
    start_time = time.time()
    
    # Complete Stage 3 training
    best_model, final_accuracy = finish_stage3_training()
    
    print(f"\n🎯 STAGE 3 TRAINING COMPLETE!")
    print(f"Final accuracy: {final_accuracy:.2%}")
    
    if final_accuracy >= 0.95:
        print("🎉 SUCCESS: Target accuracy achieved!")
    else:
        print("⚠️  Close to target - may need more training")
    
    # Comprehensive analysis
    rule, prog = best_model
    comprehensive_analysis(rule, prog)
    
    # Save final Stage 3 model
    stage3_model = {
        'rule': rule,
        'prog': prog,
        'accuracy': final_accuracy,
        'stage': 'stage3_complete'
    }
    
    with open('curriculum_checkpoint_stage3_final.pkl', 'wb') as f:
        pickle.dump(stage3_model, f)
    
    end_time = time.time()
    
    print(f"\n⏱️  Total completion time: {(end_time - start_time)/60:.1f} minutes")
    print(f"💾 Final model saved: curriculum_checkpoint_stage3_final.pkl")
    
    if final_accuracy >= 0.95:
        print("\n🚀 READY FOR STAGE 4: Larger number ranges!")
    else:
        print("\n💡 Consider: More generations, different mutation rates, or problem simplification")
