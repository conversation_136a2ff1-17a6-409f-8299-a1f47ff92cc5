"""
em43_addition_ga.py - Train EM43 to add two numbers
==================================================

Train an emergent model to perform addition: a + b = result
This is simpler than GCD but more complex than single-input operations.

Author: <PERSON><PERSON><PERSON><PERSON>
"""

from __future__ import annotations
import numpy as np, math, pickle, time
from pathlib import Path
from tqdm import tqdm
import matplotlib.pyplot as plt
import numba as nb

nb.set_num_threads(nb.config.NUMBA_NUM_THREADS)
print(f"Numba using {nb.get_num_threads()} threads")

from em43_numba import _simulate_addition, _sanitize_rule, _sanitize_programme

# ───────────────── Training Data ────────────────────────────────────
# Start with small numbers to make the problem tractable
INPUT_A = []
INPUT_B = []
TARGET_OUT = []

# Generate training pairs: small numbers (1-10) + (1-10)
for a in range(1, 11):
    for b in range(1, 11):
        INPUT_A.append(a)
        INPUT_B.append(b)
        TARGET_OUT.append(a + b)

INPUT_A = np.array(INPUT_A, dtype=np.int64)
INPUT_B = np.array(INPUT_B, dtype=np.int64)
TARGET_OUT = np.array(TARGET_OUT, dtype=np.int64)

print(f"Training addition on {len(INPUT_A)} pairs")
print(f"Input range: 1-10 + 1-10 = 2-20")
print("Sample mappings:")
for i in range(min(10, len(INPUT_A))):
    print(f"  {INPUT_A[i]} + {INPUT_B[i]} = {TARGET_OUT[i]}")

# ───────────────── Simulation Parameters ────────────────────────────
WINDOW = 400             # Need more space for addition results
MAX_STEPS = 800          # More steps for complex computation
HALT_THRESH = 0.50       # Standard halting

# ───────────────── GA Parameters ─────────────────────────────────────
POP_SIZE = 200           # Larger population for complex task
L_PROG = 16              # Program length
GENERATIONS = 1000       # More generations needed
ELITE_FRAC = 0.1         # Top 10%
MUTATE_RATE = 0.15       # Higher mutation for exploration
CROSSOVER_RATE = 0.7     # Standard crossover
IMMIGRANT_RATE = 0.05    # Random immigrants for diversity

# ───────────────── Checkpointing ────────────────────────────────────
CHECK_EVERY = 50
SAVE_DIR = Path("addition_checkpoints")
SAVE_DIR.mkdir(exist_ok=True)

rng = np.random.default_rng()

@nb.njit(parallel=True, fastmath=True, cache=True)
def fitness_population(rules: np.ndarray, progs: np.ndarray) -> np.ndarray:
    """Compute fitness for addition function."""
    P = rules.shape[0]
    fitness = np.empty(P, dtype=np.float32)
    
    for i in nb.prange(P):
        outs = _simulate_addition(rules[i], progs[i], INPUT_A, INPUT_B, 
                                 WINDOW, MAX_STEPS, HALT_THRESH)
        
        # Calculate accuracy and penalties
        correct = 0
        total_error = 0.0
        convergence_penalty = 0.0
        
        for j in range(len(outs)):
            predicted = outs[j]
            target = TARGET_OUT[j]
            
            if predicted == -10:  # Non-convergent
                convergence_penalty += 5.0
            elif predicted == target:
                correct += 1
            else:
                # Penalty proportional to error magnitude
                error = abs(predicted - target)
                total_error += error * 1.5
        
        # Fitness: maximize accuracy, minimize error and convergence issues
        accuracy = correct / len(outs)
        avg_error = total_error / len(outs)
        convergence_penalty /= len(outs)
        
        # Sparsity bonus (prefer simpler programs)
        sparsity = np.count_nonzero(progs[i]) / len(progs[i])
        sparsity_penalty = sparsity * 0.1
        
        fitness[i] = accuracy * 100 - avg_error - convergence_penalty - sparsity_penalty
    
    return fitness

def random_genome():
    """Generate random rule and program."""
    rule = rng.integers(0, 4, 64, dtype=np.uint8)
    prog = rng.choice([0, 1, 2], size=L_PROG, p=[0.7, 0.2, 0.1])
    return _sanitize_rule(rule), _sanitize_programme(prog)

def mutate_genome(rule, prog, rate=MUTATE_RATE):
    """Mutate a genome."""
    new_rule = rule.copy()
    new_prog = prog.copy()
    
    # Mutate rule
    mask = rng.random(64) < rate
    new_rule[mask] = rng.integers(0, 4, np.sum(mask), dtype=np.uint8)
    
    # Mutate program
    mask = rng.random(L_PROG) < rate
    new_prog[mask] = rng.choice([0, 1, 2], size=np.sum(mask), p=[0.7, 0.2, 0.1])
    
    return _sanitize_rule(new_rule), _sanitize_programme(new_prog)

def crossover_genomes(rule1, prog1, rule2, prog2):
    """Single-point crossover."""
    # Rule crossover
    cut = rng.integers(1, 63)
    new_rule = np.concatenate([rule1[:cut], rule2[cut:]])
    
    # Program crossover
    cut = rng.integers(1, L_PROG-1)
    new_prog = np.concatenate([prog1[:cut], prog2[cut:]])
    
    return _sanitize_rule(new_rule), _sanitize_programme(new_prog)

def test_generalization(rule, prog):
    """Test on some unseen addition pairs."""
    test_a = np.array([11, 12, 13, 15, 8], dtype=np.int64)
    test_b = np.array([3, 4, 7, 5, 12], dtype=np.int64)
    test_targets = test_a + test_b
    
    test_outs = _simulate_addition(rule, prog, test_a, test_b, 
                                  WINDOW, MAX_STEPS, HALT_THRESH)
    
    correct = 0
    for predicted, target in zip(test_outs, test_targets):
        if predicted == target:
            correct += 1
    
    return correct / len(test_targets) * 100

def run_addition_ga():
    """Run genetic algorithm for addition function."""
    
    # Initialize population
    pop_rules = np.empty((POP_SIZE, 64), np.uint8)
    pop_progs = np.empty((POP_SIZE, L_PROG), np.uint8)
    
    for i in range(POP_SIZE):
        r, p = random_genome()
        pop_rules[i], pop_progs[i] = r, p
    
    best_fitness = -np.inf
    best_rule, best_prog = None, None
    fitness_history = []
    
    print(f"Starting GA: {POP_SIZE} genomes, {GENERATIONS} generations")
    print(f"Elite: {int(POP_SIZE * ELITE_FRAC)}, Mutation: {MUTATE_RATE}")
    
    for gen in tqdm(range(GENERATIONS), desc="Training"):
        # Evaluate population
        fitness = fitness_population(pop_rules, pop_progs)
        
        # Track best
        gen_best_idx = np.argmax(fitness)
        gen_best_fitness = fitness[gen_best_idx]
        
        if gen_best_fitness > best_fitness:
            best_fitness = gen_best_fitness
            best_rule = pop_rules[gen_best_idx].copy()
            best_prog = pop_progs[gen_best_idx].copy()
        
        fitness_history.append({
            'best': best_fitness,
            'mean': np.mean(fitness),
            'std': np.std(fitness)
        })
        
        # Progress report
        if gen % 50 == 0:
            gen_acc = test_generalization(best_rule, best_prog)
            print(f"Gen {gen}: best={best_fitness:.2f}, "
                  f"mean={np.mean(fitness):.2f}, "
                  f"generalization={gen_acc:.1f}%")
        
        # Checkpoint
        if gen % CHECK_EVERY == 0 and gen > 0:
            checkpoint = {
                'generation': gen,
                'best_fitness': best_fitness,
                'best_rule': best_rule,
                'best_prog': best_prog,
                'fitness_history': fitness_history
            }
            with open(SAVE_DIR / f"checkpoint_gen_{gen}.pkl", "wb") as f:
                pickle.dump(checkpoint, f)
        
        # Selection and reproduction
        if gen < GENERATIONS - 1:
            # Sort by fitness
            sorted_indices = np.argsort(fitness)[::-1]
            n_elite = int(POP_SIZE * ELITE_FRAC)
            
            # Next generation
            next_rules = np.empty_like(pop_rules)
            next_progs = np.empty_like(pop_progs)
            
            # Elites
            for i in range(n_elite):
                idx = sorted_indices[i]
                next_rules[i] = pop_rules[idx]
                next_progs[i] = pop_progs[idx]
            
            # Offspring
            for i in range(n_elite, POP_SIZE - int(POP_SIZE * IMMIGRANT_RATE)):
                if rng.random() < CROSSOVER_RATE:
                    # Crossover
                    p1_idx = rng.choice(sorted_indices[:POP_SIZE//2])
                    p2_idx = rng.choice(sorted_indices[:POP_SIZE//2])
                    r, p = crossover_genomes(pop_rules[p1_idx], pop_progs[p1_idx],
                                           pop_rules[p2_idx], pop_progs[p2_idx])
                else:
                    # Mutation only
                    parent_idx = rng.choice(sorted_indices[:POP_SIZE//2])
                    r, p = mutate_genome(pop_rules[parent_idx], pop_progs[parent_idx])
                
                next_rules[i] = r
                next_progs[i] = p
            
            # Random immigrants
            n_immigrants = int(POP_SIZE * IMMIGRANT_RATE)
            for i in range(POP_SIZE - n_immigrants, POP_SIZE):
                r, p = random_genome()
                next_rules[i] = r
                next_progs[i] = p
            
            pop_rules, pop_progs = next_rules, next_progs
    
    # Final evaluation
    final_gen_acc = test_generalization(best_rule, best_prog)
    print(f"\nTraining completed!")
    print(f"Best fitness: {best_fitness:.2f}")
    print(f"Final generalization: {final_gen_acc:.1f}%")
    
    # Save final model
    final_model = {
        'rule': best_rule,
        'prog': best_prog,
        'fitness': best_fitness,
        'generalization': final_gen_acc,
        'fitness_history': fitness_history
    }
    
    with open("best_addition_genome.pkl", "wb") as f:
        pickle.dump(final_model, f)
    
    # Plot fitness curve
    plt.figure(figsize=(10, 6))
    bests = [h['best'] for h in fitness_history]
    means = [h['mean'] for h in fitness_history]
    plt.plot(bests, label='Best', linewidth=2)
    plt.plot(means, label='Mean', alpha=0.7)
    plt.xlabel('Generation')
    plt.ylabel('Fitness')
    plt.title('Addition Training Progress')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('addition_training_curve.png', dpi=150)
    plt.show()

if __name__ == "__main__":
    t0 = time.time()
    run_addition_ga()
    print(f"Total training time: {time.time()-t0:.1f}s")
