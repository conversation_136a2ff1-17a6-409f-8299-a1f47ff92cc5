# EM43 Project Structure

This repository is organized into the following main components:

## 📁 Folder Structure

### `shared_core/`
Core EM43 implementation and utilities used across all models.

### `traditional_em43/`
Original EM43 implementation with basic genetic algorithm training.

### `divide_by_2_model/`
EM43 model trained for integer division by 2 - first successful mathematical operation.

### `addition_model/`
Comprehensive development of EM43 for addition computation:
- Multiple training approaches (curriculum, nonlocal, commutative)
- Perfect accuracy model with 100% generalization
- Extensive analysis and debugging tools
- Interactive web viewers

### `gcd_model/`
Latest EM43 model for Greatest Common Divisor computation:
- Commutative nonlocal training
- 60.7% accuracy with perfect commutativity
- Specialized for coprime detection

## 🚀 Getting Started

1. **Install dependencies**: `pip install -r shared_core/requirements.txt`
2. **Try the addition model**: Open `addition_model/perfect_addition_viewer.html`
3. **Try the GCD model**: Open `gcd_model/gcd_viewer_trained.html`
4. **Explore training**: Run scripts in respective model folders

## 🎯 Key Achievements

- **Perfect Addition**: 100% accuracy with unlimited generalization
- **Nonlocal Rules**: Breakthrough enabling long-range information propagation
- **Commutative Computation**: GCD(a,b) = GCD(b,a) working correctly
- **Emergent Mathematics**: True mathematical reasoning in cellular automata

Each folder contains detailed README files with specific information about that component.
