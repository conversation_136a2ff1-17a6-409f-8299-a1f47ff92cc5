#!/usr/bin/env python3
"""
Organize EM43 codebase into logical folders
"""

import os
import shutil
from pathlib import Path

def organize_codebase():
    """Organize the EM43 codebase into logical folders"""
    
    # Define the organization structure
    organization = {
        'shared_core': [
            'em43_python/em43_numba.py',
            'em43_python/em43_infer.py', 
            'em43_python/eval_best_model.py',
            'em43_python/see_genome.py',
            'em43_python/extract_genome.py',
            'requirements.txt',
            'README.md'
        ],
        
        'traditional_em43': [
            'em43_python/em43_numba_ga.py',
            'em43.html',
            'em43_cpp/',
            'models/',
            'outputs/'
        ],
        
        'divide_by_2_model': [
            'em43_python/em43_div2_ga.py',
            'em43_python/em43_div2_viewer.html',
            'em43_python/div2_checkpoints/',
            'em43_python/best_genome.pkl'
        ],
        
        'addition_model': [
            # Core addition training files
            'em43_python/em43_addition_ga.py',
            'em43_python/simple_addition_trainer.py',
            'em43_python/fast_addition_trainer.py',
            'em43_python/fixed_addition_trainer.py',
            'em43_python/improved_addition_trainer.py',
            'em43_python/extended_addition_trainer.py',
            
            # Curriculum training
            'em43_python/curriculum_addition_trainer.py',
            'em43_python/corrected_curriculum_training.py',
            'em43_python/curriculum_with_improved_decoding.py',
            'em43_python/finish_stage3.py',
            'em43_python/resume_curriculum_stage4.py',
            'em43_python/stage3_5_bridge.py',
            'em43_python/stage4_optimized.py',
            'em43_python/stage5_extended.py',
            
            # Nonlocal addition
            'em43_python/adaptive_nonlocal_training.py',
            'em43_python/curriculum_nonlocal_training.py',
            'em43_python/minimal_nonlocal_training.py',
            'em43_python/focused_nonlocal_training.py',
            'em43_python/extended_adaptive_training.py',
            'em43_python/perfect_accuracy_training.py',
            'em43_python/fix_one_plus_one.py',
            
            # Commutative addition
            'em43_python/commutative_addition_trainer.py',
            'em43_python/nonlocal_commutative_addition.py',
            'em43_python/commutative_demo.py',
            'em43_python/commutative_demo.html',
            
            # Analysis and debugging
            'em43_python/addition_analysis.py',
            'em43_python/debug_addition.py',
            'em43_python/debug_nonlocal.py',
            'em43_python/debug_simulation.py',
            'em43_python/debug_discrepancy.py',
            'em43_python/decoding_experiments.py',
            'em43_python/test_addition_simulation.py',
            'em43_python/test_generalization.py',
            'em43_python/test_extreme_generalization.py',
            'em43_python/test_corrected_generalization.py',
            
            # Analysis of trained models
            'em43_python/analyze_nonlocal_mechanism.py',
            'em43_python/analyze_perfect_model_rules.py',
            'em43_python/analyze_rule_selection_logic.py',
            'em43_python/get_full_model_data.py',
            'em43_python/extract_perfect_model.py',
            
            # Viewers and demos
            'em43_python/create_addition_viewer.py',
            'em43_python/em43_addition_viewer.html',
            'em43_python/perfect_addition_viewer.html',
            'em43_python/nonlocal_demo.py',
            'em43_python/nonlocal_demo.html',
            'em43_python/nonlocal_visualizer.py',
            
            # Model files and checkpoints
            'em43_python/addition_model.pkl',
            'em43_python/improved_addition_model.pkl',
            'em43_python/extended_addition_model.pkl',
            'em43_python/adaptive_nonlocal_model.pkl',
            'em43_python/curriculum_nonlocal_model.pkl',
            'em43_python/extended_adaptive_model.pkl',
            'em43_python/fixed_adaptive_model.pkl',
            'em43_python/perfect_adaptive_model.pkl',
            
            # Checkpoints
            'em43_python/addition_checkpoints/',
            'em43_python/addition_checkpoint_stage1.pkl',
            'em43_python/addition_checkpoint_stage2.pkl',
            'em43_python/addition_checkpoint_stage3.pkl',
            'em43_python/addition_checkpoint_stage4.pkl',
            'em43_python/addition_checkpoint_stage5.pkl',
            'em43_python/curriculum_checkpoint_stage1.pkl',
            'em43_python/curriculum_checkpoint_stage2.pkl',
            'em43_python/curriculum_checkpoint_stage3_5_final.pkl',
            'em43_python/corrected_curriculum_checkpoint_stage1.pkl',
            'em43_python/corrected_curriculum_checkpoint_stage2.pkl',
            'em43_python/corrected_curriculum_checkpoint_stage3.pkl',
            'em43_python/corrected_curriculum_final_model.pkl',
            'em43_python/improved_curriculum_checkpoint_stage1.pkl',
            'em43_python/improved_curriculum_checkpoint_stage2.pkl',
            'em43_python/improved_curriculum_checkpoint_stage3.pkl',
            
            # Documentation and analysis
            'em43_python/advanced_training_approaches.md',
            'em43_python/commutative_nonlocal_summary.md',
            'em43_python/nonlocal_rules_analysis.md',
            'em43_python/addition_training_progress.png'
        ],
        
        'gcd_model': [
            'em43_python/commutative_nonlocal_gcd_trainer.py',
            'em43_python/test_gcd_trainer.py',
            'em43_python/analyze_gcd_model.py',
            'em43_python/create_gcd_viewer.py',
            'em43_python/gcd_viewer.html',
            'em43_python/gcd_viewer_trained.html',
            'em43_python/best_gcd_model.pkl'
        ]
    }
    
    print("🗂️  ORGANIZING EM43 CODEBASE")
    print("=" * 50)
    
    # Create directories and move files
    for folder, files in organization.items():
        print(f"\n📁 Creating {folder}/")
        os.makedirs(folder, exist_ok=True)
        
        for file_path in files:
            if os.path.exists(file_path):
                # Determine destination
                if os.path.isdir(file_path):
                    # For directories, copy the entire directory
                    dest_dir = os.path.join(folder, os.path.basename(file_path))
                    if os.path.exists(dest_dir):
                        shutil.rmtree(dest_dir)
                    shutil.copytree(file_path, dest_dir)
                    print(f"  📂 Copied {file_path} → {dest_dir}")
                else:
                    # For files, copy to the folder
                    dest_file = os.path.join(folder, os.path.basename(file_path))
                    shutil.copy2(file_path, dest_file)
                    print(f"  📄 Copied {file_path} → {dest_file}")
            else:
                print(f"  ⚠️  File not found: {file_path}")
    
    # Create README files for each folder
    readme_contents = {
        'shared_core': """# Shared Core Components

This folder contains the core EM43 implementation and utilities shared across all models:

- `em43_numba.py` - Core EM43 cellular automaton implementation with Numba optimization
- `em43_infer.py` - Inference utilities for trained models
- `eval_best_model.py` - Model evaluation utilities
- `see_genome.py` - Genome inspection tools
- `extract_genome.py` - Genome extraction utilities
- `requirements.txt` - Python dependencies
- `README.md` - Main project documentation
""",
        
        'traditional_em43': """# Traditional EM43

This folder contains the original EM43 implementation and basic genetic algorithm training:

- `em43_numba_ga.py` - Basic genetic algorithm for EM43 training
- `em43.html` - Original EM43 web viewer
- `em43_cpp/` - C++ implementation (if available)
- `models/` - Trained model storage
- `outputs/` - Training outputs and results

This represents the baseline EM43 approach before specialized mathematical training.
""",
        
        'divide_by_2_model': """# Divide by 2 Model

This folder contains the EM43 model trained to perform integer division by 2:

- `em43_div2_ga.py` - Genetic algorithm training for division by 2
- `em43_div2_viewer.html` - Interactive web viewer for the divide-by-2 model
- `div2_checkpoints/` - Training checkpoints
- `best_genome.pkl` - Best trained model for division by 2

This was one of the first successful mathematical operations achieved with EM43.
""",
        
        'addition_model': """# Addition Model

This folder contains the comprehensive development of EM43 models for addition:

## Core Training Scripts
- Basic trainers: `simple_addition_trainer.py`, `fast_addition_trainer.py`, etc.
- Curriculum learning: `curriculum_addition_trainer.py`, `corrected_curriculum_training.py`
- Nonlocal rules: `adaptive_nonlocal_training.py`, `curriculum_nonlocal_training.py`
- Commutative addition: `commutative_addition_trainer.py`, `nonlocal_commutative_addition.py`

## Analysis and Debugging
- Model analysis: `analyze_nonlocal_mechanism.py`, `analyze_perfect_model_rules.py`
- Testing: `test_generalization.py`, `test_extreme_generalization.py`
- Debugging: `debug_addition.py`, `debug_nonlocal.py`

## Viewers and Demos
- `perfect_addition_viewer.html` - Interactive viewer for the perfect addition model
- `nonlocal_demo.html` - Demonstration of nonlocal rules
- `commutative_demo.html` - Commutative addition demonstration

## Trained Models
- `perfect_adaptive_model.pkl` - The breakthrough model with 100% accuracy
- Various checkpoints and intermediate models

This represents the most successful EM43 mathematical computation achievement.
""",
        
        'gcd_model': """# GCD Model

This folder contains the EM43 model trained to compute Greatest Common Divisor (GCD):

- `commutative_nonlocal_gcd_trainer.py` - Main GCD training script using nonlocal rules
- `test_gcd_trainer.py` - Testing and validation scripts
- `analyze_gcd_model.py` - Comprehensive model analysis
- `create_gcd_viewer.py` - HTML viewer generator
- `gcd_viewer_trained.html` - Interactive web viewer for the trained GCD model
- `best_gcd_model.pkl` - Best trained GCD model

## Results
The GCD model achieves:
- 60.7% accuracy on test cases
- 100% convergence (no timeouts)
- Perfect commutativity: GCD(a,b) = GCD(b,a)
- Excellent detection of coprime pairs (GCD=1)

This represents the latest advancement in EM43 mathematical computation.
"""
    }
    
    # Write README files
    print(f"\n📝 Creating README files...")
    for folder, content in readme_contents.items():
        readme_path = os.path.join(folder, 'README.md')
        with open(readme_path, 'w') as f:
            f.write(content)
        print(f"  📄 Created {readme_path}")
    
    # Create main project structure README
    main_readme = """# EM43 Project Structure

This repository is organized into the following main components:

## 📁 Folder Structure

### `shared_core/`
Core EM43 implementation and utilities used across all models.

### `traditional_em43/`
Original EM43 implementation with basic genetic algorithm training.

### `divide_by_2_model/`
EM43 model trained for integer division by 2 - first successful mathematical operation.

### `addition_model/`
Comprehensive development of EM43 for addition computation:
- Multiple training approaches (curriculum, nonlocal, commutative)
- Perfect accuracy model with 100% generalization
- Extensive analysis and debugging tools
- Interactive web viewers

### `gcd_model/`
Latest EM43 model for Greatest Common Divisor computation:
- Commutative nonlocal training
- 60.7% accuracy with perfect commutativity
- Specialized for coprime detection

## 🚀 Getting Started

1. **Install dependencies**: `pip install -r shared_core/requirements.txt`
2. **Try the addition model**: Open `addition_model/perfect_addition_viewer.html`
3. **Try the GCD model**: Open `gcd_model/gcd_viewer_trained.html`
4. **Explore training**: Run scripts in respective model folders

## 🎯 Key Achievements

- **Perfect Addition**: 100% accuracy with unlimited generalization
- **Nonlocal Rules**: Breakthrough enabling long-range information propagation
- **Commutative Computation**: GCD(a,b) = GCD(b,a) working correctly
- **Emergent Mathematics**: True mathematical reasoning in cellular automata

Each folder contains detailed README files with specific information about that component.
"""
    
    with open('PROJECT_STRUCTURE.md', 'w') as f:
        f.write(main_readme)
    print(f"  📄 Created PROJECT_STRUCTURE.md")
    
    print(f"\n✅ ORGANIZATION COMPLETE!")
    print(f"📊 Summary:")
    for folder in organization.keys():
        file_count = len([f for f in organization[folder] if os.path.exists(f)])
        print(f"  {folder}: {file_count} items")

if __name__ == "__main__":
    organize_codebase()
