# Addition Model

This folder contains the comprehensive development of EM43 models for addition:

## Core Training Scripts
- Basic trainers: `simple_addition_trainer.py`, `fast_addition_trainer.py`, etc.
- Curriculum learning: `curriculum_addition_trainer.py`, `corrected_curriculum_training.py`
- Nonlocal rules: `adaptive_nonlocal_training.py`, `curriculum_nonlocal_training.py`
- Commutative addition: `commutative_addition_trainer.py`, `nonlocal_commutative_addition.py`

## Analysis and Debugging
- Model analysis: `analyze_nonlocal_mechanism.py`, `analyze_perfect_model_rules.py`
- Testing: `test_generalization.py`, `test_extreme_generalization.py`
- Debugging: `debug_addition.py`, `debug_nonlocal.py`

## Viewers and Demos
- `perfect_addition_viewer.html` - Interactive viewer for the perfect addition model
- `nonlocal_demo.html` - Demonstration of nonlocal rules
- `commutative_demo.html` - Commutative addition demonstration

## Trained Models
- `perfect_adaptive_model.pkl` - The breakthrough model with 100% accuracy
- Various checkpoints and intermediate models

This represents the most successful EM43 mathematical computation achievement.
