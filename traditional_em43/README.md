# Traditional EM43

This folder contains the original EM43 implementation and basic genetic algorithm training:

- `em43_numba_ga.py` - Basic genetic algorithm for EM43 training
- `em43.html` - Original EM43 web viewer
- `em43_cpp/` - C++ implementation (if available)
- `models/` - Trained model storage
- `outputs/` - Training outputs and results

This represents the baseline EM43 approach before specialized mathematical training.
