"""
Comprehensive GCD Model Validation

This script provides thorough testing and validation of the enhanced GCD model,
including commutativity tests, edge cases, generalization, and performance analysis.
"""

import numpy as np
import pickle
import sys
import os
import time
from typing import List, Tuple, Dict, Any
import matplotlib.pyplot as plt

# Add em43_python to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'em43_python'))

from enhanced_gcd_trainer import EnhancedGCDModel, evaluate_enhanced_gcd_model, gcd
from gcd_curriculum_framework import GCDCurriculumFramework

class ComprehensiveGCDValidator:
    """Comprehensive validation suite for GCD models."""
    
    def __init__(self, model_path: str):
        """Load model and initialize validator."""
        self.model = self._load_model(model_path)
        self.curriculum = GCDCurriculumFramework()
        self.validation_results = {}
        
    def _load_model(self, model_path: str) -> EnhancedGCDModel:
        """Load enhanced GCD model from file."""
        try:
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            if 'rule' in model_data:
                return EnhancedGCDModel(
                    model_data['rule'],
                    model_data['prog'],
                    model_data['encoding_params'],
                    model_data['decoding_params'],
                    model_data['halting_params']
                )
            else:
                return model_data
                
        except Exception as e:
            raise ValueError(f"Failed to load model from {model_path}: {e}")
    
    def test_commutativity(self, test_cases: List[Tuple[int, int]]) -> Dict[str, Any]:
        """Test if GCD(a,b) = GCD(b,a) for all test cases."""
        print("🔄 TESTING COMMUTATIVITY")
        print("=" * 50)
        
        violations = []
        total_tests = 0
        
        for a, b in test_cases:
            if a != b:  # Only test when a != b
                try:
                    result_ab = self.model.simulate(a, b, max_steps=600)
                    result_ba = self.model.simulate(b, a, max_steps=600)
                    expected = gcd(a, b)
                    
                    is_commutative = (result_ab == result_ba)
                    both_correct = (result_ab == expected and result_ba == expected)
                    
                    if not is_commutative:
                        violations.append({
                            'a': a, 'b': b, 'result_ab': result_ab, 'result_ba': result_ba,
                            'expected': expected
                        })
                        print(f"  ❌ GCD({a},{b})={result_ab}, GCD({b},{a})={result_ba} (expected {expected})")
                    elif both_correct:
                        print(f"  ✅ GCD({a},{b})=GCD({b},{a})={result_ab} (correct)")
                    else:
                        print(f"  🔶 GCD({a},{b})=GCD({b},{a})={result_ab} (commutative but wrong, expected {expected})")
                    
                    total_tests += 1
                    
                except Exception as e:
                    print(f"  ⚠️ Error testing GCD({a},{b}): {e}")
        
        commutativity_rate = (total_tests - len(violations)) / total_tests if total_tests > 0 else 0
        
        result = {
            'commutativity_rate': commutativity_rate,
            'violations': violations,
            'total_tests': total_tests
        }
        
        print(f"\nCommutativity Results:")
        print(f"  Rate: {commutativity_rate:.3f} ({total_tests - len(violations)}/{total_tests})")
        print(f"  Violations: {len(violations)}")
        
        return result
    
    def test_edge_cases(self) -> Dict[str, Any]:
        """Test edge cases and special scenarios."""
        print("\n🎯 TESTING EDGE CASES")
        print("=" * 50)
        
        edge_cases = [
            # Same numbers
            (1, 1), (5, 5), (10, 10), (15, 15),
            
            # One is 1
            (1, 7), (1, 15), (1, 25), (1, 50),
            
            # Large differences
            (2, 50), (3, 48), (5, 45), (7, 42),
            
            # Prime numbers
            (7, 11), (11, 13), (13, 17), (17, 19),
            
            # Powers of 2
            (4, 8), (8, 16), (16, 32), (2, 64),
            
            # Fibonacci-like
            (3, 5), (5, 8), (8, 13), (13, 21),
        ]
        
        results = []
        categories = {
            'same_numbers': [],
            'one_is_one': [],
            'large_differences': [],
            'prime_numbers': [],
            'powers_of_two': [],
            'fibonacci_like': []
        }
        
        for i, (a, b) in enumerate(edge_cases):
            expected = gcd(a, b)
            try:
                result = self.model.simulate(a, b, max_steps=800)
                is_correct = (result == expected)
                is_convergent = (result != -10)
                
                test_result = {
                    'a': a, 'b': b, 'expected': expected, 'result': result,
                    'correct': is_correct, 'convergent': is_convergent
                }
                results.append(test_result)
                
                # Categorize
                if a == b:
                    categories['same_numbers'].append(test_result)
                elif a == 1 or b == 1:
                    categories['one_is_one'].append(test_result)
                elif max(a, b) / min(a, b) > 5:
                    categories['large_differences'].append(test_result)
                elif self._is_prime(a) and self._is_prime(b):
                    categories['prime_numbers'].append(test_result)
                elif self._is_power_of_two(a) or self._is_power_of_two(b):
                    categories['powers_of_two'].append(test_result)
                else:
                    categories['fibonacci_like'].append(test_result)
                
                status = "✅" if is_correct else ("⏰" if not is_convergent else "❌")
                print(f"  GCD({a:2d},{b:2d})={expected:2d} → {result:2d} {status}")
                
            except Exception as e:
                print(f"  GCD({a},{b}): ERROR - {e}")
        
        # Calculate category accuracies
        category_stats = {}
        for cat_name, cat_results in categories.items():
            if cat_results:
                accuracy = sum(1 for r in cat_results if r['correct']) / len(cat_results)
                convergence = sum(1 for r in cat_results if r['convergent']) / len(cat_results)
                category_stats[cat_name] = {
                    'accuracy': accuracy,
                    'convergence': convergence,
                    'count': len(cat_results)
                }
        
        overall_accuracy = sum(1 for r in results if r['correct']) / len(results)
        overall_convergence = sum(1 for r in results if r['convergent']) / len(results)
        
        print(f"\nEdge Case Results:")
        print(f"  Overall accuracy: {overall_accuracy:.3f}")
        print(f"  Overall convergence: {overall_convergence:.3f}")
        
        for cat_name, stats in category_stats.items():
            print(f"  {cat_name.replace('_', ' ').title()}: {stats['accuracy']:.3f} ({stats['count']} cases)")
        
        return {
            'overall_accuracy': overall_accuracy,
            'overall_convergence': overall_convergence,
            'category_stats': category_stats,
            'results': results
        }
    
    def test_generalization(self, max_number: int = 30) -> Dict[str, Any]:
        """Test generalization to larger numbers."""
        print(f"\n🚀 TESTING GENERALIZATION (up to {max_number})")
        print("=" * 50)
        
        # Generate systematic test cases
        test_cases = []
        
        # Sample across different ranges
        for range_start in [1, 10, 20]:
            range_end = min(range_start + 15, max_number + 1)
            for a in range(range_start, range_end, 2):
                for b in range(range_start, range_end, 3):
                    if a <= max_number and b <= max_number:
                        test_cases.append((a, b))
        
        # Remove duplicates
        test_cases = list(set(test_cases))[:50]  # Limit to 50 cases
        
        results = []
        size_groups = {}
        
        for a, b in test_cases:
            expected = gcd(a, b)
            try:
                result = self.model.simulate(a, b, max_steps=1000)
                is_correct = (result == expected)
                is_convergent = (result != -10)
                
                test_result = {
                    'a': a, 'b': b, 'expected': expected, 'result': result,
                    'correct': is_correct, 'convergent': is_convergent
                }
                results.append(test_result)
                
                # Group by size
                size_key = (a + b) // 10  # Group by sum ranges
                if size_key not in size_groups:
                    size_groups[size_key] = []
                size_groups[size_key].append(test_result)
                
                if len(results) <= 20:  # Show first 20 results
                    status = "✅" if is_correct else ("⏰" if not is_convergent else "❌")
                    print(f"  GCD({a:2d},{b:2d})={expected:2d} → {result:2d} {status}")
                
            except Exception as e:
                print(f"  GCD({a},{b}): ERROR - {e}")
        
        if len(results) > 20:
            print(f"  ... and {len(results) - 20} more cases")
        
        # Analyze by size groups
        group_stats = {}
        for size_key, group_results in size_groups.items():
            accuracy = sum(1 for r in group_results if r['correct']) / len(group_results)
            convergence = sum(1 for r in group_results if r['convergent']) / len(group_results)
            group_stats[size_key] = {
                'accuracy': accuracy,
                'convergence': convergence,
                'count': len(group_results),
                'size_range': f"{size_key*10}-{(size_key+1)*10-1}"
            }
        
        overall_accuracy = sum(1 for r in results if r['correct']) / len(results)
        overall_convergence = sum(1 for r in results if r['convergent']) / len(results)
        
        print(f"\nGeneralization Results:")
        print(f"  Overall accuracy: {overall_accuracy:.3f}")
        print(f"  Overall convergence: {overall_convergence:.3f}")
        
        for size_key in sorted(group_stats.keys()):
            stats = group_stats[size_key]
            print(f"  Size {stats['size_range']}: {stats['accuracy']:.3f} ({stats['count']} cases)")
        
        return {
            'overall_accuracy': overall_accuracy,
            'overall_convergence': overall_convergence,
            'group_stats': group_stats,
            'results': results
        }
    
    def test_curriculum_phases(self) -> Dict[str, Any]:
        """Test performance on each curriculum phase."""
        print("\n📚 TESTING CURRICULUM PHASES")
        print("=" * 50)
        
        phase_results = {}
        
        for phase_name in self.curriculum.get_all_phases():
            phase_config = self.curriculum.get_phase(phase_name)
            test_cases = phase_config['cases']
            target_accuracy = phase_config['target_accuracy']
            
            print(f"\n{phase_config['name']}:")
            
            accuracy, convergence, results = evaluate_enhanced_gcd_model(self.model, test_cases)
            
            # Analyze GCD distribution
            gcd_distribution = {}
            for result in results:
                expected_gcd = result['expected']
                if expected_gcd not in gcd_distribution:
                    gcd_distribution[expected_gcd] = {'total': 0, 'correct': 0}
                gcd_distribution[expected_gcd]['total'] += 1
                if result['correct']:
                    gcd_distribution[expected_gcd]['correct'] += 1
            
            phase_results[phase_name] = {
                'accuracy': accuracy,
                'convergence': convergence,
                'target_accuracy': target_accuracy,
                'meets_target': accuracy >= target_accuracy,
                'gcd_distribution': gcd_distribution,
                'case_count': len(test_cases)
            }
            
            status = "✅" if accuracy >= target_accuracy else "❌"
            print(f"  Accuracy: {accuracy:.3f} (target: {target_accuracy:.3f}) {status}")
            print(f"  Convergence: {convergence:.3f}")
            print(f"  Cases: {len(test_cases)}")
            
            # Show GCD distribution
            gcd_values = sorted(gcd_distribution.keys())
            gcd_accs = [gcd_distribution[g]['correct']/gcd_distribution[g]['total'] for g in gcd_values]
            print(f"  GCD accuracies: {dict(zip(gcd_values, [f'{acc:.2f}' for acc in gcd_accs]))}")
        
        return phase_results
    
    def _is_prime(self, n: int) -> bool:
        """Check if number is prime."""
        if n < 2:
            return False
        for i in range(2, int(n**0.5) + 1):
            if n % i == 0:
                return False
        return True
    
    def _is_power_of_two(self, n: int) -> bool:
        """Check if number is power of 2."""
        return n > 0 and (n & (n - 1)) == 0
    
    def run_comprehensive_validation(self) -> Dict[str, Any]:
        """Run all validation tests."""
        print("🧪 COMPREHENSIVE GCD MODEL VALIDATION")
        print("=" * 80)
        
        start_time = time.time()
        
        # Test curriculum phases
        curriculum_results = self.test_curriculum_phases()
        
        # Test commutativity on a subset of cases
        comm_test_cases = [(2, 3), (6, 9), (8, 12), (15, 25), (7, 11), (12, 18)]
        commutativity_results = self.test_commutativity(comm_test_cases)
        
        # Test edge cases
        edge_case_results = self.test_edge_cases()
        
        # Test generalization
        generalization_results = self.test_generalization()
        
        total_time = time.time() - start_time
        
        # Compile final report
        final_results = {
            'curriculum_phases': curriculum_results,
            'commutativity': commutativity_results,
            'edge_cases': edge_case_results,
            'generalization': generalization_results,
            'validation_time': total_time
        }
        
        self._print_final_report(final_results)
        
        return final_results
    
    def _print_final_report(self, results: Dict[str, Any]):
        """Print comprehensive final report."""
        print(f"\n🏆 FINAL VALIDATION REPORT")
        print("=" * 80)
        
        # Overall summary
        curriculum_passed = sum(1 for r in results['curriculum_phases'].values() if r['meets_target'])
        total_phases = len(results['curriculum_phases'])
        
        print(f"📊 SUMMARY:")
        print(f"  Curriculum phases passed: {curriculum_passed}/{total_phases}")
        print(f"  Commutativity rate: {results['commutativity']['commutativity_rate']:.3f}")
        print(f"  Edge case accuracy: {results['edge_cases']['overall_accuracy']:.3f}")
        print(f"  Generalization accuracy: {results['generalization']['overall_accuracy']:.3f}")
        print(f"  Validation time: {results['validation_time']:.1f} seconds")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if curriculum_passed == total_phases:
            print("  ✅ Model successfully completed all curriculum phases")
        else:
            print(f"  ⚠️ Model failed {total_phases - curriculum_passed} curriculum phases")
        
        if results['commutativity']['commutativity_rate'] >= 0.95:
            print("  ✅ Excellent commutativity (GCD(a,b) = GCD(b,a))")
        else:
            print("  ⚠️ Commutativity issues detected")
        
        if results['generalization']['overall_accuracy'] >= 0.7:
            print("  ✅ Good generalization to larger numbers")
        else:
            print("  ⚠️ Limited generalization capability")

def main():
    """Main validation function."""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python comprehensive_gcd_validation.py <model_path>")
        print("Example: python comprehensive_gcd_validation.py enhanced_gcd_model_final.pkl")
        return
    
    model_path = sys.argv[1]
    
    try:
        validator = ComprehensiveGCDValidator(model_path)
        results = validator.run_comprehensive_validation()
        
        # Save results
        with open('validation_results.pkl', 'wb') as f:
            pickle.dump(results, f)
        
        print(f"\n💾 Validation results saved: validation_results.pkl")
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")

if __name__ == "__main__":
    main()
