"""
Propagation Test Cases for Rule System Comparison

This module defines specific test scenarios that require long-range information 
propagation to solve successfully. These tests are designed to highlight the 
advantages of enhanced non-local rules over traditional local rules.

Test Categories:
1. Distance-dependent computation (information must travel far)
2. Obstacle navigation (information must bypass barriers)
3. Multi-source coordination (multiple information sources must interact)
4. Pattern completion (distributed pattern must be assembled)
5. Computational tasks requiring global information
"""

import numpy as np
from typing import List, Tuple, Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class PropagationTestCase:
    """Definition of a propagation test case."""
    name: str
    description: str
    initial_state: np.ndarray
    expected_behavior: str
    success_criteria: Dict[str, Any]
    difficulty_level: int  # 1-5, higher = more challenging

class PropagationTestSuite:
    """Suite of test cases for evaluating information propagation efficiency."""
    
    def __init__(self):
        self.test_cases = []
        self._create_test_cases()
    
    def _create_test_cases(self):
        """Create all propagation test cases."""
        
        # Test Case 1: Simple Long-Distance Propagation
        self.test_cases.append(self._create_long_distance_test())
        
        # Test Case 2: Single Barrier Navigation
        self.test_cases.append(self._create_single_barrier_test())
        
        # Test Case 3: Multiple Barrier Maze
        self.test_cases.append(self._create_barrier_maze_test())
        
        # Test Case 4: Dense Obstacle Field
        self.test_cases.append(self._create_dense_obstacle_test())
        
        # Test Case 5: Two-Source Coordination
        self.test_cases.append(self._create_two_source_test())
        
        # Test Case 6: Information Relay Chain
        self.test_cases.append(self._create_relay_chain_test())
        
        # Test Case 7: Pattern Completion Task
        self.test_cases.append(self._create_pattern_completion_test())
        
        # Test Case 8: Computational Dependency Chain
        self.test_cases.append(self._create_dependency_chain_test())
    
    def _create_long_distance_test(self) -> PropagationTestCase:
        """Test requiring information to travel maximum distance."""
        N = 100
        state = np.zeros(N, dtype=np.uint8)
        
        # Information source at one end
        state[5] = 1
        
        # Target region at far end
        state[90:95] = 2  # Markers indicating target region
        
        return PropagationTestCase(
            name="Long Distance Propagation",
            description="Information must travel from position 5 to position 90+ (85 cells)",
            initial_state=state,
            expected_behavior="Information should reach the target region within reasonable time",
            success_criteria={
                'min_distance': 85,
                'max_time': 50,
                'target_region': (90, 95)
            },
            difficulty_level=2
        )
    
    def _create_single_barrier_test(self) -> PropagationTestCase:
        """Test with single blocking barrier."""
        N = 80
        state = np.zeros(N, dtype=np.uint8)
        
        # Information source
        state[20] = 1
        
        # Single barrier
        state[40] = 3
        
        # Target region beyond barrier
        state[60:65] = 2
        
        return PropagationTestCase(
            name="Single Barrier Navigation",
            description="Information must bypass a single barrier to reach target",
            initial_state=state,
            expected_behavior="Information should find way around barrier",
            success_criteria={
                'bypass_barrier': True,
                'barrier_position': 40,
                'target_region': (60, 65),
                'max_time': 40
            },
            difficulty_level=3
        )
    
    def _create_barrier_maze_test(self) -> PropagationTestCase:
        """Test with multiple barriers creating a maze-like structure."""
        N = 120
        state = np.zeros(N, dtype=np.uint8)
        
        # Information source
        state[10] = 1
        
        # Multiple barriers creating maze
        barriers = [25, 30, 45, 50, 65, 70, 85, 90]
        for pos in barriers:
            state[pos] = 3
        
        # Target at end
        state[110:115] = 2
        
        return PropagationTestCase(
            name="Barrier Maze Navigation",
            description="Information must navigate through multiple barriers",
            initial_state=state,
            expected_behavior="Information should find path through maze",
            success_criteria={
                'navigate_barriers': True,
                'barrier_count': len(barriers),
                'target_region': (110, 115),
                'max_time': 80
            },
            difficulty_level=4
        )
    
    def _create_dense_obstacle_test(self) -> PropagationTestCase:
        """Test with high density of obstacles."""
        N = 100
        state = np.zeros(N, dtype=np.uint8)
        
        # Information source
        state[15] = 1
        
        # Dense obstacle field (every 3rd cell blocked in middle region)
        for pos in range(30, 70, 3):
            state[pos] = 3
        
        # Target beyond obstacles
        state[85:90] = 2
        
        return PropagationTestCase(
            name="Dense Obstacle Field",
            description="Information must pass through high-density obstacle field",
            initial_state=state,
            expected_behavior="Information should penetrate dense obstacles",
            success_criteria={
                'penetrate_density': True,
                'obstacle_density': 0.33,
                'target_region': (85, 90),
                'max_time': 60
            },
            difficulty_level=4
        )
    
    def _create_two_source_test(self) -> PropagationTestCase:
        """Test requiring coordination between two information sources."""
        N = 100
        state = np.zeros(N, dtype=np.uint8)
        
        # Two information sources
        state[20] = 1
        state[80] = 1
        
        # Barriers that require coordination to bypass
        state[35:45] = 3  # Left barrier
        state[55:65] = 3  # Right barrier
        
        # Target in middle that requires both sources
        state[48:52] = 2
        
        return PropagationTestCase(
            name="Two-Source Coordination",
            description="Two information sources must coordinate to reach central target",
            initial_state=state,
            expected_behavior="Both sources should contribute to reaching target",
            success_criteria={
                'coordination_required': True,
                'source_positions': [20, 80],
                'target_region': (48, 52),
                'max_time': 50
            },
            difficulty_level=5
        )
    
    def _create_relay_chain_test(self) -> PropagationTestCase:
        """Test requiring information relay through intermediate stations."""
        N = 120
        state = np.zeros(N, dtype=np.uint8)
        
        # Information source
        state[10] = 1
        
        # Relay stations (special cells that can amplify/forward information)
        relay_positions = [30, 50, 70, 90]
        for pos in relay_positions:
            state[pos] = 2
        
        # Barriers between relays
        state[20] = 3
        state[40] = 3
        state[60] = 3
        state[80] = 3
        
        # Final target
        state[110:115] = 2
        
        return PropagationTestCase(
            name="Information Relay Chain",
            description="Information must be relayed through intermediate stations",
            initial_state=state,
            expected_behavior="Information should hop between relay stations",
            success_criteria={
                'use_relays': True,
                'relay_positions': relay_positions,
                'target_region': (110, 115),
                'max_time': 70
            },
            difficulty_level=5
        )
    
    def _create_pattern_completion_test(self) -> PropagationTestCase:
        """Test requiring completion of a distributed pattern."""
        N = 80
        state = np.zeros(N, dtype=np.uint8)
        
        # Partial pattern that needs completion
        pattern_positions = [20, 25, 35, 40, 55, 60]
        for i, pos in enumerate(pattern_positions):
            state[pos] = 1 if i % 2 == 0 else 2
        
        # Missing elements that should be filled
        missing_positions = [30, 45, 50]
        
        return PropagationTestCase(
            name="Pattern Completion",
            description="Distributed pattern elements must coordinate to complete pattern",
            initial_state=state,
            expected_behavior="Missing pattern elements should be filled",
            success_criteria={
                'complete_pattern': True,
                'pattern_positions': pattern_positions,
                'missing_positions': missing_positions,
                'max_time': 40
            },
            difficulty_level=4
        )
    
    def _create_dependency_chain_test(self) -> PropagationTestCase:
        """Test with computational dependencies requiring sequential processing."""
        N = 100
        state = np.zeros(N, dtype=np.uint8)
        
        # Initial computation trigger
        state[10] = 1
        
        # Dependency chain: each stage depends on previous
        stage_positions = [25, 40, 55, 70, 85]
        for i, pos in enumerate(stage_positions):
            state[pos] = 2  # Markers for computation stages
        
        return PropagationTestCase(
            name="Computational Dependency Chain",
            description="Sequential computation stages with dependencies",
            initial_state=state,
            expected_behavior="Computation should proceed through all stages in order",
            success_criteria={
                'sequential_processing': True,
                'stage_positions': stage_positions,
                'max_time': 60
            },
            difficulty_level=5
        )
    
    def get_test_case(self, name: str) -> Optional[PropagationTestCase]:
        """Get a specific test case by name."""
        for test_case in self.test_cases:
            if test_case.name == name:
                return test_case
        return None
    
    def get_test_cases_by_difficulty(self, difficulty: int) -> List[PropagationTestCase]:
        """Get all test cases of a specific difficulty level."""
        return [tc for tc in self.test_cases if tc.difficulty_level == difficulty]
    
    def get_all_test_cases(self) -> List[PropagationTestCase]:
        """Get all test cases."""
        return self.test_cases.copy()
    
    def evaluate_test_case_success(self, test_case: PropagationTestCase, 
                                 final_states: List[np.ndarray]) -> Dict[str, Any]:
        """
        Evaluate whether a test case was successfully completed.
        
        Args:
            test_case: The test case to evaluate
            final_states: List of states from the simulation
            
        Returns:
            Dictionary with success metrics
        """
        if not final_states:
            return {'success': False, 'reason': 'No states provided'}
        
        final_state = final_states[-1]
        criteria = test_case.success_criteria
        results = {'success': False, 'metrics': {}}
        
        # Check distance-based criteria
        if 'min_distance' in criteria:
            nonzero_positions = np.where(final_state != 0)[0]
            if len(nonzero_positions) >= 2:
                max_distance = np.max(nonzero_positions) - np.min(nonzero_positions)
                results['metrics']['achieved_distance'] = max_distance
                results['success'] = max_distance >= criteria['min_distance']
        
        # Check target region criteria
        if 'target_region' in criteria:
            start, end = criteria['target_region']
            target_reached = np.any(final_state[start:end] != 0)
            results['metrics']['target_reached'] = target_reached
            results['success'] = target_reached
        
        # Check time criteria
        if 'max_time' in criteria:
            time_taken = len(final_states) - 1
            results['metrics']['time_taken'] = time_taken
            if 'success' not in results or results['success']:
                results['success'] = time_taken <= criteria['max_time']
        
        return results

def test_propagation_test_suite():
    """Test the propagation test suite."""
    print("🧪 TESTING PROPAGATION TEST SUITE")
    print("=" * 50)
    
    suite = PropagationTestSuite()
    test_cases = suite.get_all_test_cases()
    
    print(f"Created {len(test_cases)} test cases:")
    for i, tc in enumerate(test_cases, 1):
        print(f"  {i}. {tc.name} (Difficulty: {tc.difficulty_level})")
        print(f"     {tc.description}")
        print(f"     Initial state size: {len(tc.initial_state)}")
        print(f"     Non-zero cells: {np.sum(tc.initial_state != 0)}")
    
    # Test difficulty filtering
    print(f"\nDifficulty distribution:")
    for diff in range(1, 6):
        cases = suite.get_test_cases_by_difficulty(diff)
        print(f"  Level {diff}: {len(cases)} cases")
    
    print("\n✅ Propagation test suite test completed")

if __name__ == "__main__":
    test_propagation_test_suite()
