"""
LCM-Specific Encoding and Decoding Strategies

This module implements specialized encoding and decoding strategies for LCM computation.

Key LCM Properties to Consider:
- LCM(a,b) ≥ max(a,b) (always larger than inputs)
- LCM(a,1) = a
- LCM(a,a) = a  
- LCM(a,b) = a×b / GCD(a,b)
- For coprimes: LCM(a,b) = a×b

Encoding Strategies:
1. Standard separation with clear input boundaries
2. Product hint encoding (includes a×b for coprime cases)
3. Multiple-based encoding (emphasizes multiples)
4. Symmetric encoding for commutativity

Decoding Strategies:
1. Count-based methods (adapted for larger outputs)
2. Multiple detection methods
3. Product-based decoding
4. Pattern-based decoding for LCM-specific patterns
"""

import numpy as np
from typing import Tuple, List, Dict

def lcm(a: int, b: int) -> int:
    """Compute LCM using the relationship LCM(a,b) = a*b / GCD(a,b)."""
    def gcd(x, y):
        while y:
            x, y = y, x % y
        return x
    return (a * b) // gcd(a, b)

class LCMEncodingSchemes:
    """Collection of LCM-specific encoding strategies."""
    
    @staticmethod
    def encode_standard_lcm(a: int, b: int, prog_len: int, N: int) -> np.ndarray:
        """
        Standard LCM encoding: PROG BB 1^a R 1^b R
        Use 1s for inputs since LCM outputs are typically larger.
        """
        state = np.zeros(N, dtype=np.uint8)
        
        # Program
        for i in range(min(prog_len, N)):
            state[i] = 1
        
        # Separator BB
        if prog_len + 1 < N:
            state[prog_len] = 3
            state[prog_len + 1] = 3
        
        base_pos = prog_len + 2
        
        # First input: 1^a
        for i in range(a):
            if base_pos + i < N:
                state[base_pos + i] = 1
        
        # First marker R
        r1_pos = base_pos + a
        if r1_pos < N:
            state[r1_pos] = 2
        
        # Second input: 1^b
        for i in range(b):
            pos = r1_pos + 1 + i
            if pos < N:
                state[pos] = 1
        
        # Second marker R
        r2_pos = r1_pos + 1 + b
        if r2_pos < N:
            state[r2_pos] = 2
        
        return state
    
    @staticmethod
    def encode_product_hint(a: int, b: int, prog_len: int, N: int) -> np.ndarray:
        """
        Product hint encoding: PROG BB 1^a R 1^b R 0^(a*b) R
        Includes the product a×b as a hint for coprime cases.
        """
        state = np.zeros(N, dtype=np.uint8)
        
        # Program
        for i in range(min(prog_len, N)):
            state[i] = 1
        
        # Separator BB
        if prog_len + 1 < N:
            state[prog_len] = 3
            state[prog_len + 1] = 3
        
        base_pos = prog_len + 2
        
        # First input: 1^a
        for i in range(a):
            if base_pos + i < N:
                state[base_pos + i] = 1
        
        r1_pos = base_pos + a
        if r1_pos < N:
            state[r1_pos] = 2
        
        # Second input: 1^b
        for i in range(b):
            pos = r1_pos + 1 + i
            if pos < N:
                state[pos] = 1
        
        r2_pos = r1_pos + 1 + b
        if r2_pos < N:
            state[r2_pos] = 2
        
        # Product hint: 0^(a*b) - limited to available space
        product = a * b
        available_space = N - r2_pos - 2
        product_length = min(product, available_space)
        
        for i in range(product_length):
            pos = r2_pos + 1 + i
            if pos < N:
                state[pos] = 0
        
        # Final marker
        final_r = r2_pos + 1 + product_length
        if final_r < N:
            state[final_r] = 2
        
        return state
    
    @staticmethod
    def encode_multiple_emphasis(a: int, b: int, prog_len: int, N: int) -> np.ndarray:
        """
        Multiple emphasis: PROG BB 1^a R 1^b R 3^max(a,b) R
        Emphasizes the larger input using blue cells.
        """
        state = np.zeros(N, dtype=np.uint8)
        
        # Program
        for i in range(min(prog_len, N)):
            state[i] = 1
        
        # Separator BB
        if prog_len + 1 < N:
            state[prog_len] = 3
            state[prog_len + 1] = 3
        
        base_pos = prog_len + 2
        
        # First input: 1^a
        for i in range(a):
            if base_pos + i < N:
                state[base_pos + i] = 1
        
        r1_pos = base_pos + a
        if r1_pos < N:
            state[r1_pos] = 2
        
        # Second input: 1^b
        for i in range(b):
            pos = r1_pos + 1 + i
            if pos < N:
                state[pos] = 1
        
        r2_pos = r1_pos + 1 + b
        if r2_pos < N:
            state[r2_pos] = 2
        
        # Multiple emphasis: 3^max(a,b)
        max_input = max(a, b)
        for i in range(max_input):
            pos = r2_pos + 1 + i
            if pos < N:
                state[pos] = 3
        
        # Final marker
        final_r = r2_pos + 1 + max_input
        if final_r < N:
            state[final_r] = 2
        
        return state
    
    @staticmethod
    def encode_lcm_symmetric(a: int, b: int, prog_len: int, N: int) -> np.ndarray:
        """
        Symmetric LCM encoding: PROG BB 1^a R 1^b R 1^b R 1^a R
        Symmetric pattern to emphasize commutativity.
        """
        state = np.zeros(N, dtype=np.uint8)
        
        # Program
        for i in range(min(prog_len, N)):
            state[i] = 1
        
        # Separator BB
        if prog_len + 1 < N:
            state[prog_len] = 3
            state[prog_len + 1] = 3
        
        base_pos = prog_len + 2
        
        # First: 1^a
        for i in range(a):
            if base_pos + i < N:
                state[base_pos + i] = 1
        
        r1_pos = base_pos + a
        if r1_pos < N:
            state[r1_pos] = 2
        
        # Second: 1^b
        for i in range(b):
            pos = r1_pos + 1 + i
            if pos < N:
                state[pos] = 1
        
        r2_pos = r1_pos + 1 + b
        if r2_pos < N:
            state[r2_pos] = 2
        
        # Third: 1^b (symmetric)
        for i in range(b):
            pos = r2_pos + 1 + i
            if pos < N:
                state[pos] = 1
        
        r3_pos = r2_pos + 1 + b
        if r3_pos < N:
            state[r3_pos] = 2
        
        # Fourth: 1^a (symmetric)
        for i in range(a):
            pos = r3_pos + 1 + i
            if pos < N:
                state[pos] = 1
        
        r4_pos = r3_pos + 1 + a
        if r4_pos < N:
            state[r4_pos] = 2
        
        return state

class LCMDecodingStrategies:
    """Collection of LCM-specific decoding strategies."""
    
    @staticmethod
    def decode_ones_after_last_marker(state: np.ndarray) -> int:
        """
        Strategy 0: Count ones after the rightmost R marker.
        Pattern: R 1^lcm
        """
        N = len(state)
        
        # Find rightmost R marker
        rightmost_r = -1
        for i in range(N-1, -1, -1):
            if state[i] == 2:
                rightmost_r = i
                break
        
        if rightmost_r == -1:
            return -10  # No marker found
        
        # Count consecutive ones after marker
        one_count = 0
        for i in range(rightmost_r + 1, N):
            if state[i] == 1:
                one_count += 1
            else:
                break
        
        return max(1, one_count)
    
    @staticmethod
    def decode_zeros_after_last_marker(state: np.ndarray) -> int:
        """
        Strategy 1: Count zeros after the rightmost R marker.
        Pattern: R 0^lcm
        """
        N = len(state)
        
        # Find rightmost R marker
        rightmost_r = -1
        for i in range(N-1, -1, -1):
            if state[i] == 2:
                rightmost_r = i
                break
        
        if rightmost_r == -1:
            return -10
        
        # Count consecutive zeros after marker
        zero_count = 0
        for i in range(rightmost_r + 1, N):
            if state[i] == 0:
                zero_count += 1
            else:
                break
        
        return max(1, zero_count)
    
    @staticmethod
    def decode_blue_cells_total(state: np.ndarray) -> int:
        """
        Strategy 2: Count total blue cells as LCM.
        Pattern: 3^lcm somewhere in the tape
        """
        blue_count = np.sum(state == 3)
        return max(1, blue_count)
    
    @staticmethod
    def decode_multiple_detection(state: np.ndarray) -> int:
        """
        Strategy 3: Detect multiple patterns in the final state.
        Look for repeating patterns that indicate LCM.
        """
        N = len(state)
        
        # Find the main computation region (after program)
        start_region = 0
        for i in range(N):
            if state[i] == 3:  # Found separator
                start_region = i + 1
                break
        
        if start_region >= N - 10:
            return -10
        
        # Look for the longest sequence of same non-zero values
        region = state[start_region:start_region + 30]
        
        max_sequence = 0
        current_sequence = 0
        current_value = 0
        
        for cell in region:
            if cell != 0 and cell == current_value:
                current_sequence += 1
            elif cell != 0:
                max_sequence = max(max_sequence, current_sequence)
                current_value = cell
                current_sequence = 1
            else:
                max_sequence = max(max_sequence, current_sequence)
                current_sequence = 0
                current_value = 0
        
        max_sequence = max(max_sequence, current_sequence)
        return max(1, max_sequence)
    
    @staticmethod
    def decode_product_based(state: np.ndarray) -> int:
        """
        Strategy 4: Product-based decoding for coprime cases.
        Look for patterns indicating a×b computation.
        """
        N = len(state)
        
        # Count different cell types in the final third of the tape
        final_third_start = 2 * N // 3
        final_region = state[final_third_start:]
        
        ones_count = np.sum(final_region == 1)
        zeros_count = np.sum(final_region == 0)
        blues_count = np.sum(final_region == 3)
        
        # Use the most prominent pattern
        if ones_count > zeros_count and ones_count > blues_count:
            return max(1, ones_count)
        elif zeros_count > blues_count:
            return max(1, zeros_count)
        else:
            return max(1, blues_count)
    
    @staticmethod
    def decode_lcm_consensus(state: np.ndarray) -> int:
        """
        Strategy 5: Consensus of multiple LCM decoding methods.
        """
        strategies = [
            LCMDecodingStrategies.decode_ones_after_last_marker,
            LCMDecodingStrategies.decode_zeros_after_last_marker,
            LCMDecodingStrategies.decode_blue_cells_total,
            LCMDecodingStrategies.decode_multiple_detection,
            LCMDecodingStrategies.decode_product_based,
        ]
        
        results = []
        for strategy in strategies:
            try:
                result = strategy(state)
                if result > 0:  # Valid result
                    results.append(result)
            except:
                continue
        
        if not results:
            return 1
        
        # Use median as consensus
        results.sort()
        n = len(results)
        if n % 2 == 1:
            return results[n // 2]
        else:
            return (results[n // 2 - 1] + results[n // 2]) // 2
    
    @staticmethod
    def decode_adaptive_lcm(state: np.ndarray, encoding_type: int) -> int:
        """
        Strategy 6: Adaptive decoding based on encoding type.
        """
        if encoding_type == 0:  # Standard LCM
            return LCMDecodingStrategies.decode_ones_after_last_marker(state)
        elif encoding_type == 1:  # Product hint
            return LCMDecodingStrategies.decode_product_based(state)
        elif encoding_type == 2:  # Multiple emphasis
            return LCMDecodingStrategies.decode_blue_cells_total(state)
        elif encoding_type == 3:  # Symmetric
            return LCMDecodingStrategies.decode_lcm_consensus(state)
        else:
            return LCMDecodingStrategies.decode_lcm_consensus(state)

def test_lcm_encoding_decoding():
    """Test LCM encoding and decoding strategies."""
    print("🧪 TESTING LCM ENCODING AND DECODING")
    print("=" * 50)
    
    test_cases = [(2, 3), (4, 6), (5, 5), (3, 9)]
    prog_len = 5
    N = 80
    
    encoding_schemes = [
        ("Standard LCM", LCMEncodingSchemes.encode_standard_lcm),
        ("Product Hint", LCMEncodingSchemes.encode_product_hint),
        ("Multiple Emphasis", LCMEncodingSchemes.encode_multiple_emphasis),
        ("Symmetric", LCMEncodingSchemes.encode_lcm_symmetric),
    ]
    
    decoding_strategies = [
        ("Ones After R", LCMDecodingStrategies.decode_ones_after_last_marker),
        ("Zeros After R", LCMDecodingStrategies.decode_zeros_after_last_marker),
        ("Blue Cells", LCMDecodingStrategies.decode_blue_cells_total),
        ("Multiple Detection", LCMDecodingStrategies.decode_multiple_detection),
        ("Consensus", LCMDecodingStrategies.decode_lcm_consensus),
    ]
    
    for a, b in test_cases:
        expected_lcm = lcm(a, b)
        print(f"\nTesting LCM({a}, {b}) = {expected_lcm}:")
        
        for enc_name, enc_func in encoding_schemes:
            state = enc_func(a, b, prog_len, N)
            nonzero = [(i, state[i]) for i in range(N) if state[i] != 0]
            print(f"  {enc_name:18}: {nonzero[:12]}...")
        
        # Test one encoding with all decoding strategies
        test_state = encoding_schemes[0][1](a, b, prog_len, N)
        print(f"  Decoding results:")
        for dec_name, dec_func in decoding_strategies:
            try:
                result = dec_func(test_state)
                print(f"    {dec_name:18}: {result}")
            except Exception as e:
                print(f"    {dec_name:18}: ERROR - {e}")
    
    print("\n✅ LCM encoding and decoding test completed")

if __name__ == "__main__":
    test_lcm_encoding_decoding()
