"""
LCM Curriculum Learning Framework

This module implements a progressive curriculum for training EM43 models to compute 
the Least Common Multiple (LCM) of two natural numbers.

Key LCM Properties:
- LCM(a,b) ≥ max(a,b) (always larger than inputs)
- LCM(a,b) = a×b / GCD(a,b)
- LCM(a,a) = a
- LCM(a,1) = a
- For coprime numbers: LCM(a,b) = a×b

Curriculum Design:
1. Simple cases (LCM with 1, identical numbers)
2. Small multiples (one divides the other)
3. Coprime pairs (LCM = product)
4. Small composite cases
5. Mixed complexity cases
"""

import numpy as np
from typing import List, Tuple, Dict, Any
import math

def gcd(a: int, b: int) -> int:
    """Compute GCD using Euclidean algorithm."""
    while b:
        a, b = b, a % b
    return a

def lcm(a: int, b: int) -> int:
    """Compute LCM using the relationship LCM(a,b) = a*b / GCD(a,b)."""
    return (a * b) // gcd(a, b)

class LCMCurriculumFramework:
    """Progressive curriculum framework for LCM computation."""
    
    def __init__(self):
        self.phases = self._create_lcm_curriculum_phases()
    
    def _create_lcm_curriculum_phases(self) -> Dict[str, Dict[str, Any]]:
        """Create 6-phase LCM curriculum with manageable complexity."""
        
        phases = {
            'phase1_trivial_cases': {
                'name': 'Trivial LCM Cases',
                'description': 'Learn LCM with 1 and identical numbers: LCM(a,1)=a, LCM(a,a)=a',
                'cases': self._generate_trivial_cases(),
                'target_accuracy': 0.95,
                'window': 100,
                'max_steps': 200,
                'generations': 100,
                'population_size': 200,
                'mutation_rate': 0.04,
                'elite_fraction': 0.15,
                'fitness_weights': {'accuracy': 100, 'convergence': 20, 'correctness_bonus': 50}
            },
            
            'phase2_simple_multiples': {
                'name': 'Simple Multiples',
                'description': 'Cases where one number divides the other: LCM(a,ka)=ka',
                'cases': self._generate_simple_multiples(),
                'target_accuracy': 0.90,
                'window': 120,
                'max_steps': 250,
                'generations': 120,
                'population_size': 250,
                'mutation_rate': 0.035,
                'elite_fraction': 0.18,
                'fitness_weights': {'accuracy': 100, 'convergence': 15, 'pattern_recognition': 40}
            },
            
            'phase3_small_coprimes': {
                'name': 'Small Coprime Pairs',
                'description': 'Coprime numbers where LCM(a,b) = a×b',
                'cases': self._generate_small_coprimes(),
                'target_accuracy': 0.85,
                'window': 140,
                'max_steps': 300,
                'generations': 150,
                'population_size': 300,
                'mutation_rate': 0.03,
                'elite_fraction': 0.20,
                'fitness_weights': {'accuracy': 100, 'convergence': 12, 'product_learning': 50}
            },
            
            'phase4_small_composites': {
                'name': 'Small Composite Cases',
                'description': 'Small numbers with common factors: LCM via GCD relationship',
                'cases': self._generate_small_composites(),
                'target_accuracy': 0.80,
                'window': 160,
                'max_steps': 350,
                'generations': 180,
                'population_size': 350,
                'mutation_rate': 0.028,
                'elite_fraction': 0.22,
                'fitness_weights': {'accuracy': 100, 'convergence': 10, 'gcd_relationship': 60}
            },
            
            'phase5_mixed_cases': {
                'name': 'Mixed Complexity Cases',
                'description': 'Combination of all previous patterns with medium-sized numbers',
                'cases': self._generate_mixed_cases(),
                'target_accuracy': 0.75,
                'window': 180,
                'max_steps': 400,
                'generations': 200,
                'population_size': 400,
                'mutation_rate': 0.025,
                'elite_fraction': 0.25,
                'fitness_weights': {'accuracy': 100, 'convergence': 8, 'generalization': 40}
            },
            
            'phase6_comprehensive': {
                'name': 'Comprehensive LCM Test',
                'description': 'Final test across all LCM patterns and difficulty levels',
                'cases': self._generate_comprehensive_cases(),
                'target_accuracy': 0.70,
                'window': 200,
                'max_steps': 500,
                'generations': 250,
                'population_size': 500,
                'mutation_rate': 0.022,
                'elite_fraction': 0.28,
                'fitness_weights': {'accuracy': 100, 'convergence': 6, 'comprehensive_score': 50}
            }
        }
        
        return phases
    
    def _generate_trivial_cases(self) -> List[Tuple[int, int]]:
        """Generate trivial LCM cases: LCM(a,1)=a and LCM(a,a)=a."""
        cases = []
        
        # LCM with 1
        for a in range(1, 11):
            cases.extend([(a, 1), (1, a)])
        
        # LCM of identical numbers
        for a in range(2, 11):
            cases.append((a, a))
        
        return cases
    
    def _generate_simple_multiples(self) -> List[Tuple[int, int]]:
        """Generate cases where one number divides the other."""
        cases = []
        
        # Simple multiples: LCM(a, 2a) = 2a
        multiples = [
            (2, 4), (2, 6), (2, 8), (2, 10),
            (3, 6), (3, 9), (3, 12), (3, 15),
            (4, 8), (4, 12), (4, 16), (4, 20),
            (5, 10), (5, 15), (5, 20), (5, 25)
        ]
        
        for a, b in multiples:
            cases.extend([(a, b), (b, a)])
        
        return cases
    
    def _generate_small_coprimes(self) -> List[Tuple[int, int]]:
        """Generate small coprime pairs where LCM = product."""
        cases = []
        
        # Small coprime pairs
        coprimes = [
            (2, 3), (2, 5), (2, 7), (3, 4), (3, 5), (3, 7), (3, 8),
            (4, 5), (4, 7), (4, 9), (5, 6), (5, 7), (5, 8), (5, 9),
            (6, 7), (7, 8), (7, 9), (8, 9)
        ]
        
        # Verify they're actually coprime and add both orders
        for a, b in coprimes:
            if gcd(a, b) == 1:
                cases.extend([(a, b), (b, a)])
        
        return cases
    
    def _generate_small_composites(self) -> List[Tuple[int, int]]:
        """Generate small composite cases with common factors."""
        cases = []
        
        # Cases with GCD > 1
        composites = [
            (4, 6), (6, 8), (6, 9), (8, 12), (9, 12), (10, 15),
            (6, 10), (8, 10), (9, 15), (12, 15), (12, 18), (15, 20)
        ]
        
        for a, b in composites:
            if gcd(a, b) > 1:  # Ensure they have common factors
                cases.extend([(a, b), (b, a)])
        
        return cases
    
    def _generate_mixed_cases(self) -> List[Tuple[int, int]]:
        """Generate mixed cases combining all patterns."""
        cases = []
        
        # Sample from all previous categories
        cases.extend([(2, 2), (5, 5), (7, 7)])  # Identical
        cases.extend([(1, 12), (1, 15), (1, 18)])  # With 1
        cases.extend([(3, 12), (4, 16), (5, 20)])  # Multiples
        cases.extend([(7, 11), (8, 15), (9, 14)])  # Coprimes
        cases.extend([(12, 16), (14, 21), (15, 25)])  # Composites
        
        # Add some medium-sized cases
        medium_cases = [
            (6, 14), (8, 18), (10, 16), (12, 20), (14, 22),
            (9, 21), (10, 25), (12, 28), (15, 35), (18, 24)
        ]
        
        for a, b in medium_cases:
            cases.extend([(a, b), (b, a)])
        
        return list(set(cases))
    
    def _generate_comprehensive_cases(self) -> List[Tuple[int, int]]:
        """Generate comprehensive test cases covering all patterns."""
        cases = []
        
        # Sample from all phases
        cases.extend(self._generate_trivial_cases()[:8])
        cases.extend(self._generate_simple_multiples()[:12])
        cases.extend(self._generate_small_coprimes()[:12])
        cases.extend(self._generate_small_composites()[:12])
        cases.extend(self._generate_mixed_cases()[:16])
        
        # Add some challenging cases
        challenging = [
            (16, 24), (18, 30), (20, 35), (21, 28), (24, 36),
            (15, 40), (18, 45), (20, 50), (25, 40), (30, 42)
        ]
        
        for a, b in challenging:
            cases.extend([(a, b), (b, a)])
        
        return list(set(cases))
    
    def get_phase(self, phase_name: str) -> Dict[str, Any]:
        """Get configuration for a specific phase."""
        if phase_name not in self.phases:
            raise ValueError(f"Unknown phase: {phase_name}")
        return self.phases[phase_name]
    
    def get_all_phases(self) -> List[str]:
        """Get list of all phase names in order."""
        return [
            'phase1_trivial_cases',
            'phase2_simple_multiples',
            'phase3_small_coprimes',
            'phase4_small_composites',
            'phase5_mixed_cases',
            'phase6_comprehensive'
        ]
    
    def validate_phase_progression(self, results: Dict[str, float]) -> Dict[str, bool]:
        """Validate if each phase meets its target accuracy."""
        validation = {}
        for phase_name in self.get_all_phases():
            phase_config = self.phases[phase_name]
            target = phase_config['target_accuracy']
            actual = results.get(phase_name, 0.0)
            validation[phase_name] = actual >= target
        return validation
    
    def get_comprehensive_test_set(self) -> List[Tuple[int, int]]:
        """Get comprehensive test set combining all phases."""
        all_cases = []
        for phase_name in self.get_all_phases():
            phase_cases = self.phases[phase_name]['cases']
            all_cases.extend(phase_cases)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_cases = []
        for case in all_cases:
            if case not in seen:
                seen.add(case)
                unique_cases.append(case)
        
        return unique_cases

def test_lcm_curriculum():
    """Test the LCM curriculum framework."""
    print("🧪 TESTING LCM CURRICULUM FRAMEWORK")
    print("=" * 50)
    
    framework = LCMCurriculumFramework()
    
    for phase_name in framework.get_all_phases():
        phase = framework.get_phase(phase_name)
        cases = phase['cases']
        
        print(f"\n{phase['name']}:")
        print(f"  Cases: {len(cases)}")
        print(f"  Target accuracy: {phase['target_accuracy']}")
        print(f"  Sample cases: {cases[:8]}")
        
        # Verify LCM values
        lcm_values = [lcm(a, b) for a, b in cases[:10]]
        print(f"  Sample LCMs: {lcm_values}")
        
        # Check LCM properties
        max_inputs = [max(a, b) for a, b in cases[:10]]
        lcm_ge_max = all(l >= m for l, m in zip(lcm_values, max_inputs))
        print(f"  LCM ≥ max(inputs): {lcm_ge_max}")
    
    total_cases = len(framework.get_comprehensive_test_set())
    print(f"\nTotal comprehensive test cases: {total_cases}")
    print("✅ LCM curriculum framework test completed")

if __name__ == "__main__":
    test_lcm_curriculum()
