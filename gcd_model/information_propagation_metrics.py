"""
Information Propagation Metrics for EM43 Efficiency Analysis

This module defines quantitative metrics to measure and compare information propagation
efficiency between different rule systems (4-rule vs 8-rule non-local systems).

Key Metrics:
1. Propagation Speed: How fast information travels across the tape
2. Propagation Distance: How far information can travel in given time
3. Information Density: How much information is preserved during propagation
4. Blocking Resistance: How well information passes through obstacles
5. Convergence Efficiency: How quickly the system reaches stable computation
"""

import numpy as np
from typing import List, Tuple, Dict, Any, Optional
import matplotlib.pyplot as plt
from dataclasses import dataclass

@dataclass
class PropagationMetrics:
    """Container for information propagation measurements."""
    speed: float  # cells per time step
    max_distance: int  # maximum distance reached
    information_density: float  # information preserved (0-1)
    blocking_resistance: float  # ability to pass through obstacles (0-1)
    convergence_time: int  # steps to reach stable state
    efficiency_score: float  # composite efficiency metric

class InformationPropagationAnalyzer:
    """Analyzer for measuring information propagation in cellular automata."""
    
    def __init__(self):
        self.history = []  # Store state evolution
        self.metrics = {}
    
    def measure_propagation_speed(self, states: List[np.ndarray], 
                                source_pos: int, target_positions: List[int]) -> float:
        """
        Measure how fast information propagates from source to target positions.
        
        Args:
            states: List of CA states over time
            source_pos: Position where information originates
            target_positions: Positions to measure propagation to
            
        Returns:
            Average propagation speed in cells per time step
        """
        if len(states) < 2:
            return 0.0
        
        # Track when information first appears at each target position
        arrival_times = {}
        source_value = states[0][source_pos]
        
        if source_value == 0:  # No initial information
            return 0.0
        
        for t, state in enumerate(states[1:], 1):  # Skip initial state
            for target_pos in target_positions:
                if target_pos not in arrival_times and target_pos < len(state):
                    # Check if information has arrived (non-zero value)
                    if state[target_pos] != 0:
                        distance = abs(target_pos - source_pos)
                        arrival_times[target_pos] = (distance, t)
        
        if not arrival_times:
            return 0.0
        
        # Calculate average speed
        speeds = [distance / time for distance, time in arrival_times.values()]
        return np.mean(speeds)
    
    def measure_max_propagation_distance(self, states: List[np.ndarray], 
                                       source_pos: int, max_time: int) -> int:
        """
        Measure the maximum distance information can propagate in given time.
        
        Args:
            states: List of CA states over time
            source_pos: Position where information originates
            max_time: Maximum time steps to consider
            
        Returns:
            Maximum distance reached
        """
        if len(states) < 2:
            return 0
        
        max_distance = 0
        source_value = states[0][source_pos]
        
        if source_value == 0:
            return 0
        
        for t in range(1, min(len(states), max_time + 1)):
            state = states[t]
            
            # Find furthest non-zero cells from source
            for pos in range(len(state)):
                if state[pos] != 0:
                    distance = abs(pos - source_pos)
                    max_distance = max(max_distance, distance)
        
        return max_distance
    
    def measure_information_density(self, states: List[np.ndarray]) -> float:
        """
        Measure how much information is preserved during propagation.
        
        Args:
            states: List of CA states over time
            
        Returns:
            Information density (0-1, higher = more information preserved)
        """
        if len(states) < 2:
            return 0.0
        
        initial_info = np.sum(states[0] != 0)
        if initial_info == 0:
            return 0.0
        
        # Measure information content over time
        info_over_time = []
        for state in states:
            nonzero_count = np.sum(state != 0)
            unique_values = len(np.unique(state[state != 0]))
            # Information = number of non-zero cells × diversity of values
            info_content = nonzero_count * unique_values
            info_over_time.append(info_content)
        
        # Information density = average information / initial information
        avg_info = np.mean(info_over_time[1:])  # Skip initial state
        return min(avg_info / (initial_info * 4), 1.0)  # Normalize by max possible (4 cell types)
    
    def measure_blocking_resistance(self, states: List[np.ndarray], 
                                  source_pos: int, barrier_positions: List[int]) -> float:
        """
        Measure how well information passes through blocking obstacles.
        
        Args:
            states: List of CA states over time
            source_pos: Position where information originates
            barrier_positions: Positions of blocking obstacles
            
        Returns:
            Blocking resistance (0-1, higher = better at passing through obstacles)
        """
        if len(states) < 2:
            return 0.0
        
        # Count how many barriers are eventually bypassed
        bypassed_barriers = 0
        
        for barrier_pos in barrier_positions:
            # Check if information appears beyond the barrier
            for t in range(1, len(states)):
                state = states[t]
                
                # Look for information beyond the barrier
                if source_pos < barrier_pos:
                    # Information should propagate rightward past barrier
                    beyond_positions = range(barrier_pos + 1, len(state))
                else:
                    # Information should propagate leftward past barrier
                    beyond_positions = range(0, barrier_pos)
                
                for pos in beyond_positions:
                    if pos < len(state) and state[pos] != 0:
                        bypassed_barriers += 1
                        break
                else:
                    continue
                break
        
        if not barrier_positions:
            return 1.0
        
        return bypassed_barriers / len(barrier_positions)
    
    def measure_convergence_time(self, states: List[np.ndarray], 
                               stability_threshold: int = 5) -> int:
        """
        Measure how quickly the system reaches a stable computational state.
        
        Args:
            states: List of CA states over time
            stability_threshold: Number of consecutive identical states for stability
            
        Returns:
            Time steps to convergence (-1 if never converges)
        """
        if len(states) < stability_threshold + 1:
            return -1
        
        for t in range(len(states) - stability_threshold):
            # Check if next 'stability_threshold' states are identical
            stable = True
            reference_state = states[t]
            
            for i in range(1, stability_threshold + 1):
                if not np.array_equal(reference_state, states[t + i]):
                    stable = False
                    break
            
            if stable:
                return t
        
        return -1  # Never converged
    
    def calculate_efficiency_score(self, metrics: PropagationMetrics) -> float:
        """
        Calculate composite efficiency score from individual metrics.
        
        Args:
            metrics: Individual propagation metrics
            
        Returns:
            Composite efficiency score (0-100, higher = more efficient)
        """
        # Normalize individual metrics to 0-1 scale
        speed_score = min(metrics.speed / 2.0, 1.0)  # Assume max reasonable speed = 2 cells/step
        distance_score = min(metrics.max_distance / 50.0, 1.0)  # Assume max reasonable distance = 50
        density_score = metrics.information_density
        resistance_score = metrics.blocking_resistance
        
        # Convergence score (faster convergence = higher score)
        if metrics.convergence_time > 0:
            convergence_score = max(0, 1.0 - metrics.convergence_time / 100.0)
        else:
            convergence_score = 0.0  # Never converged
        
        # Weighted combination
        weights = {
            'speed': 0.25,
            'distance': 0.20,
            'density': 0.20,
            'resistance': 0.20,
            'convergence': 0.15
        }
        
        efficiency = (
            speed_score * weights['speed'] +
            distance_score * weights['distance'] +
            density_score * weights['density'] +
            resistance_score * weights['resistance'] +
            convergence_score * weights['convergence']
        )
        
        return efficiency * 100  # Scale to 0-100
    
    def analyze_propagation(self, states: List[np.ndarray], 
                          source_pos: int, 
                          target_positions: List[int] = None,
                          barrier_positions: List[int] = None) -> PropagationMetrics:
        """
        Comprehensive analysis of information propagation.
        
        Args:
            states: List of CA states over time
            source_pos: Position where information originates
            target_positions: Positions to measure propagation to
            barrier_positions: Positions of blocking obstacles
            
        Returns:
            Complete propagation metrics
        """
        if target_positions is None:
            # Default targets at various distances
            N = len(states[0]) if states else 100
            target_positions = [
                max(0, source_pos - 10), max(0, source_pos - 20),
                min(N-1, source_pos + 10), min(N-1, source_pos + 20)
            ]
        
        if barrier_positions is None:
            barrier_positions = []
        
        # Calculate individual metrics
        speed = self.measure_propagation_speed(states, source_pos, target_positions)
        max_distance = self.measure_max_propagation_distance(states, source_pos, len(states))
        info_density = self.measure_information_density(states)
        blocking_resistance = self.measure_blocking_resistance(states, source_pos, barrier_positions)
        convergence_time = self.measure_convergence_time(states)
        
        # Create metrics object
        metrics = PropagationMetrics(
            speed=speed,
            max_distance=max_distance,
            information_density=info_density,
            blocking_resistance=blocking_resistance,
            convergence_time=convergence_time,
            efficiency_score=0.0  # Will be calculated next
        )
        
        # Calculate composite efficiency score
        metrics.efficiency_score = self.calculate_efficiency_score(metrics)
        
        return metrics
    
    def create_test_scenario(self, N: int, source_pos: int, 
                           barrier_positions: List[int] = None) -> np.ndarray:
        """
        Create a test scenario for measuring information propagation.
        
        Args:
            N: Length of cellular automaton tape
            source_pos: Position to place information source
            barrier_positions: Positions to place blocking obstacles
            
        Returns:
            Initial state for testing
        """
        state = np.zeros(N, dtype=np.uint8)
        
        # Place information source
        state[source_pos] = 1
        
        # Place barriers (using cell type 3 as obstacles)
        if barrier_positions:
            for pos in barrier_positions:
                if 0 <= pos < N:
                    state[pos] = 3
        
        return state

def test_propagation_metrics():
    """Test the information propagation metrics."""
    print("🧪 TESTING INFORMATION PROPAGATION METRICS")
    print("=" * 60)
    
    analyzer = InformationPropagationAnalyzer()
    
    # Create test scenario
    N = 50
    source_pos = 25
    barrier_positions = [20, 30]
    
    initial_state = analyzer.create_test_scenario(N, source_pos, barrier_positions)
    
    # Simulate simple propagation (mock evolution)
    states = [initial_state]
    current_state = initial_state.copy()
    
    # Simple propagation simulation
    for t in range(20):
        next_state = current_state.copy()
        
        # Simple rule: information spreads to adjacent cells
        for i in range(1, N-1):
            if current_state[i] == 1:  # Information cell
                # Spread to neighbors if they're empty
                if current_state[i-1] == 0:
                    next_state[i-1] = 1
                if current_state[i+1] == 0:
                    next_state[i+1] = 1
        
        states.append(next_state)
        current_state = next_state
    
    # Analyze propagation
    target_positions = [15, 20, 30, 35]
    metrics = analyzer.analyze_propagation(states, source_pos, target_positions, barrier_positions)
    
    print(f"Propagation Analysis Results:")
    print(f"  Speed: {metrics.speed:.3f} cells/step")
    print(f"  Max Distance: {metrics.max_distance} cells")
    print(f"  Information Density: {metrics.information_density:.3f}")
    print(f"  Blocking Resistance: {metrics.blocking_resistance:.3f}")
    print(f"  Convergence Time: {metrics.convergence_time} steps")
    print(f"  Efficiency Score: {metrics.efficiency_score:.1f}/100")
    
    print("\n✅ Information propagation metrics test completed")

if __name__ == "__main__":
    test_propagation_metrics()
