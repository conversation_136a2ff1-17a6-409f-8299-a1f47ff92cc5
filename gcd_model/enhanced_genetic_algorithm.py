"""
Enhanced Genetic Algorithm for GCD Computation

This module implements an improved genetic algorithm specifically designed for training
EM43 models to compute GCD with curriculum learning and advanced evolutionary strategies.

Key features:
1. Curriculum-aware fitness functions
2. Adaptive mutation rates based on progress
3. Specialized crossover operators for rule and parameter evolution
4. Multi-objective optimization (accuracy, convergence, generalization)
5. Diversity preservation mechanisms
"""

import numpy as np
from typing import List, Tuple, Dict, Any, Optional
from tqdm import tqdm
import pickle
import time

class EnhancedGeneticAlgorithm:
    """Enhanced GA for GCD computation with curriculum learning."""
    
    def __init__(self, 
                 population_size: int = 500,
                 elite_fraction: float = 0.2,
                 mutation_rate: float = 0.02,
                 crossover_rate: float = 0.8,
                 diversity_threshold: float = 0.1):
        
        self.population_size = population_size
        self.elite_fraction = elite_fraction
        self.base_mutation_rate = mutation_rate
        self.crossover_rate = crossover_rate
        self.diversity_threshold = diversity_threshold
        
        # Adaptive parameters
        self.current_mutation_rate = mutation_rate
        self.stagnation_counter = 0
        self.best_fitness_history = []
        
        # Statistics tracking
        self.generation_stats = []
        
    def calculate_curriculum_fitness(self, 
                                   individual: Any,
                                   test_cases: List[Tuple[int, int]],
                                   phase_config: Dict[str, Any],
                                   evaluate_func) -> float:
        """
        Calculate fitness with curriculum-aware weighting.
        
        Args:
            individual: EM43 model to evaluate
            test_cases: List of (a, b) test cases
            phase_config: Current curriculum phase configuration
            evaluate_func: Function to evaluate individual on test cases
        """
        try:
            accuracy, convergence, results = evaluate_func(individual, test_cases)
        except Exception as e:
            return 0.0  # Failed evaluation
        
        # Base fitness components
        weights = phase_config.get('fitness_weights', {
            'accuracy': 100, 'convergence': 10, 'correctness_bonus': 20
        })
        
        base_fitness = (accuracy * weights.get('accuracy', 100) + 
                       convergence * weights.get('convergence', 10))
        
        # Curriculum-specific bonuses
        bonus = 0.0
        
        # Correctness bonus for perfect accuracy
        if accuracy >= 0.95:
            bonus += weights.get('correctness_bonus', 20)
        
        # GCD diversity bonus (reward models that handle different GCD values)
        if 'gcd_diversity' in weights:
            gcd_values = set()
            for result in results:
                if result['correct']:
                    expected_gcd = result['expected']
                    gcd_values.add(expected_gcd)
            diversity_bonus = len(gcd_values) * weights['gcd_diversity']
            bonus += diversity_bonus
        
        # Generalization bonus (consistent performance across different input sizes)
        if 'generalization' in weights:
            size_groups = {}
            for result in results:
                a, b = result['a'], result['b']
                size_key = (a + b) // 5  # Group by sum ranges
                if size_key not in size_groups:
                    size_groups[size_key] = []
                size_groups[size_key].append(result['correct'])
            
            if len(size_groups) > 1:
                group_accuracies = [np.mean(group) for group in size_groups.values()]
                consistency = 1.0 - np.std(group_accuracies)  # Lower std = better
                bonus += consistency * weights['generalization']
        
        # Size robustness bonus (handle large differences in input sizes)
        if 'size_robustness' in weights:
            size_ratios = []
            for result in results:
                a, b = result['a'], result['b']
                if result['correct'] and min(a, b) > 0:
                    ratio = max(a, b) / min(a, b)
                    size_ratios.append(ratio)
            
            if size_ratios:
                max_ratio_handled = max(size_ratios)
                robustness_bonus = min(max_ratio_handled / 5.0, 1.0) * weights['size_robustness']
                bonus += robustness_bonus
        
        # Edge case handling bonus
        if 'edge_case_handling' in weights:
            edge_cases_correct = 0
            edge_cases_total = 0
            for result in results:
                a, b = result['a'], result['b']
                # Define edge cases
                if (a == b) or (a == 1 or b == 1) or (max(a, b) > 30):
                    edge_cases_total += 1
                    if result['correct']:
                        edge_cases_correct += 1
            
            if edge_cases_total > 0:
                edge_accuracy = edge_cases_correct / edge_cases_total
                bonus += edge_accuracy * weights['edge_case_handling']
        
        # Penalty for timeouts
        timeout_penalty = sum(1 for r in results if r['result'] == -10) * 10
        
        total_fitness = base_fitness + bonus - timeout_penalty
        return max(0.0, total_fitness)
    
    def adaptive_mutation_rate(self, generation: int, stagnation_count: int) -> float:
        """Adapt mutation rate based on training progress."""
        base_rate = self.base_mutation_rate
        
        # Increase mutation when stagnating
        if stagnation_count > 10:
            stagnation_factor = min(2.0, 1.0 + stagnation_count * 0.1)
            base_rate *= stagnation_factor
        
        # Decrease mutation rate as training progresses (simulated annealing)
        annealing_factor = 1.0 / (1.0 + generation * 0.001)
        base_rate *= annealing_factor
        
        # Keep within reasonable bounds
        return np.clip(base_rate, 0.005, 0.1)
    
    def specialized_crossover(self, parent1: Any, parent2: Any, rng: np.random.Generator) -> Any:
        """
        Specialized crossover for EM43 individuals.
        
        Handles rules, programs, and parameters separately with appropriate strategies.
        """
        # Rule crossover - use multiple crossover points for different rule types
        child_rule = np.zeros_like(parent1.rule)
        rule_sections = len(parent1.rule) // 8  # 8 rule types
        
        for section in range(8):
            start_idx = section * rule_sections
            end_idx = (section + 1) * rule_sections
            
            if rng.random() < 0.5:
                child_rule[start_idx:end_idx] = parent1.rule[start_idx:end_idx]
            else:
                child_rule[start_idx:end_idx] = parent2.rule[start_idx:end_idx]
        
        # Program crossover - preserve program structure
        min_prog_len = min(len(parent1.prog), len(parent2.prog))
        max_prog_len = max(len(parent1.prog), len(parent2.prog))
        
        if min_prog_len > 0:
            crossover_point = rng.integers(1, min_prog_len)
            if len(parent1.prog) <= len(parent2.prog):
                child_prog = np.concatenate([
                    parent1.prog[:crossover_point],
                    parent2.prog[crossover_point:]
                ])
            else:
                child_prog = np.concatenate([
                    parent2.prog[:crossover_point],
                    parent1.prog[crossover_point:]
                ])
        else:
            child_prog = parent1.prog.copy()
        
        # Parameter crossover - blend parameters
        child_encoding = parent1.encoding_params.copy()
        child_decoding = parent1.decoding_params.copy()
        child_halting = parent1.halting_params.copy()
        
        # Randomly inherit some parameters from parent2
        if rng.random() < 0.3:
            child_encoding.update(parent2.encoding_params)
        if rng.random() < 0.3:
            child_decoding.update(parent2.decoding_params)
        if rng.random() < 0.3:
            child_halting.update(parent2.halting_params)
        
        # Create child using the same class as parents
        child_class = type(parent1)
        return child_class(child_rule, child_prog, child_encoding, child_decoding, child_halting)
    
    def diversity_preserving_mutation(self, individual: Any, rng: np.random.Generator) -> Any:
        """
        Apply mutation while preserving population diversity.
        """
        mutated = individual
        
        # Rule mutation with adaptive intensity
        rule_mutation_prob = self.current_mutation_rate
        for i in range(len(mutated.rule)):
            if rng.random() < rule_mutation_prob:
                mutated.rule[i] = rng.integers(0, 4)
        
        # Program mutation
        prog_mutation_prob = self.current_mutation_rate * 0.5  # Lower rate for programs
        for i in range(len(mutated.prog)):
            if rng.random() < prog_mutation_prob:
                mutated.prog[i] = rng.integers(0, 4)
        
        # Parameter mutation (less frequent but more impactful)
        if rng.random() < 0.1:
            # Encoding parameter mutation
            if rng.random() < 0.5:
                mutated.encoding_params['separator_type'] = rng.integers(0, 4)
            if rng.random() < 0.5:
                mutated.encoding_params['input_encoding'] = rng.integers(0, 6)  # More encoding types
        
        if rng.random() < 0.1:
            # Decoding parameter mutation
            mutated.decoding_params['method'] = rng.integers(0, 10)  # More decoding strategies
        
        if rng.random() < 0.1:
            # Halting parameter mutation
            mutated.halting_params['condition_type'] = rng.integers(0, 5)
            mutated.halting_params['threshold'] = rng.integers(30, 90)
        
        return mutated
    
    def tournament_selection(self, population: List[Any], fitness_scores: np.ndarray, 
                           tournament_size: int = 3, rng: np.random.Generator = None) -> Any:
        """Tournament selection with diversity consideration."""
        if rng is None:
            rng = np.random.default_rng()
        
        # Select tournament candidates
        candidates = rng.choice(len(population), tournament_size, replace=False)
        
        # Find winner based on fitness
        winner_idx = candidates[np.argmax(fitness_scores[candidates])]
        return population[winner_idx]
    
    def evolve_generation(self, population: List[Any], fitness_scores: np.ndarray,
                         rng: np.random.Generator) -> List[Any]:
        """Evolve one generation with enhanced operators."""
        new_population = []
        
        # Elite preservation
        n_elite = int(self.elite_fraction * self.population_size)
        elite_indices = np.argsort(fitness_scores)[-n_elite:]
        
        for idx in elite_indices:
            new_population.append(population[idx])
        
        # Generate offspring
        while len(new_population) < self.population_size:
            # Selection
            parent1 = self.tournament_selection(population, fitness_scores, rng=rng)
            parent2 = self.tournament_selection(population, fitness_scores, rng=rng)
            
            # Crossover
            if rng.random() < self.crossover_rate:
                child = self.specialized_crossover(parent1, parent2, rng)
            else:
                child = parent1  # Copy parent1
            
            # Mutation
            child = self.diversity_preserving_mutation(child, rng)
            
            new_population.append(child)
        
        return new_population
    
    def track_statistics(self, generation: int, fitness_scores: np.ndarray, 
                        best_individual: Any, phase_name: str):
        """Track training statistics for analysis."""
        stats = {
            'generation': generation,
            'phase': phase_name,
            'best_fitness': np.max(fitness_scores),
            'mean_fitness': np.mean(fitness_scores),
            'std_fitness': np.std(fitness_scores),
            'diversity': self.calculate_population_diversity(fitness_scores),
            'mutation_rate': self.current_mutation_rate,
            'timestamp': time.time()
        }
        
        self.generation_stats.append(stats)
        
        # Update stagnation counter
        if len(self.best_fitness_history) > 0:
            if stats['best_fitness'] <= self.best_fitness_history[-1] + 1e-6:
                self.stagnation_counter += 1
            else:
                self.stagnation_counter = 0
        
        self.best_fitness_history.append(stats['best_fitness'])
    
    def calculate_population_diversity(self, fitness_scores: np.ndarray) -> float:
        """Calculate population diversity metric."""
        if len(fitness_scores) < 2:
            return 0.0
        
        # Use coefficient of variation as diversity measure
        mean_fitness = np.mean(fitness_scores)
        if mean_fitness == 0:
            return 0.0
        
        return np.std(fitness_scores) / mean_fitness
    
    def save_checkpoint(self, generation: int, population: List[Any], 
                       fitness_scores: np.ndarray, phase_name: str, filepath: str):
        """Save training checkpoint."""
        checkpoint = {
            'generation': generation,
            'phase': phase_name,
            'population': population,
            'fitness_scores': fitness_scores,
            'best_individual': population[np.argmax(fitness_scores)],
            'statistics': self.generation_stats,
            'algorithm_state': {
                'mutation_rate': self.current_mutation_rate,
                'stagnation_counter': self.stagnation_counter,
                'best_fitness_history': self.best_fitness_history
            }
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(checkpoint, f)

def test_enhanced_ga():
    """Test the enhanced genetic algorithm components."""
    print("🧪 TESTING ENHANCED GENETIC ALGORITHM")
    print("=" * 50)
    
    ga = EnhancedGeneticAlgorithm(population_size=10)
    
    # Test adaptive mutation rate
    for gen in range(0, 100, 20):
        for stag in [0, 5, 15]:
            rate = ga.adaptive_mutation_rate(gen, stag)
            print(f"  Gen {gen:2d}, Stagnation {stag:2d}: mutation_rate = {rate:.4f}")
    
    print("\n✅ Enhanced GA test completed")

if __name__ == "__main__":
    test_enhanced_ga()
