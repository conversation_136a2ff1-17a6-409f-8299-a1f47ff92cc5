"""
Information Flow Visualization Tools

This module provides tools to track and visualize how information moves through
cellular automata over time, enabling visual comparison of rule system efficiency.

Visualization Types:
1. Space-time diagrams showing information propagation
2. Information density heatmaps over time
3. Propagation speed analysis charts
4. Comparative side-by-side visualizations
5. Interactive HTML viewers for detailed analysis
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.colors import ListedColormap
from typing import List, Tuple, Dict, Any, Optional
import os

class InformationFlowVisualizer:
    """Visualizer for information flow in cellular automata."""
    
    def __init__(self):
        # Define color scheme for cell types
        self.colors = ['white', 'red', 'orange', 'blue']  # 0, 1, 2, 3
        self.cmap = ListedColormap(self.colors)
        
    def create_spacetime_diagram(self, states: List[np.ndarray], 
                                title: str = "Space-Time Diagram",
                                save_path: Optional[str] = None) -> plt.Figure:
        """
        Create a space-time diagram showing evolution over time.
        
        Args:
            states: List of CA states over time
            title: Title for the plot
            save_path: Optional path to save the figure
            
        Returns:
            Matplotlib figure
        """
        if not states:
            raise ValueError("No states provided")
        
        # Create space-time matrix
        T = len(states)
        N = len(states[0])
        spacetime = np.zeros((T, N))
        
        for t, state in enumerate(states):
            spacetime[t, :] = state
        
        # Create figure
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # Plot space-time diagram
        im = ax.imshow(spacetime, cmap=self.cmap, aspect='auto', 
                      vmin=0, vmax=3, origin='upper')
        
        # Customize plot
        ax.set_xlabel('Space (Cell Position)')
        ax.set_ylabel('Time (Steps)')
        ax.set_title(title)
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax, ticks=[0, 1, 2, 3])
        cbar.set_ticklabels(['Empty', 'Program', 'Marker', 'Blue'])
        
        # Add grid for better readability
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def create_information_density_plot(self, states: List[np.ndarray],
                                      title: str = "Information Density Over Time",
                                      save_path: Optional[str] = None) -> plt.Figure:
        """
        Create a plot showing information density over time.
        
        Args:
            states: List of CA states over time
            title: Title for the plot
            save_path: Optional path to save the figure
            
        Returns:
            Matplotlib figure
        """
        # Calculate information metrics over time
        time_steps = range(len(states))
        nonzero_counts = [np.sum(state != 0) for state in states]
        unique_counts = [len(np.unique(state[state != 0])) if np.sum(state != 0) > 0 else 0 
                        for state in states]
        info_content = [nz * uc for nz, uc in zip(nonzero_counts, unique_counts)]
        
        # Create figure with subplots
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(10, 10))
        
        # Plot 1: Non-zero cell count
        ax1.plot(time_steps, nonzero_counts, 'b-', linewidth=2, label='Non-zero cells')
        ax1.set_ylabel('Count')
        ax1.set_title('Non-zero Cells Over Time')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Plot 2: Unique value count
        ax2.plot(time_steps, unique_counts, 'g-', linewidth=2, label='Unique values')
        ax2.set_ylabel('Count')
        ax2.set_title('Value Diversity Over Time')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # Plot 3: Information content
        ax3.plot(time_steps, info_content, 'r-', linewidth=2, label='Information content')
        ax3.set_xlabel('Time Steps')
        ax3.set_ylabel('Information Content')
        ax3.set_title('Total Information Content Over Time')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        plt.suptitle(title, fontsize=14)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def create_propagation_analysis(self, states: List[np.ndarray], 
                                  source_pos: int,
                                  title: str = "Propagation Analysis",
                                  save_path: Optional[str] = None) -> plt.Figure:
        """
        Create analysis of information propagation from a source.
        
        Args:
            states: List of CA states over time
            source_pos: Position of information source
            title: Title for the plot
            save_path: Optional path to save the figure
            
        Returns:
            Matplotlib figure
        """
        # Calculate propagation metrics
        time_steps = range(len(states))
        max_distances = []
        left_distances = []
        right_distances = []
        
        for state in states:
            nonzero_positions = np.where(state != 0)[0]
            
            if len(nonzero_positions) > 0:
                # Maximum distance from source
                distances = np.abs(nonzero_positions - source_pos)
                max_distances.append(np.max(distances))
                
                # Leftward and rightward propagation
                left_positions = nonzero_positions[nonzero_positions < source_pos]
                right_positions = nonzero_positions[nonzero_positions > source_pos]
                
                left_dist = source_pos - np.min(left_positions) if len(left_positions) > 0 else 0
                right_dist = np.max(right_positions) - source_pos if len(right_positions) > 0 else 0
                
                left_distances.append(left_dist)
                right_distances.append(right_dist)
            else:
                max_distances.append(0)
                left_distances.append(0)
                right_distances.append(0)
        
        # Create figure
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
        
        # Plot 1: Maximum propagation distance
        ax1.plot(time_steps, max_distances, 'b-', linewidth=2, label='Max distance')
        ax1.set_ylabel('Distance (cells)')
        ax1.set_title('Maximum Propagation Distance Over Time')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Plot 2: Directional propagation
        ax2.plot(time_steps, left_distances, 'r-', linewidth=2, label='Leftward')
        ax2.plot(time_steps, right_distances, 'g-', linewidth=2, label='Rightward')
        ax2.set_xlabel('Time Steps')
        ax2.set_ylabel('Distance (cells)')
        ax2.set_title('Directional Propagation Distance')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        plt.suptitle(title, fontsize=14)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def create_comparative_visualization(self, states_4: List[np.ndarray], 
                                       states_8: List[np.ndarray],
                                       scenario_name: str = "Comparison",
                                       save_path: Optional[str] = None) -> plt.Figure:
        """
        Create side-by-side comparison of 4-rule vs 8-rule systems.
        
        Args:
            states_4: States from 4-rule system
            states_8: States from 8-rule system
            scenario_name: Name of the test scenario
            save_path: Optional path to save the figure
            
        Returns:
            Matplotlib figure
        """
        # Create space-time matrices
        T = max(len(states_4), len(states_8))
        N = len(states_4[0]) if states_4 else len(states_8[0])
        
        spacetime_4 = np.zeros((T, N))
        spacetime_8 = np.zeros((T, N))
        
        for t in range(min(len(states_4), T)):
            spacetime_4[t, :] = states_4[t]
        
        for t in range(min(len(states_8), T)):
            spacetime_8[t, :] = states_8[t]
        
        # Create figure with subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 10))
        
        # Space-time diagrams
        im1 = ax1.imshow(spacetime_4, cmap=self.cmap, aspect='auto', 
                        vmin=0, vmax=3, origin='upper')
        ax1.set_title('4-Rule System')
        ax1.set_ylabel('Time Steps')
        
        im2 = ax2.imshow(spacetime_8, cmap=self.cmap, aspect='auto', 
                        vmin=0, vmax=3, origin='upper')
        ax2.set_title('8-Rule System')
        
        # Information density comparison
        nonzero_4 = [np.sum(state != 0) for state in states_4]
        nonzero_8 = [np.sum(state != 0) for state in states_8]
        
        time_4 = range(len(nonzero_4))
        time_8 = range(len(nonzero_8))
        
        ax3.plot(time_4, nonzero_4, 'b-', linewidth=2, label='4-Rule')
        ax3.plot(time_8, nonzero_8, 'r-', linewidth=2, label='8-Rule')
        ax3.set_xlabel('Time Steps')
        ax3.set_ylabel('Non-zero Cells')
        ax3.set_title('Information Density Comparison')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # Propagation speed comparison (simplified)
        max_dist_4 = []
        max_dist_8 = []
        source_pos = N // 2  # Assume center source
        
        for state in states_4:
            nonzero_pos = np.where(state != 0)[0]
            if len(nonzero_pos) > 0:
                max_dist_4.append(np.max(np.abs(nonzero_pos - source_pos)))
            else:
                max_dist_4.append(0)
        
        for state in states_8:
            nonzero_pos = np.where(state != 0)[0]
            if len(nonzero_pos) > 0:
                max_dist_8.append(np.max(np.abs(nonzero_pos - source_pos)))
            else:
                max_dist_8.append(0)
        
        ax4.plot(time_4, max_dist_4, 'b-', linewidth=2, label='4-Rule')
        ax4.plot(time_8, max_dist_8, 'r-', linewidth=2, label='8-Rule')
        ax4.set_xlabel('Time Steps')
        ax4.set_ylabel('Max Distance')
        ax4.set_title('Propagation Distance Comparison')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # Add colorbar
        cbar = plt.colorbar(im1, ax=[ax1, ax2], ticks=[0, 1, 2, 3])
        cbar.set_ticklabels(['Empty', 'Program', 'Marker', 'Blue'])
        
        plt.suptitle(f'Rule System Comparison: {scenario_name}', fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def create_efficiency_summary_plot(self, comparison_results: List[Any],
                                     save_path: Optional[str] = None) -> plt.Figure:
        """
        Create summary plot of efficiency improvements across scenarios.
        
        Args:
            comparison_results: List of ComparisonResult objects
            save_path: Optional path to save the figure
            
        Returns:
            Matplotlib figure
        """
        # Group results by scenario
        scenario_data = {}
        for result in comparison_results:
            scenario = result.test_scenario
            if scenario not in scenario_data:
                scenario_data[scenario] = {
                    'improvements': [],
                    'efficiency_4': [],
                    'efficiency_8': []
                }
            
            scenario_data[scenario]['improvements'].append(result.improvement_factor)
            scenario_data[scenario]['efficiency_4'].append(result.rule_4_metrics.efficiency_score)
            scenario_data[scenario]['efficiency_8'].append(result.rule_8_metrics.efficiency_score)
        
        # Create figure
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Plot 1: Improvement factors by scenario
        scenarios = list(scenario_data.keys())
        improvements = [np.mean(scenario_data[s]['improvements']) for s in scenarios]
        improvement_stds = [np.std(scenario_data[s]['improvements']) for s in scenarios]
        
        bars1 = ax1.bar(scenarios, improvements, yerr=improvement_stds, 
                       capsize=5, alpha=0.7, color='skyblue')
        ax1.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='No improvement')
        ax1.set_ylabel('Improvement Factor')
        ax1.set_title('Efficiency Improvement by Scenario')
        ax1.tick_params(axis='x', rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, val in zip(bars1, improvements):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                    f'{val:.2f}x', ha='center', va='bottom')
        
        # Plot 2: Efficiency scores comparison
        x = np.arange(len(scenarios))
        width = 0.35
        
        eff_4 = [np.mean(scenario_data[s]['efficiency_4']) for s in scenarios]
        eff_8 = [np.mean(scenario_data[s]['efficiency_8']) for s in scenarios]
        
        bars2 = ax2.bar(x - width/2, eff_4, width, label='4-Rule System', alpha=0.7)
        bars3 = ax2.bar(x + width/2, eff_8, width, label='8-Rule System', alpha=0.7)
        
        ax2.set_ylabel('Efficiency Score')
        ax2.set_title('Efficiency Scores by Scenario')
        ax2.set_xticks(x)
        ax2.set_xticklabels(scenarios, rotation=45)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig

def test_visualization_tools():
    """Test the information flow visualization tools."""
    print("🧪 TESTING INFORMATION FLOW VISUALIZATION")
    print("=" * 50)
    
    visualizer = InformationFlowVisualizer()
    
    # Create test data
    N = 50
    T = 20
    states = []
    
    # Simple propagation simulation
    initial_state = np.zeros(N, dtype=np.uint8)
    initial_state[25] = 1  # Source at center
    initial_state[20] = 3  # Barrier
    initial_state[30] = 3  # Barrier
    
    current_state = initial_state.copy()
    states.append(current_state)
    
    for t in range(T):
        next_state = current_state.copy()
        # Simple spreading rule
        for i in range(1, N-1):
            if current_state[i] == 1:
                if current_state[i-1] == 0:
                    next_state[i-1] = 1
                if current_state[i+1] == 0:
                    next_state[i+1] = 1
        states.append(next_state)
        current_state = next_state
    
    # Test visualizations
    print("Creating space-time diagram...")
    fig1 = visualizer.create_spacetime_diagram(states, "Test Space-Time Diagram")
    
    print("Creating information density plot...")
    fig2 = visualizer.create_information_density_plot(states, "Test Information Density")
    
    print("Creating propagation analysis...")
    fig3 = visualizer.create_propagation_analysis(states, 25, "Test Propagation Analysis")
    
    # Close figures to save memory
    plt.close(fig1)
    plt.close(fig2)
    plt.close(fig3)
    
    print("✅ Information flow visualization test completed")

if __name__ == "__main__":
    test_visualization_tools()
