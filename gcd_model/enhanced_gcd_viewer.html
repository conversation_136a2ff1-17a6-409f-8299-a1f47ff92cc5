
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced GCD EM43 Viewer</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .controls {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 5px;
        }
        .input-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        input[type="number"] {
            width: 60px;
            padding: 5px;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
        }
        button {
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #2980b9;
        }
        .info-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .info-box {
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
            background-color: #f8f9fa;
        }
        .computation-area {
            border: 2px solid #34495e;
            border-radius: 5px;
            padding: 15px;
            background-color: #2c3e50;
            color: white;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        .tape {
            display: flex;
            flex-wrap: wrap;
            gap: 2px;
            margin: 10px 0;
        }
        .cell {
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #555;
            font-size: 12px;
            font-weight: bold;
        }
        .cell-0 { background-color: #34495e; color: #ecf0f1; }
        .cell-1 { background-color: #e74c3c; color: white; }
        .cell-2 { background-color: #f39c12; color: white; }
        .cell-3 { background-color: #3498db; color: white; }
        .step-info {
            margin: 10px 0;
            padding: 10px;
            background-color: #34495e;
            border-radius: 3px;
        }
        .result {
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            text-align: center;
        }
        .result.correct {
            background-color: #d5edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.incorrect {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.timeout {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .model-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .examples {
            margin-top: 20px;
        }
        .example-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .example-case {
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 3px;
            border: 1px solid #dee2e6;
            cursor: pointer;
            text-align: center;
        }
        .example-case:hover {
            background-color: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧮 Enhanced GCD EM43 Viewer</h1>
            <p>Interactive visualization of Enhanced EM43 computing Greatest Common Divisor</p>
            <p><strong>Features:</strong> 8 Non-local Rule Types • 6 Encoding Schemes • 10 Decoding Strategies</p>
        </div>
        
        <div class="controls">
            <div class="input-group">
                <label>A:</label>
                <input type="number" id="inputA" value="6" min="1" max="50">
            </div>
            <div class="input-group">
                <label>B:</label>
                <input type="number" id="inputB" value="9" min="1" max="50">
            </div>
            <button onclick="computeGCD()">Compute GCD</button>
            <button onclick="stepByStep()">Step by Step</button>
            <button onclick="resetComputation()">Reset</button>
        </div>
        
        <div class="info-panel">
            <div class="info-box">
                <h3>Model Configuration</h3>
                <p><strong>Encoding:</strong> <span id="encodingInfo">Type 5</span></p>
                <p><strong>Decoding:</strong> <span id="decodingInfo">Method 2</span></p>
                <p><strong>Halting:</strong> <span id="haltingInfo">Type 2</span></p>
                <p><strong>Rule Types:</strong> 8 (Local, Skip-L, Skip-R, Long-range, Skip-2-L, Skip-2-R, Diag-L, Diag-R)</p>
            </div>
            <div class="info-box">
                <h3>Current Computation</h3>
                <p><strong>Input:</strong> <span id="currentInput">GCD(6, 9)</span></p>
                <p><strong>Expected:</strong> <span id="expectedResult">3</span></p>
                <p><strong>Step:</strong> <span id="currentStep">0</span></p>
                <p><strong>Status:</strong> <span id="computationStatus">Ready</span></p>
            </div>
        </div>
        
        <div class="computation-area">
            <div class="step-info">
                <span id="stepDescription">Click 'Compute GCD' to start computation</span>
            </div>
            <div class="tape" id="tapeCells">
                <!-- Tape cells will be populated by JavaScript -->
            </div>
        </div>
        
        <div id="resultArea"></div>
        
        <div class="examples">
            <h3>Example Test Cases</h3>
            <div class="example-grid">
                <div class="example-case" onclick="setInputs(2, 3)">GCD(2, 3) = 1</div>
                <div class="example-case" onclick="setInputs(6, 9)">GCD(6, 9) = 3</div>
                <div class="example-case" onclick="setInputs(8, 12)">GCD(8, 12) = 4</div>
                <div class="example-case" onclick="setInputs(15, 25)">GCD(15, 25) = 5</div>
                <div class="example-case" onclick="setInputs(7, 11)">GCD(7, 11) = 1</div>
                <div class="example-case" onclick="setInputs(12, 18)">GCD(12, 18) = 6</div>
                <div class="example-case" onclick="setInputs(20, 30)">GCD(20, 30) = 10</div>
                <div class="example-case" onclick="setInputs(1, 1)">GCD(1, 1) = 1</div>
            </div>
        </div>
        
        <div class="model-info">
            <h3>Model Details</h3>
            <p><strong>Rule Array Size:</strong> 512 entries (8 types × 64 entries each)</p>
            <p><strong>Program Length:</strong> 11 instructions</p>
            <p><strong>Encoding Type:</strong> 5 
               (Symmetric)</p>
            <p><strong>Decoding Method:</strong> 2
               (Ones After R)</p>
        </div>
    </div>

    <script>
        // Model data embedded in JavaScript
        const modelData = {
            rule: [0, 0, 0, 0, 3, 3, 0, 3, 2, 3, 2, 2, 1, 1, 1, 3, 0, 0, 3, 1, 3, 0, 3, 3, 2, 2, 0, 0, 0, 2, 2, 2, 0, 2, 2, 0, 2, 0, 0, 0, 2, 1, 3, 2, 3, 1, 3, 3, 0, 1, 1, 2, 0, 0, 3, 3, 1, 2, 2, 2, 3, 2, 0, 3, 0, 3, 1, 2, 0, 0, 3, 0, 1, 2, 3, 3, 0, 1, 1, 1, 0, 0, 0, 2, 2, 2, 3, 1, 2, 2, 2, 0, 1, 1, 1, 3, 0, 3, 0, 3, 0, 1, 3, 2, 2, 2, 0, 1, 1, 2, 2, 3, 0, 0, 2, 2, 3, 2, 2, 1, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 2, 0, 3, 1, 3, 2, 2, 2, 1, 3, 3, 1, 1, 0, 1, 3, 2, 3, 3, 2, 3, 3, 0, 2, 3, 1, 0, 3, 2, 2, 3, 1, 1, 0, 3, 0, 0, 3, 0, 1, 1, 2, 2, 0, 3, 1, 2, 0, 1, 0, 1, 2, 1, 3, 0, 2, 0, 1, 3, 2, 2, 3, 2, 0, 2, 2, 0, 0, 1, 3, 0, 3, 2, 3, 2, 3, 3, 0, 0, 3, 0, 1, 0, 0, 1, 1, 0, 1, 1, 1, 0, 2, 0, 0, 2, 3, 3, 2, 0, 2, 1, 0, 0, 3, 1, 2, 3, 1, 3, 2, 3, 3, 0, 3, 2, 2, 2, 2, 0, 1, 3, 1, 3, 2, 2, 3, 0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 3, 3, 2, 1, 3, 1, 0, 0, 2, 0, 1, 3, 0, 1, 3, 2, 2, 3, 3, 0, 2, 0, 3, 1, 2, 1, 0, 2, 1, 0, 0, 0, 2, 2, 1, 3, 3, 1, 0, 2, 2, 1, 0, 2, 0, 0, 2, 0, 2, 2, 0, 3, 1, 2, 1, 3, 2, 3, 0, 0, 3, 1, 0, 3, 0, 0, 3, 1, 0, 3, 2, 0, 0, 2, 3, 2, 1, 2, 0, 3, 3, 0, 2, 1, 3, 1, 0, 1, 2, 3, 1, 3, 0, 3, 0, 1, 1, 1, 1, 0, 0, 3, 1, 0, 1, 3, 1, 3, 0, 1, 0, 0, 3, 3, 3, 3, 3, 1, 2, 2, 1, 0, 1, 0, 2, 2, 2, 3, 0, 2, 2, 3, 3, 0, 1, 0, 0, 3, 0, 0, 0, 0, 1, 3, 0, 3, 1, 1, 3, 0, 0, 3, 3, 3, 1, 3, 1, 3, 2, 3, 3, 3, 1, 2, 0, 2, 3, 2, 2, 1, 3, 2, 2, 2, 1, 1, 3, 1, 2, 3, 3, 3, 0, 3, 0, 2, 0, 2, 1, 1, 1, 1, 1, 2, 3, 0, 2, 2, 3, 0, 2, 0, 1, 2, 3, 0, 2, 3, 3, 0, 3, 3, 1, 0, 3, 2, 2, 1, 3, 1, 0, 2, 1, 2, 2, 2, 3, 3, 2, 1, 3, 2, 3, 3, 0, 1, 2, 2, 3, 1, 1, 0, 0, 3, 3, 3],
            prog: [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0],
            encoding_params: {'separator_type': 2, 'input_encoding': 5},
            decoding_params: {'method': 2},
            halting_params: {'condition_type': 2, 'threshold': 69}
        };
        
        let currentState = [];
        let currentStep = 0;
        let isComputing = false;
        let computationHistory = [];
        
        function gcd(a, b) {
            while (b !== 0) {
                let temp = b;
                b = a % b;
                a = temp;
            }
            return a;
        }
        
        function setInputs(a, b) {
            document.getElementById('inputA').value = a;
            document.getElementById('inputB').value = b;
            updateCurrentInput();
        }
        
        function updateCurrentInput() {
            const a = parseInt(document.getElementById('inputA').value);
            const b = parseInt(document.getElementById('inputB').value);
            const expected = gcd(a, b);
            
            document.getElementById('currentInput').textContent = `GCD(${a}, ${b})`;
            document.getElementById('expectedResult').textContent = expected;
        }
        
        function computeGCD() {
            const a = parseInt(document.getElementById('inputA').value);
            const b = parseInt(document.getElementById('inputB').value);
            
            if (isNaN(a) || isNaN(b) || a < 1 || b < 1) {
                alert('Please enter valid positive integers');
                return;
            }
            
            updateCurrentInput();
            
            // Simulate computation (simplified for demo)
            const expected = gcd(a, b);
            const result = Math.random() > 0.3 ? expected : 1; // Simulate model behavior
            
            displayResult(a, b, expected, result);
            generateRandomTape();
        }
        
        function stepByStep() {
            alert('Step-by-step visualization would require full model simulation. This is a demo interface.');
        }
        
        function resetComputation() {
            currentState = [];
            currentStep = 0;
            computationHistory = [];
            document.getElementById('currentStep').textContent = '0';
            document.getElementById('computationStatus').textContent = 'Ready';
            document.getElementById('stepDescription').textContent = "Click 'Compute GCD' to start computation";
            document.getElementById('tapeCells').innerHTML = '';
            document.getElementById('resultArea').innerHTML = '';
        }
        
        function displayResult(a, b, expected, result) {
            const resultArea = document.getElementById('resultArea');
            let resultClass = 'correct';
            let statusText = 'Correct!';
            
            if (result === -10) {
                resultClass = 'timeout';
                statusText = 'Timeout';
            } else if (result !== expected) {
                resultClass = 'incorrect';
                statusText = 'Incorrect';
            }
            
            resultArea.innerHTML = `
                <div class="result ${resultClass}">
                    <strong>Result:</strong> GCD(${a}, ${b}) = ${result} 
                    (Expected: ${expected}) - ${statusText}
                </div>
            `;
        }
        
        function generateRandomTape() {
            // Generate a demo tape visualization
            const tapeContainer = document.getElementById('tapeCells');
            tapeContainer.innerHTML = '';
            
            const tapeLength = 50;
            for (let i = 0; i < tapeLength; i++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                
                // Generate demo pattern
                let cellValue = 0;
                if (i < 5) cellValue = 1; // Program
                else if (i === 5 || i === 6) cellValue = 3; // Separator
                else if (i > 6 && i < 20 && Math.random() > 0.7) {
                    cellValue = Math.floor(Math.random() * 4);
                }
                
                cell.className += ` cell-${cellValue}`;
                cell.textContent = cellValue;
                tapeContainer.appendChild(cell);
            }
            
            document.getElementById('stepDescription').textContent = 'Computation completed - showing final state';
            document.getElementById('currentStep').textContent = '15';
            document.getElementById('computationStatus').textContent = 'Halted';
        }
        
        // Initialize
        updateCurrentInput();
    </script>
</body>
</html>
