"""
Refined GCD Curriculum Learning Framework

Based on analysis of initial training results, this implements an improved curriculum with:
1. More gradual difficulty progression (10 phases vs 5)
2. Intermediate stages to bridge difficulty gaps
3. Better GCD value progression
4. Adaptive training parameters
5. Commutativity-focused training
"""

import numpy as np
from typing import List, Tuple, Dict, Any
import math

def gcd(a: int, b: int) -> int:
    """Compute GCD using Euclidean algorithm."""
    while b:
        a, b = b, a % b
    return a

class RefinedGCDCurriculumFramework:
    """Refined framework for progressive GCD curriculum learning."""
    
    def __init__(self):
        self.phases = self._create_refined_curriculum_phases()
    
    def _create_refined_curriculum_phases(self) -> Dict[str, Dict[str, Any]]:
        """Create the refined curriculum with 10 progressive phases."""
        
        phases = {
            'phase1_basic_coprime': {
                'name': 'Basic Co-prime Pairs (GCD=1)',
                'description': 'Learn fundamental GCD=1 pattern with very simple cases',
                'cases': self._generate_basic_coprime_cases(),
                'target_accuracy': 0.98,
                'window': 120,
                'max_steps': 250,
                'generations': 150,
                'population_size': 800,
                'mutation_rate': 0.04,
                'elite_fraction': 0.12,
                'fitness_weights': {'accuracy': 100, 'convergence': 25, 'correctness_bonus': 75}
            },
            
            'phase2_extended_coprime': {
                'name': 'Extended Co-prime Pairs',
                'description': 'Reinforce GCD=1 learning with more diverse cases',
                'cases': self._generate_extended_coprime_cases(),
                'target_accuracy': 0.95,
                'window': 140,
                'max_steps': 300,
                'generations': 200,
                'population_size': 1000,
                'mutation_rate': 0.035,
                'elite_fraction': 0.15,
                'fitness_weights': {'accuracy': 100, 'convergence': 20, 'commutativity_bonus': 50}
            },
            
            'phase3_simple_gcd2': {
                'name': 'Simple GCD=2 Cases',
                'description': 'Introduce GCD=2 with very simple, clear cases',
                'cases': self._generate_simple_gcd2_cases(),
                'target_accuracy': 0.90,
                'window': 160,
                'max_steps': 350,
                'generations': 250,
                'population_size': 1200,
                'mutation_rate': 0.03,
                'elite_fraction': 0.18,
                'fitness_weights': {'accuracy': 100, 'convergence': 15, 'gcd_diversity': 40, 'anti_bias': 60}
            },
            
            'phase4_mixed_gcd1_2': {
                'name': 'Mixed GCD=1,2 Cases',
                'description': 'Combine GCD=1 and GCD=2 to prevent overfitting',
                'cases': self._generate_mixed_gcd1_2_cases(),
                'target_accuracy': 0.88,
                'window': 180,
                'max_steps': 400,
                'generations': 300,
                'population_size': 1400,
                'mutation_rate': 0.028,
                'elite_fraction': 0.20,
                'fitness_weights': {'accuracy': 100, 'convergence': 12, 'gcd_diversity': 50, 'commutativity_bonus': 40}
            },
            
            'phase5_simple_gcd3': {
                'name': 'Simple GCD=3 Cases',
                'description': 'Introduce GCD=3 with clear multiples',
                'cases': self._generate_simple_gcd3_cases(),
                'target_accuracy': 0.85,
                'window': 200,
                'max_steps': 450,
                'generations': 350,
                'population_size': 1600,
                'mutation_rate': 0.025,
                'elite_fraction': 0.22,
                'fitness_weights': {'accuracy': 100, 'convergence': 10, 'gcd_diversity': 60, 'anti_bias': 70}
            },
            
            'phase6_small_gcds': {
                'name': 'Small GCDs (1,2,3,4)',
                'description': 'Combine small GCD values with balanced representation',
                'cases': self._generate_small_gcds_cases(),
                'target_accuracy': 0.82,
                'window': 220,
                'max_steps': 500,
                'generations': 400,
                'population_size': 1800,
                'mutation_rate': 0.022,
                'elite_fraction': 0.25,
                'fitness_weights': {'accuracy': 100, 'convergence': 8, 'gcd_diversity': 70, 'generalization': 30}
            },
            
            'phase7_medium_gcds': {
                'name': 'Medium GCDs (1-6)',
                'description': 'Expand to GCD values 1-6 with systematic coverage',
                'cases': self._generate_medium_gcds_cases(),
                'target_accuracy': 0.78,
                'window': 250,
                'max_steps': 600,
                'generations': 450,
                'population_size': 2000,
                'mutation_rate': 0.020,
                'elite_fraction': 0.28,
                'fitness_weights': {'accuracy': 100, 'convergence': 6, 'gcd_diversity': 80, 'size_robustness': 25}
            },
            
            'phase8_size_variations': {
                'name': 'Size Variations',
                'description': 'Handle different input sizes while maintaining GCD accuracy',
                'cases': self._generate_size_variation_cases(),
                'target_accuracy': 0.75,
                'window': 280,
                'max_steps': 700,
                'generations': 500,
                'population_size': 2200,
                'mutation_rate': 0.018,
                'elite_fraction': 0.30,
                'fitness_weights': {'accuracy': 100, 'convergence': 5, 'size_robustness': 60, 'commutativity_bonus': 35}
            },
            
            'phase9_challenging_cases': {
                'name': 'Challenging Cases',
                'description': 'Difficult cases with larger numbers and diverse GCDs',
                'cases': self._generate_challenging_cases(),
                'target_accuracy': 0.70,
                'window': 320,
                'max_steps': 800,
                'generations': 600,
                'population_size': 2500,
                'mutation_rate': 0.015,
                'elite_fraction': 0.32,
                'fitness_weights': {'accuracy': 100, 'convergence': 4, 'edge_case_handling': 50, 'generalization': 40}
            },
            
            'phase10_comprehensive': {
                'name': 'Comprehensive Evaluation',
                'description': 'Final comprehensive test across all difficulty levels',
                'cases': self._generate_comprehensive_cases(),
                'target_accuracy': 0.65,
                'window': 350,
                'max_steps': 1000,
                'generations': 700,
                'population_size': 3000,
                'mutation_rate': 0.012,
                'elite_fraction': 0.35,
                'fitness_weights': {'accuracy': 100, 'convergence': 3, 'comprehensive_score': 80}
            }
        }
        
        return phases
    
    def _generate_basic_coprime_cases(self) -> List[Tuple[int, int]]:
        """Generate very basic co-prime pairs for initial learning."""
        cases = [
            # Tiny cases
            (1, 2), (2, 1), (1, 3), (3, 1), (1, 4), (4, 1),
            (1, 5), (5, 1), (2, 3), (3, 2), (2, 5), (5, 2),
            
            # Small clear cases
            (3, 4), (4, 3), (3, 5), (5, 3), (4, 5), (5, 4),
            (2, 7), (7, 2), (3, 7), (7, 3), (5, 7), (7, 5)
        ]
        return cases
    
    def _generate_extended_coprime_cases(self) -> List[Tuple[int, int]]:
        """Generate extended co-prime pairs to reinforce learning."""
        cases = []
        
        # Include all basic cases
        cases.extend(self._generate_basic_coprime_cases())
        
        # Add more diverse coprime pairs
        additional = [
            (1, 6), (6, 1), (1, 7), (7, 1), (1, 8), (8, 1),
            (3, 8), (8, 3), (5, 6), (6, 5), (5, 8), (8, 5),
            (7, 8), (8, 7), (3, 10), (10, 3), (7, 9), (9, 7),
            (4, 9), (9, 4), (5, 9), (9, 5), (7, 10), (10, 7)
        ]
        cases.extend(additional)
        return list(set(cases))
    
    def _generate_simple_gcd2_cases(self) -> List[Tuple[int, int]]:
        """Generate very simple GCD=2 cases."""
        cases = [
            # Clear multiples of 2
            (2, 4), (4, 2), (2, 6), (6, 2), (2, 8), (8, 2),
            (4, 6), (6, 4), (4, 8), (8, 4), (6, 8), (8, 6),
            (2, 10), (10, 2), (4, 10), (10, 4), (6, 10), (10, 6),
            (8, 10), (10, 8), (4, 12), (12, 4), (6, 12), (12, 6)
        ]
        return cases
    
    def _generate_mixed_gcd1_2_cases(self) -> List[Tuple[int, int]]:
        """Generate mixed GCD=1 and GCD=2 cases."""
        cases = []
        
        # Include some coprime cases (GCD=1)
        coprime_subset = [(1, 3), (3, 1), (3, 5), (5, 3), (5, 7), (7, 5), (3, 7), (7, 3)]
        cases.extend(coprime_subset)
        
        # Include some GCD=2 cases
        gcd2_subset = [(2, 4), (4, 2), (2, 6), (6, 2), (4, 6), (6, 4), (4, 8), (8, 4)]
        cases.extend(gcd2_subset)
        
        return cases
    
    def _generate_simple_gcd3_cases(self) -> List[Tuple[int, int]]:
        """Generate simple GCD=3 cases."""
        cases = [
            # Clear multiples of 3
            (3, 6), (6, 3), (3, 9), (9, 3), (3, 12), (12, 3),
            (6, 9), (9, 6), (6, 12), (12, 6), (9, 12), (12, 9),
            (3, 15), (15, 3), (6, 15), (15, 6), (9, 15), (15, 9),
            (12, 15), (15, 12), (6, 18), (18, 6), (9, 18), (18, 9)
        ]
        return cases
    
    def _generate_small_gcds_cases(self) -> List[Tuple[int, int]]:
        """Generate cases with GCD values 1,2,3,4."""
        cases = []
        
        # Balanced representation of each GCD value
        for target_gcd in [1, 2, 3, 4]:
            gcd_cases = []
            for mult1 in range(1, 6):
                for mult2 in range(mult1, 6):
                    if gcd(mult1, mult2) == 1:  # Ensure mult1, mult2 are coprime
                        a = target_gcd * mult1
                        b = target_gcd * mult2
                        if a <= 20 and b <= 20:
                            gcd_cases.extend([(a, b), (b, a)])
            
            # Limit each GCD to similar number of cases
            cases.extend(gcd_cases[:12])
        
        return list(set(cases))
    
    def _generate_medium_gcds_cases(self) -> List[Tuple[int, int]]:
        """Generate cases with GCD values 1-6."""
        cases = []
        
        for target_gcd in range(1, 7):
            gcd_cases = []
            for mult1 in range(1, 7):
                for mult2 in range(mult1, 7):
                    if gcd(mult1, mult2) == 1:
                        a = target_gcd * mult1
                        b = target_gcd * mult2
                        if a <= 25 and b <= 25:
                            gcd_cases.extend([(a, b), (b, a)])
            
            cases.extend(gcd_cases[:10])  # 10 cases per GCD value
        
        return list(set(cases))
    
    def _generate_size_variation_cases(self) -> List[Tuple[int, int]]:
        """Generate cases with varying input sizes."""
        cases = []
        
        # Small vs medium
        size_patterns = [
            (2, 12), (3, 15), (4, 16), (5, 20), (6, 18),
            (2, 14), (3, 21), (4, 20), (5, 25), (6, 24),
            
            # Medium vs large
            (8, 24), (9, 27), (10, 30), (12, 36), (14, 28),
            (6, 30), (8, 32), (10, 40), (12, 48), (15, 45),
            
            # Include some coprime with size differences
            (3, 20), (5, 22), (7, 24), (11, 25), (13, 28)
        ]
        
        for a, b in size_patterns:
            cases.extend([(a, b), (b, a)])
        
        return list(set(cases))
    
    def _generate_challenging_cases(self) -> List[Tuple[int, int]]:
        """Generate challenging cases with larger numbers."""
        cases = []
        
        # Larger numbers with various GCDs
        challenging_patterns = [
            (15, 25), (18, 24), (20, 30), (21, 35), (24, 36),
            (16, 32), (18, 27), (20, 40), (25, 35), (28, 42),
            (12, 30), (14, 35), (16, 40), (18, 45), (21, 49),
            
            # Some coprime larger cases
            (13, 29), (17, 31), (19, 37), (23, 41), (11, 47)
        ]
        
        for a, b in challenging_patterns:
            cases.extend([(a, b), (b, a)])
        
        return list(set(cases))
    
    def _generate_comprehensive_cases(self) -> List[Tuple[int, int]]:
        """Generate comprehensive test cases covering all learned patterns."""
        cases = []
        
        # Sample from all previous phases
        cases.extend(self._generate_basic_coprime_cases()[:8])
        cases.extend(self._generate_simple_gcd2_cases()[:8])
        cases.extend(self._generate_simple_gcd3_cases()[:8])
        cases.extend(self._generate_small_gcds_cases()[:16])
        cases.extend(self._generate_size_variation_cases()[:12])
        cases.extend(self._generate_challenging_cases()[:12])
        
        # Add some new test cases
        new_cases = [
            (7, 14), (9, 36), (11, 33), (13, 39), (15, 60),
            (8, 48), (12, 60), (16, 64), (18, 72), (20, 80)
        ]
        
        for a, b in new_cases:
            cases.extend([(a, b), (b, a)])
        
        return list(set(cases))
    
    def get_phase(self, phase_name: str) -> Dict[str, Any]:
        """Get configuration for a specific phase."""
        if phase_name not in self.phases:
            raise ValueError(f"Unknown phase: {phase_name}")
        return self.phases[phase_name]
    
    def get_all_phases(self) -> List[str]:
        """Get list of all phase names in order."""
        return [
            'phase1_basic_coprime',
            'phase2_extended_coprime',
            'phase3_simple_gcd2',
            'phase4_mixed_gcd1_2',
            'phase5_simple_gcd3',
            'phase6_small_gcds',
            'phase7_medium_gcds',
            'phase8_size_variations',
            'phase9_challenging_cases',
            'phase10_comprehensive'
        ]
    
    def validate_phase_progression(self, results: Dict[str, float]) -> Dict[str, bool]:
        """Validate if each phase meets its target accuracy."""
        validation = {}
        for phase_name in self.get_all_phases():
            phase_config = self.phases[phase_name]
            target = phase_config['target_accuracy']
            actual = results.get(phase_name, 0.0)
            validation[phase_name] = actual >= target
        return validation

def test_refined_curriculum():
    """Test the refined curriculum framework."""
    print("🧪 TESTING REFINED GCD CURRICULUM FRAMEWORK")
    print("=" * 60)
    
    framework = RefinedGCDCurriculumFramework()
    
    for phase_name in framework.get_all_phases():
        phase = framework.get_phase(phase_name)
        cases = phase['cases']
        
        print(f"\n{phase['name']}:")
        print(f"  Cases: {len(cases)}")
        print(f"  Target accuracy: {phase['target_accuracy']}")
        print(f"  Population: {phase['population_size']}")
        print(f"  Generations: {phase['generations']}")
        print(f"  Sample cases: {cases[:8]}")
        
        # Verify GCD values
        gcd_values = [gcd(a, b) for a, b in cases]
        unique_gcds = sorted(set(gcd_values))
        print(f"  GCD values: {unique_gcds}")
    
    total_cases = sum(len(framework.get_phase(p)['cases']) for p in framework.get_all_phases())
    print(f"\nTotal training cases across all phases: {total_cases}")
    print("✅ Refined curriculum framework test completed")

if __name__ == "__main__":
    test_refined_curriculum()
