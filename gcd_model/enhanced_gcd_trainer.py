"""
Enhanced GCD Trainer with Curriculum Learning

This is the main training pipeline that integrates all components:
- Enhanced non-local rules (8 rule types)
- GCD-specific encoding schemes (6 types)
- GCD-specific decoding strategies (10 types)
- Curriculum learning framework (5 phases)
- Enhanced genetic algorithm

The trainer progressively trains through curriculum phases with model transfer.
"""

import numpy as np
import sys
import os
import pickle
import time
from typing import List, Tuple, Dict, Any, Optional
from tqdm import tqdm

# Add em43_python to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'em43_python'))

# Import our enhanced components
from enhanced_nonlocal_rules import EnhancedNonlocalRules, create_random_enhanced_rules
from gcd_encoding_schemes import GCDEncodingSchemes
from gcd_decoding_strategies import GCDDecodingStrategies
from gcd_curriculum_framework import GCDCurriculumFramework, gcd
from enhanced_genetic_algorithm import EnhancedGeneticAlgorithm

# Import base EM43 components
from em43_numba import _sanitize_programme, _sanitize_rule

class EnhancedGCDModel:
    """Enhanced EM43 model for GCD computation with all improvements."""
    
    def __init__(self, rule: np.ndarray, prog: np.ndarray, 
                 encoding_params: Dict, decoding_params: Dict, halting_params: Dict):
        self.rule = rule.copy()
        self.prog = prog.copy()
        self.encoding_params = encoding_params.copy()
        self.decoding_params = decoding_params.copy()
        self.halting_params = halting_params.copy()
        
        # Initialize enhanced rule system
        self.nonlocal_rules = EnhancedNonlocalRules(self.rule)
    
    def encode_inputs(self, a: int, b: int, N: int = 100) -> np.ndarray:
        """Encode inputs using selected encoding scheme."""
        encoding_type = self.encoding_params.get('input_encoding', 0)
        prog_len = len(self.prog)
        
        if encoding_type == 0:
            return GCDEncodingSchemes.encode_standard_separated(a, b, prog_len, N)
        elif encoding_type == 1:
            return GCDEncodingSchemes.encode_commutative_overlap(a, b, prog_len, N)
        elif encoding_type == 2:
            return GCDEncodingSchemes.encode_factorization_hint(a, b, prog_len, N)
        elif encoding_type == 3:
            return GCDEncodingSchemes.encode_binary_representation(a, b, prog_len, N)
        elif encoding_type == 4:
            return GCDEncodingSchemes.encode_divisor_pattern(a, b, prog_len, N)
        elif encoding_type == 5:
            return GCDEncodingSchemes.encode_symmetric_layout(a, b, prog_len, N)
        else:
            return GCDEncodingSchemes.encode_standard_separated(a, b, prog_len, N)
    
    def check_halting(self, state: np.ndarray) -> bool:
        """Check halting condition using enhanced criteria."""
        N = len(state)
        
        # Count cell types
        counts = [0, 0, 0, 0]
        for x in state:
            counts[x] += 1
        
        blank, prog, red, blue = counts
        nonzero = prog + red + blue
        
        if nonzero == 0:
            return False
        
        halting_type = self.halting_params.get('condition_type', 0)
        threshold = self.halting_params.get('threshold', 50) / 100.0
        
        if halting_type == 0:  # Blue ratio
            return blue / nonzero >= threshold
        elif halting_type == 1:  # Stability (low program ratio)
            return prog / nonzero <= (1.0 - threshold)
        elif halting_type == 2:  # Combined blue + red
            return (blue + red) / nonzero >= threshold
        elif halting_type == 3:  # Absolute blue count
            return blue >= int(threshold * 20)
        else:  # Enhanced stability
            blue_ok = blue / nonzero >= threshold * 0.7
            stable_ok = prog / nonzero <= 0.4
            return blue_ok and stable_ok
    
    def decode_result(self, state: np.ndarray) -> int:
        """Decode result using selected decoding strategy."""
        decoding_method = self.decoding_params.get('method', 0)
        encoding_type = self.encoding_params.get('input_encoding', 0)
        
        if decoding_method == 0:
            return GCDDecodingStrategies.decode_zeros_after_last_marker(state)
        elif decoding_method == 1:
            return GCDDecodingStrategies.decode_zeros_between_markers(state)
        elif decoding_method == 2:
            return GCDDecodingStrategies.decode_ones_after_last_marker(state)
        elif decoding_method == 3:
            return GCDDecodingStrategies.decode_marker_count(state)
        elif decoding_method == 4:
            return GCDDecodingStrategies.decode_blue_cell_count(state)
        elif decoding_method == 5:
            return GCDDecodingStrategies.decode_pattern_length(state)
        elif decoding_method == 6:
            return GCDDecodingStrategies.decode_divisor_markers(state)
        elif decoding_method == 7:
            return GCDDecodingStrategies.decode_remainder_pattern(state)
        elif decoding_method == 8:
            return GCDDecodingStrategies.decode_consensus(state)
        elif decoding_method == 9:
            return GCDDecodingStrategies.decode_adaptive(state, encoding_type)
        else:
            return GCDDecodingStrategies.decode_consensus(state)
    
    def simulate(self, a: int, b: int, max_steps: int = 500) -> int:
        """Run complete GCD simulation."""
        N = 150  # Larger window for complex cases
        state = self.encode_inputs(a, b, N)
        
        # Write program to initial state
        for i in range(min(len(self.prog), N)):
            state[i] = self.prog[i]
        
        for step in range(max_steps):
            # Check halting condition
            if self.check_halting(state):
                return self.decode_result(state)
            
            # Apply enhanced nonlocal rules
            state = self.nonlocal_rules.apply_rules(state)
        
        return -10  # Timeout

def create_random_enhanced_gcd_model(rng: np.random.Generator) -> EnhancedGCDModel:
    """Create a random enhanced GCD model."""
    
    # Create 512-entry enhanced rule (8 types × 64 entries each)
    rule = create_random_enhanced_rules(rng)
    
    # Create program
    prog = rng.choice([0, 1], size=rng.integers(6, 16), p=[0.6, 0.4])
    prog = _sanitize_programme(prog)
    
    # Enhanced encoding parameters (6 encoding types)
    encoding_params = {
        'separator_type': rng.integers(0, 4),
        'input_encoding': rng.integers(0, 6),
    }
    
    # Enhanced decoding parameters (10 decoding strategies)
    decoding_params = {
        'method': rng.integers(0, 10),
    }
    
    # Enhanced halting parameters
    halting_params = {
        'condition_type': rng.integers(0, 5),
        'threshold': rng.integers(40, 85),
    }
    
    return EnhancedGCDModel(rule, prog, encoding_params, decoding_params, halting_params)

def evaluate_enhanced_gcd_model(model: EnhancedGCDModel, test_cases: List[Tuple[int, int]]) -> Tuple[float, float, List[Dict]]:
    """Evaluate enhanced GCD model on test cases."""
    correct = 0
    convergent = 0
    results = []
    
    for a, b in test_cases:
        expected = gcd(a, b)
        try:
            result = model.simulate(a, b, max_steps=600)
            is_correct = (result == expected)
            is_convergent = (result != -10)
            
            if is_correct:
                correct += 1
            if is_convergent:
                convergent += 1
            
            results.append({
                'a': a, 'b': b, 'expected': expected, 'result': result,
                'correct': is_correct, 'convergent': is_convergent
            })
            
        except Exception as e:
            results.append({
                'a': a, 'b': b, 'expected': expected, 'result': -99,
                'correct': False, 'convergent': False
            })
    
    accuracy = correct / len(test_cases) if test_cases else 0
    convergence = convergent / len(test_cases) if test_cases else 0
    
    return accuracy, convergence, results

class EnhancedGCDTrainer:
    """Main trainer class for enhanced GCD computation."""
    
    def __init__(self):
        self.curriculum = GCDCurriculumFramework()
        self.ga = EnhancedGeneticAlgorithm()
        self.training_history = []
        
    def train_phase(self, phase_name: str, initial_population: Optional[List[EnhancedGCDModel]] = None) -> Tuple[EnhancedGCDModel, float, float]:
        """Train a single curriculum phase."""
        
        print(f"\n🎯 TRAINING PHASE: {phase_name}")
        print("=" * 60)
        
        phase_config = self.curriculum.get_phase(phase_name)
        test_cases = phase_config['cases']
        
        print(f"Phase: {phase_config['name']}")
        print(f"Cases: {len(test_cases)}")
        print(f"Target accuracy: {phase_config['target_accuracy']}")
        print(f"Sample cases: {test_cases[:10]}")
        
        # Initialize or use provided population
        if initial_population is None:
            print("Initializing random population...")
            rng = np.random.default_rng(42)
            population = []
            for _ in range(phase_config['population_size']):
                population.append(create_random_enhanced_gcd_model(rng))
        else:
            print("Using transferred population...")
            population = initial_population[:phase_config['population_size']]
            # Fill remaining slots if needed
            if len(population) < phase_config['population_size']:
                rng = np.random.default_rng(42)
                while len(population) < phase_config['population_size']:
                    population.append(create_random_enhanced_gcd_model(rng))
        
        # Update GA parameters for this phase
        self.ga.population_size = phase_config['population_size']
        self.ga.elite_fraction = phase_config['elite_fraction']
        self.ga.base_mutation_rate = phase_config['mutation_rate']
        self.ga.current_mutation_rate = phase_config['mutation_rate']
        
        best_model = None
        best_fitness = 0.0
        best_accuracy = 0.0
        
        rng = np.random.default_rng()
        
        print(f"Starting evolution for {phase_config['generations']} generations...")
        
        for generation in tqdm(range(phase_config['generations']), desc="Evolving"):
            
            # Evaluate population
            fitness_scores = []
            for individual in population:
                fitness = self.ga.calculate_curriculum_fitness(
                    individual, test_cases, phase_config, evaluate_enhanced_gcd_model
                )
                fitness_scores.append(fitness)
            
            fitness_scores = np.array(fitness_scores)
            
            # Track best individual
            best_idx = np.argmax(fitness_scores)
            if fitness_scores[best_idx] > best_fitness:
                best_fitness = fitness_scores[best_idx]
                best_model = population[best_idx]
                
                # Evaluate best model
                accuracy, convergence, _ = evaluate_enhanced_gcd_model(best_model, test_cases)
                best_accuracy = accuracy
                
                if generation % 50 == 0 or accuracy > phase_config['target_accuracy']:
                    print(f"\n  Gen {generation:3d}: fitness={best_fitness:.1f}, acc={accuracy:.3f}, conv={convergence:.3f}")
            
            # Update GA adaptive parameters
            self.ga.current_mutation_rate = self.ga.adaptive_mutation_rate(generation, self.ga.stagnation_counter)
            
            # Track statistics
            self.ga.track_statistics(generation, fitness_scores, best_model, phase_name)
            
            # Early stopping if target reached
            if best_accuracy >= phase_config['target_accuracy']:
                print(f"  🎯 Target accuracy reached at generation {generation}!")
                break
            
            # Evolve population
            if generation < phase_config['generations'] - 1:
                population = self.ga.evolve_generation(population, fitness_scores, rng)
        
        # Final evaluation
        final_accuracy, final_convergence, results = evaluate_enhanced_gcd_model(best_model, test_cases)
        
        print(f"\n📊 PHASE {phase_name} RESULTS:")
        print(f"  Final accuracy: {final_accuracy:.3f} (target: {phase_config['target_accuracy']:.3f})")
        print(f"  Final convergence: {final_convergence:.3f}")
        print(f"  Best fitness: {best_fitness:.1f}")
        
        # Save phase checkpoint
        checkpoint_path = f"checkpoint_{phase_name}.pkl"
        self.ga.save_checkpoint(generation, population, fitness_scores, phase_name, checkpoint_path)
        print(f"  💾 Checkpoint saved: {checkpoint_path}")
        
        return best_model, final_accuracy, final_convergence

    def run_full_curriculum(self) -> EnhancedGCDModel:
        """Run the complete curriculum training pipeline."""

        print("🚀 ENHANCED GCD CURRICULUM TRAINING")
        print("=" * 80)
        print("Training EM43 to compute GCD using curriculum learning")
        print("Components: Enhanced nonlocal rules + GCD encoding/decoding + Curriculum")

        all_phases = self.curriculum.get_all_phases()
        current_population = None
        phase_results = {}

        start_time = time.time()

        for phase_idx, phase_name in enumerate(all_phases):
            phase_start = time.time()

            print(f"\n{'='*20} PHASE {phase_idx+1}/{len(all_phases)} {'='*20}")

            # Train this phase
            best_model, accuracy, convergence = self.train_phase(phase_name, current_population)

            phase_results[phase_name] = {
                'accuracy': accuracy,
                'convergence': convergence,
                'model': best_model
            }

            phase_time = time.time() - phase_start
            print(f"Phase completed in {phase_time:.1f} seconds")

            # Transfer best individuals to next phase
            if phase_idx < len(all_phases) - 1:
                print("Preparing population transfer to next phase...")
                # Use elite individuals from current generation as seed for next phase
                current_population = [best_model] * 50  # Seed with best model

            # Save intermediate results
            with open(f'phase_results_{phase_name}.pkl', 'wb') as f:
                pickle.dump(phase_results[phase_name], f)

        total_time = time.time() - start_time

        # Final comprehensive evaluation
        print(f"\n🏆 CURRICULUM TRAINING COMPLETE!")
        print(f"Total training time: {total_time:.1f} seconds ({total_time/60:.1f} minutes)")

        # Get final best model
        final_model = phase_results[all_phases[-1]]['model']

        # Comprehensive test on all curriculum cases
        print(f"\n🧪 COMPREHENSIVE EVALUATION:")
        all_test_cases = self.curriculum.get_comprehensive_test_set()
        final_accuracy, final_convergence, final_results = evaluate_enhanced_gcd_model(
            final_model, all_test_cases
        )

        print(f"Comprehensive Results:")
        print(f"  Test cases: {len(all_test_cases)}")
        print(f"  Accuracy: {final_accuracy:.3f} ({final_accuracy*100:.1f}%)")
        print(f"  Convergence: {final_convergence:.3f} ({final_convergence*100:.1f}%)")

        # Phase-by-phase summary
        print(f"\n📊 PHASE SUMMARY:")
        for phase_name in all_phases:
            result = phase_results[phase_name]
            target = self.curriculum.get_phase(phase_name)['target_accuracy']
            status = "✅" if result['accuracy'] >= target else "❌"
            print(f"  {phase_name:20}: {result['accuracy']:.3f} (target: {target:.3f}) {status}")

        # Save final model
        final_model_data = {
            'rule': final_model.rule,
            'prog': final_model.prog,
            'encoding_params': final_model.encoding_params,
            'decoding_params': final_model.decoding_params,
            'halting_params': final_model.halting_params,
            'final_accuracy': final_accuracy,
            'final_convergence': final_convergence,
            'phase_results': phase_results,
            'training_time': total_time,
            'comprehensive_results': final_results
        }

        with open('enhanced_gcd_model_final.pkl', 'wb') as f:
            pickle.dump(final_model_data, f)

        print(f"\n💾 Final model saved: enhanced_gcd_model_final.pkl")
        print(f"🎉 Enhanced GCD training pipeline complete!")

        return final_model

def run_enhanced_gcd_training(quick_test=False):
    """Main function to run the enhanced GCD training."""
    trainer = EnhancedGCDTrainer()

    if quick_test:
        # Override curriculum for quick testing
        for phase_name in trainer.curriculum.get_all_phases():
            phase = trainer.curriculum.phases[phase_name]
            phase['generations'] = 20  # Much shorter for testing
            phase['population_size'] = 50  # Smaller population
            phase['cases'] = phase['cases'][:10]  # Fewer test cases
        print("🚀 RUNNING QUICK TEST MODE")

    final_model = trainer.run_full_curriculum()
    return final_model

def test_enhanced_trainer():
    """Test the enhanced trainer components."""
    print("🧪 TESTING ENHANCED GCD TRAINER")
    print("=" * 50)

    # Test model creation
    rng = np.random.default_rng(42)
    model = create_random_enhanced_gcd_model(rng)

    print(f"Created model with:")
    print(f"  Rule size: {len(model.rule)}")
    print(f"  Program size: {len(model.prog)}")
    print(f"  Encoding params: {model.encoding_params}")
    print(f"  Decoding params: {model.decoding_params}")

    # Test simulation
    test_cases = [(3, 6), (4, 4), (5, 7)]
    for a, b in test_cases:
        result = model.simulate(a, b, max_steps=100)
        expected = gcd(a, b)
        print(f"  GCD({a},{b})={expected} → {result}")

    print("\n✅ Enhanced trainer test completed")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "train":
        quick_test = len(sys.argv) > 2 and sys.argv[2] == "quick"
        run_enhanced_gcd_training(quick_test=quick_test)
    else:
        test_enhanced_trainer()
