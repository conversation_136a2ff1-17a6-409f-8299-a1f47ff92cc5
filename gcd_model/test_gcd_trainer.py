"""
Quick test of the GCD trainer to verify it works before running full training
"""

import numpy as np
from commutative_nonlocal_gcd_trainer import (
    gcd, create_gcd_test_cases, evaluate_gcd_model, 
    create_gcd_adaptive_em43, genetic_algorithm_gcd
)

def test_gcd_function():
    """Test the GCD function itself"""
    print("🧮 Testing GCD function:")
    test_cases = [
        (12, 8, 4),
        (15, 25, 5),
        (7, 11, 1),
        (6, 9, 3),
        (10, 15, 5),
        (14, 21, 7),
        (1, 1, 1),
        (5, 5, 5)
    ]
    
    for a, b, expected in test_cases:
        result = gcd(a, b)
        status = "✅" if result == expected else "❌"
        print(f"  GCD({a},{b}) = {result} (expected {expected}) {status}")

def test_model_creation():
    """Test creating GCD models"""
    print("\n🏗️ Testing model creation:")
    rng = np.random.default_rng(42)
    
    try:
        model = create_gcd_adaptive_em43(rng)
        print(f"  ✅ Model created successfully")
        print(f"  Rule length: {len(model.rule)}")
        print(f"  Program length: {len(model.prog)}")
        print(f"  Encoding params: {model.encoding_params}")
        print(f"  Decoding params: {model.decoding_params}")
        print(f"  Halting params: {model.halting_params}")
        
        # Test a simple simulation
        try:
            result = model.simulate(2, 3, max_steps=100)
            print(f"  Test simulation GCD(2,3): {result} (expected 1)")
        except Exception as e:
            print(f"  Simulation error: {e}")
            
    except Exception as e:
        print(f"  ❌ Model creation failed: {e}")

def test_evaluation():
    """Test model evaluation"""
    print("\n📊 Testing model evaluation:")
    rng = np.random.default_rng(42)
    model = create_gcd_adaptive_em43(rng)
    
    small_test_cases = [(1, 1), (2, 1), (2, 2), (3, 2), (4, 2)]
    
    try:
        accuracy, convergence, results = evaluate_gcd_model(model, small_test_cases, verbose=True)
        print(f"  Accuracy: {accuracy:.3f}")
        print(f"  Convergence: {convergence:.3f}")
    except Exception as e:
        print(f"  ❌ Evaluation failed: {e}")

def run_mini_training():
    """Run a very small training session to test the GA"""
    print("\n🧬 Testing mini genetic algorithm:")
    
    try:
        # Very small parameters for quick test
        best_model, best_fitness = genetic_algorithm_gcd(
            population_size=20,  # Very small
            generations=5,       # Very few
            elite_fraction=0.2
        )
        
        print(f"  ✅ Mini training completed!")
        print(f"  Best fitness: {best_fitness:.2f}")
        
        # Test the best model
        test_cases = [(1, 1), (2, 1), (2, 2), (3, 2)]
        accuracy, convergence, _ = evaluate_gcd_model(best_model, test_cases)
        print(f"  Final test - Accuracy: {accuracy:.3f}, Convergence: {convergence:.3f}")
        
    except Exception as e:
        print(f"  ❌ Mini training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔬 TESTING GCD TRAINER COMPONENTS")
    print("=" * 50)
    
    test_gcd_function()
    test_model_creation()
    test_evaluation()
    run_mini_training()
    
    print(f"\n✅ All tests completed!")
    print(f"Ready to run full GCD training with:")
    print(f"  python3 commutative_nonlocal_gcd_trainer.py")
