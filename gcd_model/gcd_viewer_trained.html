<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergent Model - EM43 - GCD with Nonlocal Rules</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .success-banner {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .input-group {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .input-group label {
            font-weight: 600;
            color: #495057;
            min-width: 30px;
        }
        
        .input-group input {
            padding: 10px 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 16px;
            width: 100px;
            transition: border-color 0.3s;
        }
        
        .input-group input:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .run-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .run-btn:hover {
            transform: translateY(-2px);
        }
        
        .zoom-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .zoom-slider {
            width: 200px;
        }
        
        .visualization {
            padding: 30px;
            text-align: center;
        }
        
        .ca-display {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .results {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        
        .result-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .result-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .result-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }
        
        .result-value {
            font-size: 1.2em;
            font-weight: bold;
        }
        
        .correct { color: #28a745; }
        .incorrect { color: #dc3545; }
        .timeout { color: #ffc107; }
        
        .cell {
            display: inline-block;
            width: 8px;
            height: 8px;
            margin: 1px;
            border-radius: 1px;
        }
        
        .cell-0 { background-color: #ffffff; border: 1px solid #e0e0e0; }
        .cell-1 { background-color: #ff6b6b; }
        .cell-2 { background-color: #4ecdc4; }
        .cell-3 { background-color: #45b7d1; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Emergent Model - EM43 - GCD with Nonlocal Rules</h1>
            <p>A trained EM43 cellular automaton with nonlocal rules that computes GCD through emergent computation.</p>
        </div>
        
        <div class="success-banner">
            🎯 Commutative GCD Computation: GCD(a,b) = GCD(b,a) - Test it with any natural numbers!
        </div>
        
        <div class="controls">
            <div class="input-group">
                <label for="inputA">a =</label>
                <input type="number" id="inputA" value="6" min="1" max="100">
                
                <label for="inputB">b =</label>
                <input type="number" id="inputB" value="9" min="1" max="100">
                
                <button class="run-btn" onclick="runSimulation()">run</button>
                
                <div class="zoom-control">
                    <label>zoom</label>
                    <input type="range" class="zoom-slider" id="zoomSlider" min="1" max="10" value="5" oninput="updateZoom()">
                </div>
            </div>
        </div>
        
        <div class="visualization">
            <div class="ca-display" id="caDisplay">
                Click "run" to start simulation
            </div>
        </div>
        
        <div class="results">
            <div class="result-grid">
                <div class="result-item">
                    <div class="result-label">a + b</div>
                    <div class="result-value" id="inputSum">: 6 + 9</div>
                </div>
                <div class="result-item">
                    <div class="result-label">predicted</div>
                    <div class="result-value" id="predicted">: ?</div>
                </div>
                <div class="result-item">
                    <div class="result-label">true value</div>
                    <div class="result-value" id="trueValue">: ?</div>
                </div>
                <div class="result-item">
                    <div class="result-label">accuracy</div>
                    <div class="result-value" id="accuracy">: ?</div>
                </div>
                <div class="result-item">
                    <div class="result-label">steps</div>
                    <div class="result-value" id="steps">: ?</div>
                </div>
                <div class="result-item">
                    <div class="result-label">width</div>
                    <div class="result-value" id="width">: ?</div>
                </div>
                <div class="result-item">
                    <div class="result-label">time (ms)</div>
                    <div class="result-value" id="time">: ?</div>
                </div>
                <div class="result-item">
                    <div class="result-label">nonlocal</div>
                    <div class="result-value" id="nonlocal">: enabled (4 rule types)</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Model data from Python
        const modelData = {
        "rule": [
                0,
                1,
                0,
                0,
                3,
                2,
                3,
                2,
                2,
                2,
                1,
                2,
                0,
                2,
                0,
                3,
                2,
                0,
                2,
                2,
                1,
                0,
                1,
                0,
                1,
                0,
                2,
                3,
                1,
                1,
                1,
                2,
                0,
                0,
                0,
                3,
                0,
                3,
                3,
                0,
                0,
                0,
                1,
                1,
                1,
                0,
                1,
                2,
                0,
                1,
                3,
                1,
                0,
                1,
                0,
                1,
                3,
                0,
                2,
                0,
                3,
                2,
                2,
                1,
                0,
                3,
                0,
                0,
                1,
                3,
                0,
                1,
                2,
                0,
                0,
                2,
                0,
                0,
                1,
                2,
                2,
                0,
                1,
                2,
                1,
                3,
                2,
                1,
                3,
                1,
                2,
                3,
                2,
                0,
                1,
                0,
                1,
                0,
                2,
                1,
                2,
                2,
                0,
                0,
                1,
                0,
                3,
                1,
                3,
                3,
                0,
                3,
                1,
                1,
                3,
                1,
                2,
                2,
                2,
                3,
                1,
                2,
                2,
                1,
                2,
                0,
                0,
                2,
                3,
                1,
                0,
                1,
                3,
                0,
                0,
                1,
                2,
                2,
                2,
                3,
                1,
                3,
                0,
                3,
                2,
                1,
                1,
                1,
                0,
                0,
                1,
                3,
                0,
                0,
                3,
                1,
                2,
                0,
                2,
                2,
                2,
                1,
                3,
                3,
                3,
                1,
                0,
                1,
                1,
                3,
                2,
                2,
                1,
                2,
                2,
                1,
                1,
                3,
                1,
                0,
                3,
                0,
                2,
                0,
                2,
                0,
                1,
                1,
                1,
                3,
                0,
                3,
                1,
                3,
                2,
                0,
                1,
                3,
                2,
                1,
                3,
                1,
                0,
                2,
                3,
                0,
                3,
                0,
                0,
                3,
                2,
                2,
                1,
                0,
                1,
                1,
                0,
                1,
                0,
                3,
                3,
                2,
                1,
                2,
                1,
                0,
                0,
                2,
                0,
                3,
                1,
                0,
                2,
                2,
                2,
                1,
                3,
                0,
                1,
                3,
                0,
                2,
                3,
                0,
                3,
                1,
                0,
                3,
                0,
                2,
                0,
                1,
                1,
                2,
                0,
                2
        ],
        "prog": [
                1,
                0,
                1,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0
        ],
        "encoding_params": {
                "separator_type": 1,
                "input_encoding": 1
        },
        "decoding_params": {
                "method": 3
        },
        "halting_params": {
                "condition_type": 0,
                "threshold": 54
        },
        "fitness": 86.0,
        "accuracy": 0.6065573770491803
};
        
        let currentZoom = 5;
        
        function gcd(a, b) {
            while (b !== 0) {
                let temp = b;
                b = a % b;
                a = temp;
            }
            return a;
        }
        
        function updateInputDisplay() {
            const a = parseInt(document.getElementById('inputA').value);
            const b = parseInt(document.getElementById('inputB').value);
            document.getElementById('inputSum').textContent = `: ${a} + ${b}`;
            document.getElementById('trueValue').textContent = `: ${gcd(a, b)}`;
        }
        
        function updateZoom() {
            currentZoom = parseInt(document.getElementById('zoomSlider').value);
            // Re-render current display if exists
            const display = document.getElementById('caDisplay');
            if (display.dataset.lastState) {
                renderState(JSON.parse(display.dataset.lastState), currentZoom);
            }
        }
        
        function encodeInputs(a, b, N = 500) {
            const state = new Array(N).fill(0);
            const prog = modelData.prog;
            const L = prog.length;
            
            // Write program
            for (let j = 0; j < Math.min(L, N); j++) {
                state[j] = prog[j];
            }
            
            // Separator (using encoding params)
            const sepType = modelData.encoding_params.separator_type;
            let basePos = L;
            
            if (sepType === 0) { // BB
                if (basePos + 1 < N) {
                    state[basePos] = 3;
                    state[basePos + 1] = 3;
                }
                basePos += 2;
            } else if (sepType === 1) { // B
                if (basePos < N) {
                    state[basePos] = 3;
                }
                basePos += 1;
            } else if (sepType === 2) { // BBB
                for (let i = 0; i < 3; i++) {
                    if (basePos + i < N) {
                        state[basePos + i] = 3;
                    }
                }
                basePos += 3;
            }
            
            // Commutative encoding (input_encoding = 1)
            const maxInput = Math.max(a, b);
            for (let i = 0; i < maxInput; i++) {
                if (basePos + i < N) {
                    state[basePos + i] = 0;
                }
            }
            
            const rPos = basePos + maxInput;
            if (rPos < N) {
                state[rPos] = 2;
            }
            
            return state;
        }
        
        function checkHalting(state) {
            // Simplified halting condition
            const haltType = modelData.halting_params.condition_type;
            const threshold = modelData.halting_params.threshold;
            
            // Count non-zero cells
            const nonZero = state.filter(x => x !== 0).length;
            const ratio = (state.length - nonZero) / state.length * 100;
            
            return ratio >= threshold;
        }
        
        function decodeResult(state) {
            const method = modelData.decoding_params.method;
            const N = state.length;
            
            if (method === 0) { // Count zeros after rightmost R
                let rightmostR = -1;
                for (let i = N - 1; i >= 0; i--) {
                    if (state[i] === 2) {
                        rightmostR = i;
                        break;
                    }
                }
                
                if (rightmostR === -1) return -10;
                
                let zeroCount = 0;
                for (let i = rightmostR + 1; i < N; i++) {
                    if (state[i] === 0) {
                        zeroCount++;
                    } else if (state[i] !== 0) {
                        break;
                    }
                }
                return zeroCount;
            }
            
            // Other decoding methods...
            return -10;
        }
        
        function simulateStep(state) {
            const N = state.length;
            const nxt = new Array(N).fill(0);
            const rule = modelData.rule;
            
            // Extract rule components
            const ruleLocal = rule.slice(0, 64);
            const ruleSkipLeft = rule.slice(64, 128);
            const ruleSkipRight = rule.slice(128, 192);
            const ruleLongRange = rule.slice(192, 256);
            
            for (let x = 1; x < N - 1; x++) {
                const left = x > 0 ? state[x - 1] : 0;
                const center = state[x];
                const right = x < N - 1 ? state[x + 1] : 0;
                const localIdx = (left << 4) | (center << 2) | right;
                
                // Default to local rule
                nxt[x] = ruleLocal[localIdx];
                
                // Apply nonlocal rules
                if (x >= 2 && x < N - 2) {
                    const left2 = state[x - 2];
                    const right2 = state[x + 2];
                    
                    if (center !== 0) {
                        const longRangeIdx = (left2 << 4) | (center << 2) | right2;
                        nxt[x] = ruleLongRange[longRangeIdx];
                    } else if (left !== 0 && right === 0) {
                        const skipLeftIdx = (left2 << 4) | (center << 2) | right;
                        nxt[x] = ruleSkipLeft[skipLeftIdx];
                    } else if (right !== 0 && left === 0) {
                        const skipRightIdx = (left << 4) | (center << 2) | right2;
                        nxt[x] = ruleSkipRight[skipRightIdx];
                    }
                }
            }
            
            return nxt;
        }
        
        function renderState(state, zoom = 5) {
            const display = document.getElementById('caDisplay');
            let html = '';
            
            const cellSize = Math.max(2, zoom);
            const cellStyle = `width: ${cellSize}px; height: ${cellSize}px; margin: 1px;`;
            
            for (let i = 0; i < Math.min(state.length, 1000); i++) {
                const value = state[i];
                html += `<span class="cell cell-${value}" style="${cellStyle}"></span>`;
                if ((i + 1) % 100 === 0) html += '<br>';
            }
            
            display.innerHTML = html;
            display.dataset.lastState = JSON.stringify(state);
        }
        
        function runSimulation() {
            const startTime = performance.now();
            
            const a = parseInt(document.getElementById('inputA').value);
            const b = parseInt(document.getElementById('inputB').value);
            const expected = gcd(a, b);
            
            updateInputDisplay();
            
            let state = encodeInputs(a, b);
            let steps = 0;
            const maxSteps = 1000;
            
            // Initial render
            renderState(state, currentZoom);
            
            // Simulate
            while (steps < maxSteps) {
                if (checkHalting(state)) {
                    const result = decodeResult(state);
                    const endTime = performance.now();
                    
                    // Update results
                    document.getElementById('predicted').textContent = `: ${result}`;
                    document.getElementById('steps').textContent = `: ${steps}`;
                    document.getElementById('width').textContent = `: ${state.length}`;
                    document.getElementById('time').textContent = `: ${(endTime - startTime).toFixed(1)}`;
                    
                    const isCorrect = result === expected;
                    document.getElementById('accuracy').textContent = `: ${isCorrect ? '✓ CORRECT' : '✗ INCORRECT'}`;
                    document.getElementById('accuracy').className = `result-value ${isCorrect ? 'correct' : 'incorrect'}`;
                    
                    return;
                }
                
                state = simulateStep(state);
                steps++;
            }
            
            // Timeout
            const endTime = performance.now();
            document.getElementById('predicted').textContent = ': -10 (timeout)';
            document.getElementById('steps').textContent = `: ${steps}`;
            document.getElementById('time').textContent = `: ${(endTime - startTime).toFixed(1)}`;
            document.getElementById('accuracy').textContent = ': ⏰ TIMEOUT';
            document.getElementById('accuracy').className = 'result-value timeout';
        }
        
        // Initialize
        document.getElementById('inputA').addEventListener('input', updateInputDisplay);
        document.getElementById('inputB').addEventListener('input', updateInputDisplay);
        updateInputDisplay();
        
        // Auto-run on load
        setTimeout(runSimulation, 500);
    </script>
</body>
</html>