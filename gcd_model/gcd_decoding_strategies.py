"""
GCD-Specific Decoding Strategies

This module implements specialized decoding strategies tailored for GCD computation.
The goal is to extract the GCD result from the final cellular automaton state.

Key strategies:
1. Count-based decoding (zeros, ones, markers)
2. Pattern-based decoding (divisor patterns, remainders)
3. Position-based decoding (specific regions, markers)
4. Consensus-based decoding (multiple methods combined)
"""

import numpy as np
from typing import Tuple, List, Dict, Optional

class GCDDecodingStrategies:
    """Collection of GCD-specific decoding strategies."""
    
    @staticmethod
    def decode_zeros_after_last_marker(state: np.ndarray) -> int:
        """
        Strategy 0: Count zeros after the rightmost R marker.
        Classic approach: R 0^gcd pattern.
        """
        N = len(state)
        
        # Find rightmost R marker
        rightmost_r = -1
        for i in range(N-1, -1, -1):
            if state[i] == 2:
                rightmost_r = i
                break
        
        if rightmost_r == -1:
            return -10  # No marker found
        
        # Count consecutive zeros after marker
        zero_count = 0
        for i in range(rightmost_r + 1, N):
            if state[i] == 0:
                zero_count += 1
            else:
                break
        
        return max(1, zero_count)  # GCD is at least 1
    
    @staticmethod
    def decode_zeros_between_markers(state: np.ndarray) -> int:
        """
        Strategy 1: Count zeros between the last two R markers.
        Pattern: ... R 0^gcd R
        """
        N = len(state)
        
        # Find all R markers
        markers = [i for i in range(N) if state[i] == 2]
        
        if len(markers) < 2:
            return -10  # Need at least 2 markers
        
        # Use last two markers
        start_marker = markers[-2]
        end_marker = markers[-1]
        
        # Count zeros between them
        zero_count = 0
        for i in range(start_marker + 1, end_marker):
            if state[i] == 0:
                zero_count += 1
        
        return max(1, zero_count)
    
    @staticmethod
    def decode_ones_after_last_marker(state: np.ndarray) -> int:
        """
        Strategy 2: Count ones after the rightmost R marker.
        Alternative pattern: R 1^gcd
        """
        N = len(state)
        
        # Find rightmost R marker
        rightmost_r = -1
        for i in range(N-1, -1, -1):
            if state[i] == 2:
                rightmost_r = i
                break
        
        if rightmost_r == -1:
            return -10
        
        # Count consecutive ones after marker
        one_count = 0
        for i in range(rightmost_r + 1, N):
            if state[i] == 1:
                one_count += 1
            else:
                break
        
        return max(1, one_count)
    
    @staticmethod
    def decode_marker_count(state: np.ndarray) -> int:
        """
        Strategy 3: Use number of R markers as GCD.
        Pattern: R^gcd (gcd markers total)
        """
        marker_count = np.sum(state == 2)
        return max(1, marker_count)
    
    @staticmethod
    def decode_blue_cell_count(state: np.ndarray) -> int:
        """
        Strategy 4: Count total blue cells (state 3).
        Pattern: 3^gcd somewhere in the tape
        """
        blue_count = np.sum(state == 3)
        return max(1, blue_count)
    
    @staticmethod
    def decode_pattern_length(state: np.ndarray) -> int:
        """
        Strategy 5: Measure length of specific patterns.
        Look for repeating patterns that indicate GCD.
        """
        N = len(state)
        
        # Find the main computation region (after program)
        # Look for first significant pattern after initial program
        start_region = 0
        for i in range(N):
            if state[i] == 3:  # Found separator
                start_region = i + 1
                break
        
        if start_region >= N - 5:
            return -10
        
        # Look for repeating patterns in computation region
        region = state[start_region:start_region + 20]  # Check first 20 cells
        
        # Find pattern length by looking for repetitions
        for pattern_len in range(1, 8):  # Check patterns of length 1-7
            if pattern_len * 3 > len(region):
                continue
                
            pattern = region[:pattern_len]
            is_repeating = True
            
            for rep in range(1, 3):  # Check 2 more repetitions
                start_idx = rep * pattern_len
                end_idx = start_idx + pattern_len
                if end_idx > len(region):
                    is_repeating = False
                    break
                if not np.array_equal(pattern, region[start_idx:end_idx]):
                    is_repeating = False
                    break
            
            if is_repeating:
                return max(1, pattern_len)
        
        return 1  # Default to 1 if no pattern found
    
    @staticmethod
    def decode_divisor_markers(state: np.ndarray) -> int:
        """
        Strategy 6: Look for specific divisor marker patterns.
        Assumes encoding includes divisor hints.
        """
        N = len(state)
        
        # Look for regions with specific divisor patterns
        # Pattern: consecutive 1s indicating divisor
        max_consecutive_ones = 0
        current_ones = 0
        
        for i in range(N):
            if state[i] == 1:
                current_ones += 1
                max_consecutive_ones = max(max_consecutive_ones, current_ones)
            else:
                current_ones = 0
        
        return max(1, max_consecutive_ones)
    
    @staticmethod
    def decode_remainder_pattern(state: np.ndarray) -> int:
        """
        Strategy 7: Analyze remainder patterns for GCD detection.
        Look for patterns that suggest Euclidean algorithm steps.
        """
        N = len(state)
        
        # Find all non-zero regions
        nonzero_regions = []
        current_region = []
        
        for i in range(N):
            if state[i] != 0:
                current_region.append((i, state[i]))
            else:
                if current_region:
                    nonzero_regions.append(current_region)
                    current_region = []
        
        if current_region:
            nonzero_regions.append(current_region)
        
        if not nonzero_regions:
            return 1
        
        # Analyze the last significant region
        last_region = nonzero_regions[-1]
        
        # Count different cell types in last region
        type_counts = {1: 0, 2: 0, 3: 0}
        for pos, cell_type in last_region:
            if cell_type in type_counts:
                type_counts[cell_type] += 1
        
        # Use the most frequent non-marker type as GCD hint
        if type_counts[1] > 0:
            return max(1, type_counts[1])
        elif type_counts[3] > 0:
            return max(1, type_counts[3])
        else:
            return max(1, type_counts[2])
    
    @staticmethod
    def decode_consensus(state: np.ndarray) -> int:
        """
        Strategy 8: Consensus of multiple decoding methods.
        Combines results from multiple strategies for robustness.
        """
        strategies = [
            GCDDecodingStrategies.decode_zeros_after_last_marker,
            GCDDecodingStrategies.decode_zeros_between_markers,
            GCDDecodingStrategies.decode_ones_after_last_marker,
            GCDDecodingStrategies.decode_marker_count,
            GCDDecodingStrategies.decode_pattern_length,
            GCDDecodingStrategies.decode_divisor_markers,
        ]
        
        results = []
        for strategy in strategies:
            try:
                result = strategy(state)
                if result > 0:  # Valid result
                    results.append(result)
            except:
                continue
        
        if not results:
            return 1
        
        # Use median as consensus (robust to outliers)
        results.sort()
        n = len(results)
        if n % 2 == 1:
            return results[n // 2]
        else:
            return (results[n // 2 - 1] + results[n // 2]) // 2
    
    @staticmethod
    def decode_adaptive(state: np.ndarray, encoding_type: int) -> int:
        """
        Strategy 9: Adaptive decoding based on encoding type.
        Chooses decoding strategy based on how input was encoded.
        """
        if encoding_type == 0:  # Standard separated
            return GCDDecodingStrategies.decode_zeros_after_last_marker(state)
        elif encoding_type == 1:  # Commutative overlap
            return GCDDecodingStrategies.decode_zeros_between_markers(state)
        elif encoding_type == 2:  # Factorization hint
            return GCDDecodingStrategies.decode_divisor_markers(state)
        elif encoding_type == 3:  # Binary representation
            return GCDDecodingStrategies.decode_pattern_length(state)
        elif encoding_type == 4:  # Divisor pattern
            return GCDDecodingStrategies.decode_remainder_pattern(state)
        elif encoding_type == 5:  # Symmetric layout
            return GCDDecodingStrategies.decode_consensus(state)
        else:
            return GCDDecodingStrategies.decode_consensus(state)

def test_decoding_strategies():
    """Test all decoding strategies with sample states."""
    print("🧪 TESTING GCD DECODING STRATEGIES")
    print("=" * 50)
    
    # Create test states representing different GCD results
    test_states = {
        "GCD=1": np.array([1, 1, 3, 3, 2, 0, 2, 0, 0, 0], dtype=np.uint8),
        "GCD=2": np.array([1, 1, 3, 3, 2, 0, 0, 2, 0, 0], dtype=np.uint8),
        "GCD=3": np.array([1, 1, 3, 3, 2, 0, 0, 0, 2, 0], dtype=np.uint8),
        "GCD=4": np.array([1, 1, 3, 3, 2, 2, 2, 2, 0, 0], dtype=np.uint8),
    }
    
    strategies = [
        ("Zeros After Last Marker", GCDDecodingStrategies.decode_zeros_after_last_marker),
        ("Zeros Between Markers", GCDDecodingStrategies.decode_zeros_between_markers),
        ("Ones After Last Marker", GCDDecodingStrategies.decode_ones_after_last_marker),
        ("Marker Count", GCDDecodingStrategies.decode_marker_count),
        ("Pattern Length", GCDDecodingStrategies.decode_pattern_length),
        ("Divisor Markers", GCDDecodingStrategies.decode_divisor_markers),
        ("Consensus", GCDDecodingStrategies.decode_consensus),
    ]
    
    for state_name, state in test_states.items():
        print(f"\nTesting {state_name} (state: {state}):")
        for strategy_name, strategy_func in strategies:
            try:
                result = strategy_func(state)
                print(f"  {strategy_name:25}: {result}")
            except Exception as e:
                print(f"  {strategy_name:25}: ERROR - {e}")
    
    print("\n✅ Decoding strategies test completed")

if __name__ == "__main__":
    test_decoding_strategies()
