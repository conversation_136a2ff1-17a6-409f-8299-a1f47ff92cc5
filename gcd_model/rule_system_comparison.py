"""
Controlled Comparison Framework for Rule Systems

This module provides a framework to compare the original 4-rule system with the 
enhanced 8-rule system under identical conditions to measure information propagation efficiency.

Key Comparisons:
1. Same initial conditions
2. Same computational tasks
3. Same evaluation metrics
4. Statistical significance testing
"""

import numpy as np
import sys
import os
from typing import List, Tuple, Dict, Any, Optional
from dataclasses import dataclass
import pickle
import time
from tqdm import tqdm

# Add em43_python to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'em43_python'))

from enhanced_nonlocal_rules import EnhancedNonlocalRules, create_random_enhanced_rules
from information_propagation_metrics import InformationPropagationAnalyzer, PropagationMetrics
from em43_numba import _sanitize_rule

@dataclass
class ComparisonResult:
    """Results from comparing two rule systems."""
    rule_4_metrics: PropagationMetrics
    rule_8_metrics: PropagationMetrics
    improvement_factor: float
    statistical_significance: float
    test_scenario: str

class OriginalEM43Rules:
    """Original 4-rule EM43 system for comparison."""
    
    def __init__(self, rule: np.ndarray):
        """Initialize with 64-entry rule array."""
        assert len(rule) == 64, f"Original rule must have 64 entries, got {len(rule)}"
        self.rule = rule.copy()
    
    def apply_rules(self, state: np.ndarray) -> np.ndarray:
        """Apply original 4-rule system."""
        N = len(state)
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(1, N-1):
            # Original local rule: (left, center, right)
            left = state[x-1] if x > 0 else 0
            center = state[x]
            right = state[x+1] if x < N-1 else 0
            
            # Calculate rule index
            rule_idx = (left << 4) | (center << 2) | right
            nxt[x] = self.rule[rule_idx]
        
        return nxt

class RuleSystemComparator:
    """Framework for comparing 4-rule vs 8-rule systems."""
    
    def __init__(self):
        self.analyzer = InformationPropagationAnalyzer()
        self.results = []
    
    def create_matched_rule_pairs(self, num_pairs: int = 10, seed: int = 42) -> List[Tuple[np.ndarray, np.ndarray]]:
        """
        Create pairs of rules where the 8-rule system includes the 4-rule as its first component.
        
        Args:
            num_pairs: Number of rule pairs to generate
            seed: Random seed for reproducibility
            
        Returns:
            List of (4-rule, 8-rule) pairs
        """
        rng = np.random.default_rng(seed)
        pairs = []
        
        for _ in range(num_pairs):
            # Create base 4-rule
            base_rule = rng.integers(0, 4, 64, dtype=np.uint8)
            base_rule = _sanitize_rule(base_rule)
            
            # Create 8-rule that includes the 4-rule as first component
            enhanced_rule = np.zeros(512, dtype=np.uint8)
            enhanced_rule[0:64] = base_rule  # First 64 entries = original rule
            
            # Fill remaining 448 entries with random rules
            for i in range(1, 8):  # Rule types 1-7
                start_idx = i * 64
                end_idx = (i + 1) * 64
                enhanced_rule[start_idx:end_idx] = rng.integers(0, 4, 64, dtype=np.uint8)
            
            pairs.append((base_rule, enhanced_rule))
        
        return pairs
    
    def create_propagation_test_scenarios(self) -> List[Dict[str, Any]]:
        """
        Create standardized test scenarios for information propagation.
        
        Returns:
            List of test scenario configurations
        """
        scenarios = [
            {
                'name': 'Simple Propagation',
                'description': 'Single information source, no obstacles',
                'N': 100,
                'source_pos': 50,
                'barrier_positions': [],
                'max_steps': 30
            },
            {
                'name': 'Single Barrier',
                'description': 'Information must pass through one barrier',
                'N': 100,
                'source_pos': 30,
                'barrier_positions': [50],
                'max_steps': 40
            },
            {
                'name': 'Multiple Barriers',
                'description': 'Information must navigate multiple obstacles',
                'N': 100,
                'source_pos': 20,
                'barrier_positions': [35, 50, 65],
                'max_steps': 50
            },
            {
                'name': 'Dense Blocking',
                'description': 'High density of blocking cells',
                'N': 100,
                'source_pos': 25,
                'barrier_positions': list(range(40, 60, 2)),  # Every other cell blocked
                'max_steps': 60
            },
            {
                'name': 'Long Range',
                'description': 'Test long-distance propagation',
                'N': 150,
                'source_pos': 25,
                'barrier_positions': [50, 75, 100],
                'max_steps': 80
            }
        ]
        
        return scenarios
    
    def simulate_evolution(self, rule_system, initial_state: np.ndarray, max_steps: int) -> List[np.ndarray]:
        """
        Simulate cellular automaton evolution.
        
        Args:
            rule_system: Either OriginalEM43Rules or EnhancedNonlocalRules
            initial_state: Starting state
            max_steps: Maximum evolution steps
            
        Returns:
            List of states over time
        """
        states = [initial_state.copy()]
        current_state = initial_state.copy()
        
        for step in range(max_steps):
            next_state = rule_system.apply_rules(current_state)
            states.append(next_state)
            current_state = next_state
            
            # Early stopping if state becomes all zeros
            if np.sum(next_state) == 0:
                break
        
        return states
    
    def compare_rule_systems(self, rule_4: np.ndarray, rule_8: np.ndarray, 
                           scenarios: List[Dict[str, Any]]) -> List[ComparisonResult]:
        """
        Compare 4-rule vs 8-rule systems across multiple scenarios.
        
        Args:
            rule_4: 64-entry rule for original system
            rule_8: 512-entry rule for enhanced system
            scenarios: List of test scenarios
            
        Returns:
            List of comparison results
        """
        # Initialize rule systems
        original_system = OriginalEM43Rules(rule_4)
        enhanced_system = EnhancedNonlocalRules(rule_8)
        
        results = []
        
        for scenario in scenarios:
            # Create initial state
            initial_state = self.analyzer.create_test_scenario(
                scenario['N'], 
                scenario['source_pos'], 
                scenario['barrier_positions']
            )
            
            # Simulate both systems
            states_4 = self.simulate_evolution(original_system, initial_state, scenario['max_steps'])
            states_8 = self.simulate_evolution(enhanced_system, initial_state, scenario['max_steps'])
            
            # Analyze propagation for both systems
            target_positions = [
                max(0, scenario['source_pos'] - 20),
                max(0, scenario['source_pos'] - 40),
                min(scenario['N']-1, scenario['source_pos'] + 20),
                min(scenario['N']-1, scenario['source_pos'] + 40)
            ]
            
            metrics_4 = self.analyzer.analyze_propagation(
                states_4, scenario['source_pos'], target_positions, scenario['barrier_positions']
            )
            
            metrics_8 = self.analyzer.analyze_propagation(
                states_8, scenario['source_pos'], target_positions, scenario['barrier_positions']
            )
            
            # Calculate improvement factor
            if metrics_4.efficiency_score > 0:
                improvement_factor = metrics_8.efficiency_score / metrics_4.efficiency_score
            else:
                improvement_factor = float('inf') if metrics_8.efficiency_score > 0 else 1.0
            
            # Create comparison result
            result = ComparisonResult(
                rule_4_metrics=metrics_4,
                rule_8_metrics=metrics_8,
                improvement_factor=improvement_factor,
                statistical_significance=0.0,  # Will be calculated in batch analysis
                test_scenario=scenario['name']
            )
            
            results.append(result)
        
        return results
    
    def run_comprehensive_comparison(self, num_rule_pairs: int = 20, 
                                   save_results: bool = True) -> Dict[str, Any]:
        """
        Run comprehensive comparison across multiple rule pairs and scenarios.
        
        Args:
            num_rule_pairs: Number of random rule pairs to test
            save_results: Whether to save results to file
            
        Returns:
            Comprehensive analysis results
        """
        print("🔬 COMPREHENSIVE RULE SYSTEM COMPARISON")
        print("=" * 60)
        print(f"Testing {num_rule_pairs} rule pairs across multiple scenarios...")
        
        # Generate rule pairs
        rule_pairs = self.create_matched_rule_pairs(num_rule_pairs)
        scenarios = self.create_propagation_test_scenarios()
        
        all_results = []
        scenario_summaries = {scenario['name']: [] for scenario in scenarios}
        
        start_time = time.time()
        
        # Test each rule pair
        for pair_idx, (rule_4, rule_8) in enumerate(tqdm(rule_pairs, desc="Testing rule pairs")):
            pair_results = self.compare_rule_systems(rule_4, rule_8, scenarios)
            all_results.extend(pair_results)
            
            # Group by scenario
            for result in pair_results:
                scenario_summaries[result.test_scenario].append(result)
        
        # Calculate statistics
        total_time = time.time() - start_time
        
        # Analyze results by scenario
        scenario_analysis = {}
        for scenario_name, results in scenario_summaries.items():
            improvements = [r.improvement_factor for r in results]
            efficiency_4 = [r.rule_4_metrics.efficiency_score for r in results]
            efficiency_8 = [r.rule_8_metrics.efficiency_score for r in results]
            
            scenario_analysis[scenario_name] = {
                'mean_improvement': np.mean(improvements),
                'std_improvement': np.std(improvements),
                'median_improvement': np.median(improvements),
                'mean_efficiency_4': np.mean(efficiency_4),
                'mean_efficiency_8': np.mean(efficiency_8),
                'improvement_rate': np.mean([1 if imp > 1.0 else 0 for imp in improvements]),
                'significant_improvement_rate': np.mean([1 if imp > 1.2 else 0 for imp in improvements])
            }
        
        # Overall statistics
        all_improvements = [r.improvement_factor for r in all_results if r.improvement_factor != float('inf')]
        overall_stats = {
            'total_tests': len(all_results),
            'rule_pairs_tested': num_rule_pairs,
            'scenarios_tested': len(scenarios),
            'mean_improvement': np.mean(all_improvements),
            'median_improvement': np.median(all_improvements),
            'improvement_rate': np.mean([1 if imp > 1.0 else 0 for imp in all_improvements]),
            'significant_improvement_rate': np.mean([1 if imp > 1.2 else 0 for imp in all_improvements]),
            'testing_time': total_time
        }
        
        # Compile final results
        final_results = {
            'overall_stats': overall_stats,
            'scenario_analysis': scenario_analysis,
            'detailed_results': all_results,
            'rule_pairs': rule_pairs,
            'scenarios': scenarios
        }
        
        # Print summary
        self.print_comparison_summary(final_results)
        
        # Save results
        if save_results:
            with open('rule_system_comparison_results.pkl', 'wb') as f:
                pickle.dump(final_results, f)
            print(f"\n💾 Results saved to: rule_system_comparison_results.pkl")
        
        return final_results
    
    def print_comparison_summary(self, results: Dict[str, Any]):
        """Print a summary of comparison results."""
        overall = results['overall_stats']
        scenarios = results['scenario_analysis']
        
        print(f"\n📊 COMPARISON RESULTS SUMMARY")
        print("=" * 60)
        
        print(f"Overall Statistics:")
        print(f"  Total tests: {overall['total_tests']}")
        print(f"  Rule pairs: {overall['rule_pairs_tested']}")
        print(f"  Scenarios: {overall['scenarios_tested']}")
        print(f"  Mean improvement: {overall['mean_improvement']:.2f}x")
        print(f"  Median improvement: {overall['median_improvement']:.2f}x")
        print(f"  Improvement rate: {overall['improvement_rate']:.1%}")
        print(f"  Significant improvement rate: {overall['significant_improvement_rate']:.1%}")
        print(f"  Testing time: {overall['testing_time']:.1f} seconds")
        
        print(f"\nScenario-by-Scenario Analysis:")
        for scenario_name, stats in scenarios.items():
            print(f"  {scenario_name}:")
            print(f"    Mean improvement: {stats['mean_improvement']:.2f}x")
            print(f"    4-rule efficiency: {stats['mean_efficiency_4']:.1f}")
            print(f"    8-rule efficiency: {stats['mean_efficiency_8']:.1f}")
            print(f"    Improvement rate: {stats['improvement_rate']:.1%}")

def test_rule_comparison():
    """Test the rule system comparison framework."""
    print("🧪 TESTING RULE SYSTEM COMPARISON")
    print("=" * 50)
    
    comparator = RuleSystemComparator()
    
    # Quick test with 2 rule pairs
    results = comparator.run_comprehensive_comparison(num_rule_pairs=2, save_results=False)
    
    print("\n✅ Rule system comparison test completed")

if __name__ == "__main__":
    test_rule_comparison()
