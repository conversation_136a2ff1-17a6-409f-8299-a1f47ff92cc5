"""
Analyze trained GCD models - test commutativity, generalization, and create visualizations
"""

import pickle
import numpy as np
from commutative_nonlocal_gcd_trainer import gcd, evaluate_gcd_model
from adaptive_nonlocal_training import AdaptiveEM43

def test_commutativity(model, test_cases):
    """Test if GCD(a,b) = GCD(b,a) for the model"""
    print("🔄 TESTING COMMUTATIVITY")
    print("=" * 40)
    
    commutative_violations = 0
    total_tests = 0
    
    for a, b in test_cases:
        if a != b:  # Only test when a != b
            try:
                result_ab = model.simulate(a, b, max_steps=500)
                result_ba = model.simulate(b, a, max_steps=500)
                expected = gcd(a, b)
                
                is_commutative = (result_ab == result_ba)
                is_correct_ab = (result_ab == expected)
                is_correct_ba = (result_ba == expected)
                
                if not is_commutative:
                    commutative_violations += 1
                    print(f"  ❌ GCD({a},{b})={result_ab}, GCD({b},{a})={result_ba} (expected {expected})")
                elif is_correct_ab and is_correct_ba:
                    print(f"  ✅ GCD({a},{b})=GCD({b},{a})={result_ab} (correct)")
                else:
                    print(f"  🔶 GCD({a},{b})=GCD({b},{a})={result_ab} (commutative but wrong, expected {expected})")
                
                total_tests += 1
                
            except Exception as e:
                print(f"  ⚠️ Error testing GCD({a},{b}): {e}")
    
    commutativity_rate = (total_tests - commutative_violations) / total_tests if total_tests > 0 else 0
    print(f"\nCommutativity rate: {commutativity_rate:.3f} ({total_tests - commutative_violations}/{total_tests})")
    return commutativity_rate

def test_generalization(model):
    """Test model on larger numbers to see generalization"""
    print("\n🚀 TESTING GENERALIZATION")
    print("=" * 40)
    
    # Test cases with larger numbers
    large_test_cases = [
        (8, 12),   # GCD = 4
        (15, 25),  # GCD = 5  
        (18, 24),  # GCD = 6
        (20, 30),  # GCD = 10
        (21, 35),  # GCD = 7
        (16, 24),  # GCD = 8
        (9, 15),   # GCD = 3
        (14, 35),  # GCD = 7
        (12, 18),  # GCD = 6
        (10, 25),  # GCD = 5
    ]
    
    correct = 0
    convergent = 0
    
    for a, b in large_test_cases:
        expected = gcd(a, b)
        try:
            result = model.simulate(a, b, max_steps=1000)
            is_correct = (result == expected)
            is_convergent = (result != -10)
            
            if is_correct:
                correct += 1
                status = "✅"
            elif is_convergent:
                status = "❌"
            else:
                status = "⏰"
            
            if is_convergent:
                convergent += 1
                
            print(f"  GCD({a:2d},{b:2d})={expected:2d} → {result:2d} {status}")
            
        except Exception as e:
            print(f"  GCD({a},{b}): ERROR - {e}")
    
    accuracy = correct / len(large_test_cases)
    convergence_rate = convergent / len(large_test_cases)
    
    print(f"\nGeneralization Results:")
    print(f"  Accuracy: {accuracy:.3f} ({correct}/{len(large_test_cases)})")
    print(f"  Convergence: {convergence_rate:.3f} ({convergent}/{len(large_test_cases)})")
    
    return accuracy, convergence_rate

def analyze_encoding_strategy(model):
    """Analyze how the model encodes different inputs"""
    print("\n🔍 ANALYZING ENCODING STRATEGY")
    print("=" * 40)
    
    print(f"Encoding parameters: {model.encoding_params}")
    print(f"Decoding parameters: {model.decoding_params}")
    print(f"Halting parameters: {model.halting_params}")
    
    # Test encoding for different input pairs
    test_pairs = [(1, 1), (2, 3), (3, 2), (4, 6), (6, 4)]
    
    for a, b in test_pairs:
        state = model.encode_inputs(a, b, N=100)
        nonzero_positions = [(i, state[i]) for i in range(len(state)) if state[i] != 0]
        print(f"  GCD({a},{b}): {nonzero_positions[:15]}...")  # Show first 15 positions

def trace_computation_steps(model, a, b, max_steps=10):
    """Trace the computation steps for a specific GCD calculation"""
    print(f"\n🔬 TRACING GCD({a},{b}) COMPUTATION")
    print("=" * 40)
    
    N = 150
    state = model.encode_inputs(a, b, N)
    expected = gcd(a, b)
    
    print(f"Expected result: {expected}")
    print(f"Initial encoding: {[i for i, v in enumerate(state) if v != 0]}")
    
    # Extract rule components
    rule_local = model.rule[0:64]
    rule_skip_left = model.rule[64:128]
    rule_skip_right = model.rule[128:192]
    rule_long_range = model.rule[192:256]
    
    for step in range(max_steps):
        print(f"\nStep {step}:")
        
        # Show current state (non-zero positions)
        nonzero = [(i, state[i]) for i in range(N) if state[i] != 0]
        print(f"  State: {nonzero[:20]}...")  # Show first 20 non-zero positions
        
        # Check halting
        if model.check_halting(state):
            result = model.decode_result(state)
            print(f"  HALTED: Result = {result} {'✅' if result == expected else '❌'}")
            return result
        
        # Apply one step
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(1, N - 1):
            left = state[x-1] if x > 0 else 0
            center = state[x]
            right = state[x+1] if x < N-1 else 0
            local_idx = (left << 4) | (center << 2) | right
            
            # Default to local rule
            nxt[x] = rule_local[local_idx]
            
            # Apply nonlocal rules
            if x >= 2 and x < N - 2:
                left2 = state[x-2]
                right2 = state[x+2]
                
                if center != 0:
                    long_range_idx = (left2 << 4) | (center << 2) | right2
                    nxt[x] = rule_long_range[long_range_idx]
                elif left != 0 and right == 0:
                    skip_left_idx = (left2 << 4) | (center << 2) | right
                    nxt[x] = rule_skip_left[skip_left_idx]
                elif right != 0 and left == 0:
                    skip_right_idx = (left << 4) | (center << 2) | right2
                    nxt[x] = rule_skip_right[skip_right_idx]
        
        state = nxt
    
    print(f"  Did not halt within {max_steps} steps")
    return -10

def comprehensive_analysis(model_path):
    """Run comprehensive analysis of a trained GCD model"""
    print("🎯 COMPREHENSIVE GCD MODEL ANALYSIS")
    print("=" * 60)
    
    # Load model
    try:
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        model = AdaptiveEM43(
            model_data['rule'],
            model_data['prog'],
            model_data['encoding_params'],
            model_data['decoding_params'],
            model_data['halting_params']
        )
        
        print(f"✅ Model loaded from {model_path}")
        print(f"Training fitness: {model_data.get('fitness', 'N/A')}")
        print(f"Training accuracy: {model_data.get('accuracy', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return
    
    # Basic evaluation
    small_cases = [(a, b) for a in range(1, 6) for b in range(1, 6)]
    accuracy, convergence, _ = evaluate_gcd_model(model, small_cases)
    print(f"\nBasic evaluation (1-5 range):")
    print(f"  Accuracy: {accuracy:.3f}")
    print(f"  Convergence: {convergence:.3f}")
    
    # Test commutativity
    comm_cases = [(1, 2), (2, 3), (3, 4), (4, 6), (2, 6), (3, 9)]
    commutativity_rate = test_commutativity(model, comm_cases)
    
    # Test generalization
    gen_accuracy, gen_convergence = test_generalization(model)
    
    # Analyze encoding
    analyze_encoding_strategy(model)
    
    # Trace specific computations
    trace_computation_steps(model, 6, 9)  # GCD = 3
    trace_computation_steps(model, 8, 12) # GCD = 4
    
    print(f"\n📊 SUMMARY:")
    print(f"  Basic accuracy: {accuracy:.3f}")
    print(f"  Commutativity: {commutativity_rate:.3f}")
    print(f"  Generalization accuracy: {gen_accuracy:.3f}")
    print(f"  Overall convergence: {min(convergence, gen_convergence):.3f}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        model_path = sys.argv[1]
    else:
        model_path = 'best_gcd_model.pkl'
    
    comprehensive_analysis(model_path)
