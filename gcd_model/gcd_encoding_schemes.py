"""
GCD-Specific Encoding Schemes

This module implements specialized input encoding strategies optimized for GCD computation.
Key requirements:
1. Commutativity: GCD(a,b) = GCD(b,a) 
2. Clear separation and identification of inputs
3. Support for different number ranges
4. Efficient use of tape space
"""

import numpy as np
from typing import Tuple, List, Dict

class GCDEncodingSchemes:
    """Collection of GCD-specific encoding strategies."""
    
    @staticmethod
    def encode_standard_separated(a: int, b: int, prog_len: int, N: int) -> np.ndarray:
        """
        Standard encoding: PROG BB 0^a R 0^b R
        Clear separation, works well for small numbers.
        """
        state = np.zeros(N, dtype=np.uint8)
        
        # Program (placeholder)
        for i in range(min(prog_len, N)):
            state[i] = 1  # Use 1s for program
        
        # Separator BB
        if prog_len + 1 < N:
            state[prog_len] = 3
            state[prog_len + 1] = 3
        
        base_pos = prog_len + 2
        
        # First input: 0^a
        for i in range(a):
            if base_pos + i < N:
                state[base_pos + i] = 0
        
        # First marker R
        r1_pos = base_pos + a
        if r1_pos < N:
            state[r1_pos] = 2
        
        # Second input: 0^b  
        for i in range(b):
            pos = r1_pos + 1 + i
            if pos < N:
                state[pos] = 0
        
        # Second marker R
        r2_pos = r1_pos + 1 + b
        if r2_pos < N:
            state[r2_pos] = 2
        
        return state
    
    @staticmethod
    def encode_commutative_overlap(a: int, b: int, prog_len: int, N: int) -> np.ndarray:
        """
        Commutative overlap: PROG BB 0^max(a,b) R
        When a=b, they occupy same space. Leverages commutativity.
        """
        state = np.zeros(N, dtype=np.uint8)
        
        # Program
        for i in range(min(prog_len, N)):
            state[i] = 1
        
        # Separator BB
        if prog_len + 1 < N:
            state[prog_len] = 3
            state[prog_len + 1] = 3
        
        base_pos = prog_len + 2
        
        # Overlapped input: 0^max(a,b)
        max_input = max(a, b)
        for i in range(max_input):
            if base_pos + i < N:
                state[base_pos + i] = 0
        
        # Single marker R
        r_pos = base_pos + max_input
        if r_pos < N:
            state[r_pos] = 2
        
        return state
    
    @staticmethod
    def encode_factorization_hint(a: int, b: int, prog_len: int, N: int) -> np.ndarray:
        """
        Factorization hint: PROG BB 1^a R 1^b R 0^(a*b) R
        Includes product to help with factorization-based GCD algorithms.
        """
        state = np.zeros(N, dtype=np.uint8)
        
        # Program
        for i in range(min(prog_len, N)):
            state[i] = 1
        
        # Separator BB
        if prog_len + 1 < N:
            state[prog_len] = 3
            state[prog_len + 1] = 3
        
        base_pos = prog_len + 2
        
        # First input as 1s: 1^a
        for i in range(a):
            if base_pos + i < N:
                state[base_pos + i] = 1
        
        r1_pos = base_pos + a
        if r1_pos < N:
            state[r1_pos] = 2
        
        # Second input as 1s: 1^b
        for i in range(b):
            pos = r1_pos + 1 + i
            if pos < N:
                state[pos] = 1
        
        r2_pos = r1_pos + 1 + b
        if r2_pos < N:
            state[r2_pos] = 2
        
        # Product hint: 0^(a*b)
        product = a * b
        for i in range(min(product, N - r2_pos - 2)):  # Limit to available space
            pos = r2_pos + 1 + i
            if pos < N:
                state[pos] = 0
        
        # Final marker
        final_r = r2_pos + 1 + min(product, N - r2_pos - 2)
        if final_r < N:
            state[final_r] = 2
        
        return state
    
    @staticmethod
    def encode_binary_representation(a: int, b: int, prog_len: int, N: int) -> np.ndarray:
        """
        Binary encoding: PROG BB [binary(a)] R [binary(b)] R
        More compact for larger numbers.
        """
        state = np.zeros(N, dtype=np.uint8)
        
        # Program
        for i in range(min(prog_len, N)):
            state[i] = 1
        
        # Separator BB
        if prog_len + 1 < N:
            state[prog_len] = 3
            state[prog_len + 1] = 3
        
        base_pos = prog_len + 2
        
        # Binary representation of a (using 1s and 0s)
        a_binary = bin(a)[2:]  # Remove '0b' prefix
        for i, bit in enumerate(a_binary):
            if base_pos + i < N:
                state[base_pos + i] = int(bit)
        
        r1_pos = base_pos + len(a_binary)
        if r1_pos < N:
            state[r1_pos] = 2
        
        # Binary representation of b
        b_binary = bin(b)[2:]
        for i, bit in enumerate(b_binary):
            pos = r1_pos + 1 + i
            if pos < N:
                state[pos] = int(bit)
        
        r2_pos = r1_pos + 1 + len(b_binary)
        if r2_pos < N:
            state[r2_pos] = 2
        
        return state
    
    @staticmethod
    def encode_divisor_pattern(a: int, b: int, prog_len: int, N: int) -> np.ndarray:
        """
        Divisor pattern: PROG BB 0^a R 0^b R [divisor_hints] R
        Includes hints about potential common divisors.
        """
        state = np.zeros(N, dtype=np.uint8)
        
        # Program
        for i in range(min(prog_len, N)):
            state[i] = 1
        
        # Separator BB
        if prog_len + 1 < N:
            state[prog_len] = 3
            state[prog_len + 1] = 3
        
        base_pos = prog_len + 2
        
        # Standard encoding first
        for i in range(a):
            if base_pos + i < N:
                state[base_pos + i] = 0
        
        r1_pos = base_pos + a
        if r1_pos < N:
            state[r1_pos] = 2
        
        for i in range(b):
            pos = r1_pos + 1 + i
            if pos < N:
                state[pos] = 0
        
        r2_pos = r1_pos + 1 + b
        if r2_pos < N:
            state[r2_pos] = 2
        
        # Add divisor hints (small common divisors)
        hint_pos = r2_pos + 1
        for d in [2, 3, 5]:  # Common small divisors
            if a % d == 0 and b % d == 0 and hint_pos < N:
                state[hint_pos] = 1  # Mark potential divisor
                hint_pos += 1
        
        if hint_pos < N:
            state[hint_pos] = 2  # Final marker
        
        return state
    
    @staticmethod
    def encode_symmetric_layout(a: int, b: int, prog_len: int, N: int) -> np.ndarray:
        """
        Symmetric layout: PROG BB 0^a R 0^b R 0^b R 0^a R
        Symmetric pattern to emphasize commutativity.
        """
        state = np.zeros(N, dtype=np.uint8)
        
        # Program
        for i in range(min(prog_len, N)):
            state[i] = 1
        
        # Separator BB
        if prog_len + 1 < N:
            state[prog_len] = 3
            state[prog_len + 1] = 3
        
        base_pos = prog_len + 2
        
        # First: 0^a
        for i in range(a):
            if base_pos + i < N:
                state[base_pos + i] = 0
        
        r1_pos = base_pos + a
        if r1_pos < N:
            state[r1_pos] = 2
        
        # Second: 0^b
        for i in range(b):
            pos = r1_pos + 1 + i
            if pos < N:
                state[pos] = 0
        
        r2_pos = r1_pos + 1 + b
        if r2_pos < N:
            state[r2_pos] = 2
        
        # Third: 0^b (symmetric)
        for i in range(b):
            pos = r2_pos + 1 + i
            if pos < N:
                state[pos] = 0
        
        r3_pos = r2_pos + 1 + b
        if r3_pos < N:
            state[r3_pos] = 2
        
        # Fourth: 0^a (symmetric)
        for i in range(a):
            pos = r3_pos + 1 + i
            if pos < N:
                state[pos] = 0
        
        r4_pos = r3_pos + 1 + a
        if r4_pos < N:
            state[r4_pos] = 2
        
        return state

def test_encoding_schemes():
    """Test all encoding schemes with sample inputs."""
    print("🧪 TESTING GCD ENCODING SCHEMES")
    print("=" * 50)
    
    test_cases = [(3, 6), (4, 4), (5, 7), (8, 12)]
    prog_len = 5
    N = 100
    
    schemes = [
        ("Standard Separated", GCDEncodingSchemes.encode_standard_separated),
        ("Commutative Overlap", GCDEncodingSchemes.encode_commutative_overlap),
        ("Factorization Hint", GCDEncodingSchemes.encode_factorization_hint),
        ("Binary Representation", GCDEncodingSchemes.encode_binary_representation),
        ("Divisor Pattern", GCDEncodingSchemes.encode_divisor_pattern),
        ("Symmetric Layout", GCDEncodingSchemes.encode_symmetric_layout),
    ]
    
    for a, b in test_cases:
        print(f"\nTesting GCD({a}, {b}):")
        for name, scheme_func in schemes:
            state = scheme_func(a, b, prog_len, N)
            nonzero = [(i, state[i]) for i in range(N) if state[i] != 0]
            print(f"  {name:20}: {nonzero[:15]}...")
    
    print("\n✅ Encoding schemes test completed")

if __name__ == "__main__":
    test_encoding_schemes()
