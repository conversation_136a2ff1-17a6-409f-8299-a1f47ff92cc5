"""
Full-Scale Enhanced GCD Trainer

This implements the complete full-scale training pipeline with:
1. Refined 10-phase curriculum
2. Large populations (800-3000)
3. Extended generations (150-700)
4. Enhanced fitness functions with anti-bias measures
5. Advanced monitoring and adaptive adjustments
"""

import numpy as np
import sys
import os
import pickle
import time
from typing import List, Tuple, Dict, Any, Optional
from tqdm import tqdm

# Add em43_python to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'em43_python'))

# Import enhanced components
from enhanced_nonlocal_rules import EnhancedNonlocalRules, create_random_enhanced_rules
from gcd_encoding_schemes import GCDEncodingSchemes
from gcd_decoding_strategies import GCDDecodingStrategies
from refined_curriculum_framework import RefinedGCDCurriculumFramework, gcd
from enhanced_genetic_algorithm import EnhancedGeneticAlgorithm

# Import base EM43 components
from em43_numba import _sanitize_programme, _sanitize_rule

class FullScaleEnhancedGCDModel:
    """Enhanced EM43 model with anti-bias measures for full-scale training."""
    
    def __init__(self, rule: np.ndarray, prog: np.ndarray, 
                 encoding_params: Dict, decoding_params: Dict, halting_params: Dict):
        self.rule = rule.copy()
        self.prog = prog.copy()
        self.encoding_params = encoding_params.copy()
        self.decoding_params = decoding_params.copy()
        self.halting_params = halting_params.copy()
        
        # Initialize enhanced rule system
        self.nonlocal_rules = EnhancedNonlocalRules(self.rule)
    
    def encode_inputs(self, a: int, b: int, N: int = 150) -> np.ndarray:
        """Encode inputs using selected encoding scheme."""
        encoding_type = self.encoding_params.get('input_encoding', 0)
        prog_len = len(self.prog)
        
        if encoding_type == 0:
            return GCDEncodingSchemes.encode_standard_separated(a, b, prog_len, N)
        elif encoding_type == 1:
            return GCDEncodingSchemes.encode_commutative_overlap(a, b, prog_len, N)
        elif encoding_type == 2:
            return GCDEncodingSchemes.encode_factorization_hint(a, b, prog_len, N)
        elif encoding_type == 3:
            return GCDEncodingSchemes.encode_binary_representation(a, b, prog_len, N)
        elif encoding_type == 4:
            return GCDEncodingSchemes.encode_divisor_pattern(a, b, prog_len, N)
        elif encoding_type == 5:
            return GCDEncodingSchemes.encode_symmetric_layout(a, b, prog_len, N)
        else:
            return GCDEncodingSchemes.encode_standard_separated(a, b, prog_len, N)
    
    def check_halting(self, state: np.ndarray) -> bool:
        """Enhanced halting condition with stability checks."""
        N = len(state)
        
        # Count cell types
        counts = [0, 0, 0, 0]
        for x in state:
            counts[x] += 1
        
        blank, prog, red, blue = counts
        nonzero = prog + red + blue
        
        if nonzero == 0:
            return False
        
        halting_type = self.halting_params.get('condition_type', 0)
        threshold = self.halting_params.get('threshold', 50) / 100.0
        
        # Enhanced halting conditions
        if halting_type == 0:  # Blue ratio with minimum requirement
            return blue / nonzero >= threshold and blue >= 2
        elif halting_type == 1:  # Stability-based
            return prog / nonzero <= (1.0 - threshold) and blue >= 1
        elif halting_type == 2:  # Combined blue + red with balance
            marker_ratio = (blue + red) / nonzero
            return marker_ratio >= threshold and blue >= 1 and red >= 1
        elif halting_type == 3:  # Absolute blue count with ratio check
            return blue >= int(threshold * 15) and blue / nonzero >= 0.1
        else:  # Enhanced stability with multiple criteria
            blue_ok = blue / nonzero >= threshold * 0.6
            stable_ok = prog / nonzero <= 0.5
            marker_ok = blue >= 1
            return blue_ok and stable_ok and marker_ok
    
    def decode_result(self, state: np.ndarray) -> int:
        """Enhanced decoding with anti-bias measures."""
        decoding_method = self.decoding_params.get('method', 0)
        encoding_type = self.encoding_params.get('input_encoding', 0)
        
        # Apply anti-bias filtering
        raw_result = self._apply_decoding_method(state, decoding_method, encoding_type)
        
        # Anti-bias measures: prevent trivial solutions
        if raw_result <= 0:
            return 1  # Minimum GCD is 1
        
        # Limit maximum result to prevent runaway values
        if raw_result > 50:
            return min(raw_result, 20)  # Cap at reasonable value
        
        return raw_result
    
    def _apply_decoding_method(self, state: np.ndarray, method: int, encoding_type: int) -> int:
        """Apply specific decoding method."""
        if method == 0:
            return GCDDecodingStrategies.decode_zeros_after_last_marker(state)
        elif method == 1:
            return GCDDecodingStrategies.decode_zeros_between_markers(state)
        elif method == 2:
            return GCDDecodingStrategies.decode_ones_after_last_marker(state)
        elif method == 3:
            return GCDDecodingStrategies.decode_marker_count(state)
        elif method == 4:
            return GCDDecodingStrategies.decode_blue_cell_count(state)
        elif method == 5:
            return GCDDecodingStrategies.decode_pattern_length(state)
        elif method == 6:
            return GCDDecodingStrategies.decode_divisor_markers(state)
        elif method == 7:
            return GCDDecodingStrategies.decode_remainder_pattern(state)
        elif method == 8:
            return GCDDecodingStrategies.decode_consensus(state)
        elif method == 9:
            return GCDDecodingStrategies.decode_adaptive(state, encoding_type)
        else:
            return GCDDecodingStrategies.decode_consensus(state)
    
    def simulate(self, a: int, b: int, max_steps: int = 600) -> int:
        """Run complete GCD simulation with enhanced monitoring."""
        N = 200  # Larger window for complex cases
        state = self.encode_inputs(a, b, N)
        
        # Write program to initial state
        for i in range(min(len(self.prog), N)):
            state[i] = self.prog[i]
        
        for step in range(max_steps):
            # Check halting condition
            if self.check_halting(state):
                return self.decode_result(state)
            
            # Apply enhanced nonlocal rules
            state = self.nonlocal_rules.apply_rules(state)
        
        return -10  # Timeout

def create_random_full_scale_gcd_model(rng: np.random.Generator) -> FullScaleEnhancedGCDModel:
    """Create a random enhanced GCD model for full-scale training."""
    
    # Create 512-entry enhanced rule (8 types × 64 entries each)
    rule = create_random_enhanced_rules(rng)
    
    # Create program with better length distribution
    prog_length = rng.integers(8, 20)  # Longer programs for complex computation
    prog = rng.choice([0, 1], size=prog_length, p=[0.65, 0.35])
    prog = _sanitize_programme(prog)
    
    # Enhanced encoding parameters with bias toward effective schemes
    encoding_weights = [0.25, 0.20, 0.15, 0.10, 0.15, 0.15]  # Favor standard and commutative
    encoding_type = rng.choice(6, p=encoding_weights)
    
    encoding_params = {
        'separator_type': rng.integers(0, 4),
        'input_encoding': encoding_type,
    }
    
    # Enhanced decoding parameters with bias toward consensus methods
    decoding_weights = [0.15, 0.15, 0.10, 0.05, 0.05, 0.10, 0.10, 0.10, 0.15, 0.05]
    decoding_method = rng.choice(10, p=decoding_weights)
    
    decoding_params = {
        'method': decoding_method,
    }
    
    # Enhanced halting parameters
    halting_params = {
        'condition_type': rng.integers(0, 5),
        'threshold': rng.integers(45, 80),  # More conservative thresholds
    }
    
    return FullScaleEnhancedGCDModel(rule, prog, encoding_params, decoding_params, halting_params)

def evaluate_full_scale_gcd_model(model: FullScaleEnhancedGCDModel, test_cases: List[Tuple[int, int]]) -> Tuple[float, float, List[Dict]]:
    """Evaluate enhanced GCD model with detailed analysis."""
    correct = 0
    convergent = 0
    results = []
    
    for a, b in test_cases:
        expected = gcd(a, b)
        try:
            result = model.simulate(a, b, max_steps=800)
            is_correct = (result == expected)
            is_convergent = (result != -10)
            
            if is_correct:
                correct += 1
            if is_convergent:
                convergent += 1
            
            results.append({
                'a': a, 'b': b, 'expected': expected, 'result': result,
                'correct': is_correct, 'convergent': is_convergent
            })
            
        except Exception as e:
            results.append({
                'a': a, 'b': b, 'expected': expected, 'result': -99,
                'correct': False, 'convergent': False
            })
    
    accuracy = correct / len(test_cases) if test_cases else 0
    convergence = convergent / len(test_cases) if test_cases else 0
    
    return accuracy, convergence, results

class FullScaleGCDTrainer:
    """Full-scale trainer with refined curriculum and enhanced monitoring."""
    
    def __init__(self):
        self.curriculum = RefinedGCDCurriculumFramework()
        self.ga = EnhancedGeneticAlgorithm()
        self.training_history = []
        
    def enhanced_fitness_calculation(self, individual, test_cases, phase_config, evaluate_func):
        """Enhanced fitness calculation with anti-bias measures."""
        try:
            accuracy, convergence, results = evaluate_func(individual, test_cases)
        except Exception as e:
            return 0.0
        
        # Base fitness
        weights = phase_config.get('fitness_weights', {})
        base_fitness = (accuracy * weights.get('accuracy', 100) + 
                       convergence * weights.get('convergence', 10))
        
        # Anti-bias penalty: heavily penalize trivial solutions
        anti_bias_penalty = 0
        if 'anti_bias' in weights:
            # Check for patterns indicating trivial solutions
            outputs = [r['result'] for r in results if r['convergent']]
            if outputs:
                # Penalize if always outputting first input
                first_inputs = [r['a'] for r in results if r['convergent']]
                if len(outputs) > 3 and outputs == first_inputs:
                    anti_bias_penalty += weights['anti_bias']
                
                # Penalize if always outputting same value
                unique_outputs = set(outputs)
                if len(unique_outputs) == 1 and len(outputs) > 5:
                    anti_bias_penalty += weights['anti_bias'] * 0.5
        
        # Commutativity bonus
        commutativity_bonus = 0
        if 'commutativity_bonus' in weights:
            comm_violations = 0
            comm_tests = 0
            for i, (a, b) in enumerate(test_cases):
                # Find reverse case
                reverse_case = (b, a)
                if reverse_case in test_cases:
                    j = test_cases.index(reverse_case)
                    if i < len(results) and j < len(results):
                        result_ab = results[i]['result']
                        result_ba = results[j]['result']
                        if result_ab != result_ba:
                            comm_violations += 1
                        comm_tests += 1
            
            if comm_tests > 0:
                commutativity_rate = (comm_tests - comm_violations) / comm_tests
                commutativity_bonus = commutativity_rate * weights['commutativity_bonus']
        
        # Other bonuses from base class
        other_bonuses = self.ga.calculate_curriculum_fitness(
            individual, test_cases, phase_config, evaluate_func
        ) - base_fitness
        
        total_fitness = base_fitness + other_bonuses + commutativity_bonus - anti_bias_penalty
        return max(0.0, total_fitness)
    
    def train_phase_with_monitoring(self, phase_name: str, initial_population: Optional[List] = None):
        """Train phase with enhanced monitoring and adaptive adjustments."""
        
        print(f"\n🎯 FULL-SCALE TRAINING PHASE: {phase_name}")
        print("=" * 70)
        
        phase_config = self.curriculum.get_phase(phase_name)
        test_cases = phase_config['cases']
        
        print(f"Phase: {phase_config['name']}")
        print(f"Cases: {len(test_cases)} | Target: {phase_config['target_accuracy']:.1%}")
        print(f"Population: {phase_config['population_size']} | Generations: {phase_config['generations']}")
        
        # Initialize population
        if initial_population is None:
            print("Initializing random population...")
            rng = np.random.default_rng(42)
            population = []
            for _ in tqdm(range(phase_config['population_size']), desc="Creating population"):
                population.append(create_random_full_scale_gcd_model(rng))
        else:
            print("Using transferred population...")
            population = initial_population[:phase_config['population_size']]
            if len(population) < phase_config['population_size']:
                rng = np.random.default_rng(42)
                while len(population) < phase_config['population_size']:
                    population.append(create_random_full_scale_gcd_model(rng))
        
        # Update GA parameters
        self.ga.population_size = phase_config['population_size']
        self.ga.elite_fraction = phase_config['elite_fraction']
        self.ga.base_mutation_rate = phase_config['mutation_rate']
        self.ga.current_mutation_rate = phase_config['mutation_rate']
        
        best_model = None
        best_fitness = 0.0
        best_accuracy = 0.0
        stagnation_count = 0
        
        rng = np.random.default_rng()
        
        print(f"Starting evolution...")
        
        for generation in tqdm(range(phase_config['generations']), desc="Evolving"):
            
            # Evaluate population with enhanced fitness
            fitness_scores = []
            for individual in population:
                fitness = self.enhanced_fitness_calculation(
                    individual, test_cases, phase_config, evaluate_full_scale_gcd_model
                )
                fitness_scores.append(fitness)
            
            fitness_scores = np.array(fitness_scores)
            
            # Track best individual
            best_idx = np.argmax(fitness_scores)
            if fitness_scores[best_idx] > best_fitness:
                best_fitness = fitness_scores[best_idx]
                best_model = population[best_idx]
                stagnation_count = 0
                
                # Evaluate best model
                accuracy, convergence, _ = evaluate_full_scale_gcd_model(best_model, test_cases)
                best_accuracy = accuracy
                
                if generation % 50 == 0 or accuracy > phase_config['target_accuracy']:
                    print(f"\n  Gen {generation:3d}: fitness={best_fitness:.1f}, acc={accuracy:.3f}, conv={convergence:.3f}")
            else:
                stagnation_count += 1
            
            # Adaptive mutation rate
            self.ga.current_mutation_rate = self.ga.adaptive_mutation_rate(generation, stagnation_count)
            
            # Early stopping if target reached
            if best_accuracy >= phase_config['target_accuracy']:
                print(f"  🎯 Target accuracy reached at generation {generation}!")
                break
            
            # Evolve population
            if generation < phase_config['generations'] - 1:
                population = self.ga.evolve_generation(population, fitness_scores, rng)
        
        # Final evaluation
        final_accuracy, final_convergence, results = evaluate_full_scale_gcd_model(best_model, test_cases)
        
        print(f"\n📊 PHASE {phase_name} RESULTS:")
        print(f"  Final accuracy: {final_accuracy:.3f} (target: {phase_config['target_accuracy']:.3f})")
        print(f"  Final convergence: {final_convergence:.3f}")
        print(f"  Best fitness: {best_fitness:.1f}")
        
        # Save checkpoint
        checkpoint_path = f"fullscale_checkpoint_{phase_name}.pkl"
        checkpoint_data = {
            'phase_name': phase_name,
            'best_model': best_model,
            'final_accuracy': final_accuracy,
            'final_convergence': final_convergence,
            'best_fitness': best_fitness,
            'population': population[:100],  # Save top 100 for transfer
            'results': results
        }
        
        with open(checkpoint_path, 'wb') as f:
            pickle.dump(checkpoint_data, f)
        
        print(f"  💾 Checkpoint saved: {checkpoint_path}")
        
        return best_model, final_accuracy, final_convergence

    def run_full_scale_curriculum(self) -> FullScaleEnhancedGCDModel:
        """Run the complete full-scale curriculum training pipeline."""

        print("🚀 FULL-SCALE ENHANCED GCD CURRICULUM TRAINING")
        print("=" * 90)
        print("Training EM43 to compute GCD using refined 10-phase curriculum")
        print("Features: 8 Non-local Rules + 6 Encodings + 10 Decodings + Anti-bias + Large Populations")

        all_phases = self.curriculum.get_all_phases()
        current_population = None
        phase_results = {}

        start_time = time.time()

        for phase_idx, phase_name in enumerate(all_phases):
            phase_start = time.time()

            print(f"\n{'='*25} PHASE {phase_idx+1}/{len(all_phases)} {'='*25}")

            # Train this phase
            best_model, accuracy, convergence = self.train_phase_with_monitoring(
                phase_name, current_population
            )

            phase_results[phase_name] = {
                'accuracy': accuracy,
                'convergence': convergence,
                'model': best_model,
                'phase_time': time.time() - phase_start
            }

            phase_time = time.time() - phase_start
            print(f"Phase completed in {phase_time/60:.1f} minutes")

            # Prepare population transfer
            if phase_idx < len(all_phases) - 1:
                print("Preparing elite population transfer...")
                # Load checkpoint to get population
                try:
                    checkpoint_path = f"fullscale_checkpoint_{phase_name}.pkl"
                    with open(checkpoint_path, 'rb') as f:
                        checkpoint = pickle.load(f)

                    # Use top performers as seed
                    elite_population = checkpoint['population'][:200]  # Top 200
                    current_population = [best_model] * 100 + elite_population[:100]  # Mix best + elite

                except Exception as e:
                    print(f"Warning: Could not load checkpoint for transfer: {e}")
                    current_population = [best_model] * 150  # Fallback

        total_time = time.time() - start_time

        # Final comprehensive evaluation
        print(f"\n🏆 FULL-SCALE CURRICULUM TRAINING COMPLETE!")
        print(f"Total training time: {total_time/60:.1f} minutes ({total_time/3600:.1f} hours)")

        # Get final best model
        final_model = phase_results[all_phases[-1]]['model']

        # Comprehensive test on all curriculum cases
        print(f"\n🧪 COMPREHENSIVE EVALUATION:")
        all_test_cases = []
        for phase_name in all_phases:
            phase_cases = self.curriculum.get_phase(phase_name)['cases']
            all_test_cases.extend(phase_cases)

        # Remove duplicates
        all_test_cases = list(set(all_test_cases))

        final_accuracy, final_convergence, final_results = evaluate_full_scale_gcd_model(
            final_model, all_test_cases
        )

        print(f"Comprehensive Results:")
        print(f"  Test cases: {len(all_test_cases)}")
        print(f"  Accuracy: {final_accuracy:.3f} ({final_accuracy*100:.1f}%)")
        print(f"  Convergence: {final_convergence:.3f} ({final_convergence*100:.1f}%)")

        # Phase-by-phase summary
        print(f"\n📊 PHASE-BY-PHASE SUMMARY:")
        total_passed = 0
        for phase_name in all_phases:
            result = phase_results[phase_name]
            target = self.curriculum.get_phase(phase_name)['target_accuracy']
            status = "✅" if result['accuracy'] >= target else "❌"
            if result['accuracy'] >= target:
                total_passed += 1
            phase_time = result['phase_time']
            print(f"  {phase_name:25}: {result['accuracy']:.3f} (target: {target:.3f}) {status} [{phase_time/60:.1f}min]")

        print(f"\nPhases passed: {total_passed}/{len(all_phases)} ({total_passed/len(all_phases)*100:.1f}%)")

        # Save final comprehensive model
        final_model_data = {
            'rule': final_model.rule,
            'prog': final_model.prog,
            'encoding_params': final_model.encoding_params,
            'decoding_params': final_model.decoding_params,
            'halting_params': final_model.halting_params,
            'final_accuracy': final_accuracy,
            'final_convergence': final_convergence,
            'phase_results': phase_results,
            'training_time': total_time,
            'comprehensive_results': final_results,
            'curriculum_type': 'refined_10_phase',
            'training_mode': 'full_scale'
        }

        with open('full_scale_gcd_model_final.pkl', 'wb') as f:
            pickle.dump(final_model_data, f)

        print(f"\n💾 Final model saved: full_scale_gcd_model_final.pkl")
        print(f"🎉 Full-scale enhanced GCD training pipeline complete!")

        return final_model

def run_full_scale_gcd_training():
    """Main function to run full-scale GCD training."""
    trainer = FullScaleGCDTrainer()
    final_model = trainer.run_full_scale_curriculum()
    return final_model

def test_full_scale_components():
    """Test the full-scale training components."""
    print("🧪 TESTING FULL-SCALE GCD TRAINER COMPONENTS")
    print("=" * 60)

    # Test model creation
    rng = np.random.default_rng(42)
    model = create_random_full_scale_gcd_model(rng)

    print(f"Created full-scale model:")
    print(f"  Rule size: {len(model.rule)}")
    print(f"  Program size: {len(model.prog)}")
    print(f"  Encoding: {model.encoding_params}")
    print(f"  Decoding: {model.decoding_params}")

    # Test simulation
    test_cases = [(3, 6), (4, 4), (5, 7), (2, 8)]
    for a, b in test_cases:
        result = model.simulate(a, b, max_steps=200)
        expected = gcd(a, b)
        print(f"  GCD({a},{b})={expected} → {result}")

    print("\n✅ Full-scale trainer components test completed")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "train":
        run_full_scale_gcd_training()
    else:
        test_full_scale_components()
