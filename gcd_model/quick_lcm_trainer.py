"""
Quick LCM Trainer

This implements a streamlined LCM trainer with reasonable parameters for quick testing.
Uses the enhanced non-local rules with LCM-specific encoding/decoding and curriculum learning.

Key Features:
- 6-phase LCM curriculum (manageable scope)
- Quick training parameters (small populations, fewer generations)
- LCM-specific fitness functions
- Enhanced monitoring and validation
"""

import numpy as np
import sys
import os
import pickle
import time
from typing import List, Tuple, Dict, Any, Optional
from tqdm import tqdm

# Add em43_python to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'em43_python'))

# Import components
from enhanced_nonlocal_rules import EnhancedNonlocalRules, create_random_enhanced_rules
from lcm_curriculum_framework import LCMCurriculumFramework, lcm
from lcm_encoding_decoding import LCMEncodingSchemes, LCMDecodingStrategies
from enhanced_genetic_algorithm import EnhancedGeneticAlgorithm

# Import base EM43 components
from em43_numba import _sanitize_programme, _sanitize_rule

class QuickLCMModel:
    """Quick LCM model for streamlined training."""
    
    def __init__(self, rule: np.ndarray, prog: np.ndarray, 
                 encoding_params: Dict, decoding_params: Dict, halting_params: Dict):
        self.rule = rule.copy()
        self.prog = prog.copy()
        self.encoding_params = encoding_params.copy()
        self.decoding_params = decoding_params.copy()
        self.halting_params = halting_params.copy()
        
        # Initialize enhanced rule system
        self.nonlocal_rules = EnhancedNonlocalRules(self.rule)
    
    def encode_inputs(self, a: int, b: int, N: int = 120) -> np.ndarray:
        """Encode inputs using selected LCM encoding scheme."""
        encoding_type = self.encoding_params.get('input_encoding', 0)
        prog_len = len(self.prog)
        
        if encoding_type == 0:
            return LCMEncodingSchemes.encode_standard_lcm(a, b, prog_len, N)
        elif encoding_type == 1:
            return LCMEncodingSchemes.encode_product_hint(a, b, prog_len, N)
        elif encoding_type == 2:
            return LCMEncodingSchemes.encode_multiple_emphasis(a, b, prog_len, N)
        elif encoding_type == 3:
            return LCMEncodingSchemes.encode_lcm_symmetric(a, b, prog_len, N)
        else:
            return LCMEncodingSchemes.encode_standard_lcm(a, b, prog_len, N)
    
    def check_halting(self, state: np.ndarray) -> bool:
        """Check halting condition optimized for LCM."""
        N = len(state)
        
        # Count cell types
        counts = [0, 0, 0, 0]
        for x in state:
            counts[x] += 1
        
        blank, prog, red, blue = counts
        nonzero = prog + red + blue
        
        if nonzero == 0:
            return False
        
        halting_type = self.halting_params.get('condition_type', 0)
        threshold = self.halting_params.get('threshold', 60) / 100.0
        
        # LCM-optimized halting conditions
        if halting_type == 0:  # Blue ratio (LCM often uses blue for output)
            return blue / nonzero >= threshold and blue >= 1
        elif halting_type == 1:  # Red marker stability
            return red / nonzero >= threshold and red >= 2
        elif halting_type == 2:  # Combined markers
            return (blue + red) / nonzero >= threshold and blue >= 1
        elif halting_type == 3:  # Program reduction
            return prog / nonzero <= (1.0 - threshold) and (blue + red) >= 2
        else:  # Balanced approach
            marker_ok = (blue + red) >= 2
            ratio_ok = (blue + red) / nonzero >= threshold * 0.7
            return marker_ok and ratio_ok
    
    def decode_result(self, state: np.ndarray) -> int:
        """Decode LCM result using selected strategy."""
        decoding_method = self.decoding_params.get('method', 0)
        encoding_type = self.encoding_params.get('input_encoding', 0)
        
        if decoding_method == 0:
            result = LCMDecodingStrategies.decode_ones_after_last_marker(state)
        elif decoding_method == 1:
            result = LCMDecodingStrategies.decode_zeros_after_last_marker(state)
        elif decoding_method == 2:
            result = LCMDecodingStrategies.decode_blue_cells_total(state)
        elif decoding_method == 3:
            result = LCMDecodingStrategies.decode_multiple_detection(state)
        elif decoding_method == 4:
            result = LCMDecodingStrategies.decode_product_based(state)
        elif decoding_method == 5:
            result = LCMDecodingStrategies.decode_lcm_consensus(state)
        elif decoding_method == 6:
            result = LCMDecodingStrategies.decode_adaptive_lcm(state, encoding_type)
        else:
            result = LCMDecodingStrategies.decode_lcm_consensus(state)
        
        # LCM constraints: result must be >= max(a,b) and reasonable
        if result <= 0:
            return 1
        if result > 200:  # Cap unreasonable values
            return min(result, 100)
        
        return result
    
    def simulate(self, a: int, b: int, max_steps: int = 400) -> int:
        """Run complete LCM simulation."""
        N = 120  # Reasonable window for LCM
        state = self.encode_inputs(a, b, N)
        
        # Write program to initial state
        for i in range(min(len(self.prog), N)):
            state[i] = self.prog[i]
        
        for step in range(max_steps):
            # Check halting condition
            if self.check_halting(state):
                return self.decode_result(state)
            
            # Apply enhanced nonlocal rules
            state = self.nonlocal_rules.apply_rules(state)
        
        return -10  # Timeout

def create_random_quick_lcm_model(rng: np.random.Generator) -> QuickLCMModel:
    """Create a random LCM model for quick training."""
    
    # Create 512-entry enhanced rule
    rule = create_random_enhanced_rules(rng)
    
    # Create program
    prog_length = rng.integers(6, 14)
    prog = rng.choice([0, 1], size=prog_length, p=[0.6, 0.4])
    prog = _sanitize_programme(prog)
    
    # LCM-optimized encoding parameters
    encoding_params = {
        'separator_type': rng.integers(0, 4),
        'input_encoding': rng.integers(0, 4),  # 4 LCM encoding types
    }
    
    # LCM-optimized decoding parameters
    decoding_params = {
        'method': rng.integers(0, 7),  # 7 LCM decoding strategies
    }
    
    # LCM-optimized halting parameters
    halting_params = {
        'condition_type': rng.integers(0, 5),
        'threshold': rng.integers(50, 80),
    }
    
    return QuickLCMModel(rule, prog, encoding_params, decoding_params, halting_params)

def evaluate_quick_lcm_model(model: QuickLCMModel, test_cases: List[Tuple[int, int]]) -> Tuple[float, float, List[Dict]]:
    """Evaluate LCM model on test cases."""
    correct = 0
    convergent = 0
    results = []
    
    for a, b in test_cases:
        expected = lcm(a, b)
        try:
            result = model.simulate(a, b, max_steps=500)
            is_correct = (result == expected)
            is_convergent = (result != -10)
            
            if is_correct:
                correct += 1
            if is_convergent:
                convergent += 1
            
            results.append({
                'a': a, 'b': b, 'expected': expected, 'result': result,
                'correct': is_correct, 'convergent': is_convergent
            })
            
        except Exception as e:
            results.append({
                'a': a, 'b': b, 'expected': expected, 'result': -99,
                'correct': False, 'convergent': False
            })
    
    accuracy = correct / len(test_cases) if test_cases else 0
    convergence = convergent / len(test_cases) if test_cases else 0
    
    return accuracy, convergence, results

class QuickLCMTrainer:
    """Quick trainer for LCM computation."""
    
    def __init__(self):
        self.curriculum = LCMCurriculumFramework()
        self.ga = EnhancedGeneticAlgorithm()
        
    def lcm_fitness_calculation(self, individual, test_cases, phase_config, evaluate_func):
        """LCM-specific fitness calculation."""
        try:
            accuracy, convergence, results = evaluate_func(individual, test_cases)
        except Exception as e:
            return 0.0
        
        # Base fitness
        weights = phase_config.get('fitness_weights', {})
        base_fitness = (accuracy * weights.get('accuracy', 100) + 
                       convergence * weights.get('convergence', 10))
        
        # LCM-specific bonuses
        bonus = 0.0
        
        # Correctness bonus
        if accuracy >= 0.9:
            bonus += weights.get('correctness_bonus', 20)
        
        # Pattern recognition bonus (for multiples, trivial cases)
        if 'pattern_recognition' in weights:
            pattern_correct = 0
            pattern_total = 0
            for result in results:
                a, b = result['a'], result['b']
                # Check for simple patterns
                if a == b or a == 1 or b == 1 or a % b == 0 or b % a == 0:
                    pattern_total += 1
                    if result['correct']:
                        pattern_correct += 1
            
            if pattern_total > 0:
                pattern_accuracy = pattern_correct / pattern_total
                bonus += pattern_accuracy * weights['pattern_recognition']
        
        # Product learning bonus (for coprime cases)
        if 'product_learning' in weights:
            product_correct = 0
            product_total = 0
            for result in results:
                a, b = result['a'], result['b']
                expected_lcm = result['expected']
                # Check if coprime (LCM should equal product)
                if expected_lcm == a * b:
                    product_total += 1
                    if result['correct']:
                        product_correct += 1
            
            if product_total > 0:
                product_accuracy = product_correct / product_total
                bonus += product_accuracy * weights['product_learning']
        
        # Anti-trivial solution penalty
        outputs = [r['result'] for r in results if r['convergent']]
        if len(outputs) > 5:
            # Penalize if always outputting same value
            unique_outputs = set(outputs)
            if len(unique_outputs) == 1:
                bonus -= 50  # Heavy penalty for trivial solutions
        
        total_fitness = base_fitness + bonus
        return max(0.0, total_fitness)
    
    def train_lcm_phase(self, phase_name: str, initial_population: Optional[List] = None):
        """Train a single LCM phase."""
        
        print(f"\n🎯 QUICK LCM TRAINING PHASE: {phase_name}")
        print("=" * 60)
        
        phase_config = self.curriculum.get_phase(phase_name)
        test_cases = phase_config['cases']
        
        print(f"Phase: {phase_config['name']}")
        print(f"Cases: {len(test_cases)} | Target: {phase_config['target_accuracy']:.1%}")
        print(f"Population: {phase_config['population_size']} | Generations: {phase_config['generations']}")
        
        # Initialize population
        if initial_population is None:
            print("Initializing random population...")
            rng = np.random.default_rng(42)
            population = []
            for _ in range(phase_config['population_size']):
                population.append(create_random_quick_lcm_model(rng))
        else:
            population = initial_population[:phase_config['population_size']]
            if len(population) < phase_config['population_size']:
                rng = np.random.default_rng(42)
                while len(population) < phase_config['population_size']:
                    population.append(create_random_quick_lcm_model(rng))
        
        # Update GA parameters
        self.ga.population_size = phase_config['population_size']
        self.ga.elite_fraction = phase_config['elite_fraction']
        self.ga.base_mutation_rate = phase_config['mutation_rate']
        
        best_model = None
        best_fitness = 0.0
        best_accuracy = 0.0
        
        rng = np.random.default_rng()
        
        for generation in tqdm(range(phase_config['generations']), desc="Evolving"):
            
            # Evaluate population
            fitness_scores = []
            for individual in population:
                fitness = self.lcm_fitness_calculation(
                    individual, test_cases, phase_config, evaluate_quick_lcm_model
                )
                fitness_scores.append(fitness)
            
            fitness_scores = np.array(fitness_scores)
            
            # Track best individual
            best_idx = np.argmax(fitness_scores)
            if fitness_scores[best_idx] > best_fitness:
                best_fitness = fitness_scores[best_idx]
                best_model = population[best_idx]
                
                # Evaluate best model
                accuracy, convergence, _ = evaluate_quick_lcm_model(best_model, test_cases)
                best_accuracy = accuracy
                
                if generation % 25 == 0 or accuracy > phase_config['target_accuracy']:
                    print(f"\n  Gen {generation:3d}: fitness={best_fitness:.1f}, acc={accuracy:.3f}, conv={convergence:.3f}")
            
            # Early stopping if target reached
            if best_accuracy >= phase_config['target_accuracy']:
                print(f"  🎯 Target accuracy reached at generation {generation}!")
                break
            
            # Evolve population
            if generation < phase_config['generations'] - 1:
                population = self.ga.evolve_generation(population, fitness_scores, rng)
        
        # Final evaluation
        final_accuracy, final_convergence, results = evaluate_quick_lcm_model(best_model, test_cases)
        
        print(f"\n📊 PHASE {phase_name} RESULTS:")
        print(f"  Final accuracy: {final_accuracy:.3f} (target: {phase_config['target_accuracy']:.3f})")
        print(f"  Final convergence: {final_convergence:.3f}")
        print(f"  Best fitness: {best_fitness:.1f}")
        
        return best_model, final_accuracy, final_convergence

    def run_quick_lcm_curriculum(self) -> QuickLCMModel:
        """Run the complete quick LCM curriculum training."""

        print("🚀 QUICK LCM CURRICULUM TRAINING")
        print("=" * 70)
        print("Training EM43 to compute LCM using 6-phase curriculum")
        print("Features: Enhanced Non-local Rules + LCM Encoding/Decoding + Quick Training")

        all_phases = self.curriculum.get_all_phases()
        current_population = None
        phase_results = {}

        start_time = time.time()

        for phase_idx, phase_name in enumerate(all_phases):
            phase_start = time.time()

            print(f"\n{'='*15} PHASE {phase_idx+1}/{len(all_phases)} {'='*15}")

            # Train this phase
            best_model, accuracy, convergence = self.train_lcm_phase(phase_name, current_population)

            phase_results[phase_name] = {
                'accuracy': accuracy,
                'convergence': convergence,
                'model': best_model
            }

            phase_time = time.time() - phase_start
            print(f"Phase completed in {phase_time:.1f} seconds")

            # Transfer best individuals to next phase
            if phase_idx < len(all_phases) - 1:
                print("Preparing population transfer...")
                current_population = [best_model] * 20  # Seed with best model

        total_time = time.time() - start_time

        # Final comprehensive evaluation
        print(f"\n🏆 QUICK LCM CURRICULUM TRAINING COMPLETE!")
        print(f"Total training time: {total_time:.1f} seconds ({total_time/60:.1f} minutes)")

        # Get final best model
        final_model = phase_results[all_phases[-1]]['model']

        # Comprehensive test
        print(f"\n🧪 COMPREHENSIVE EVALUATION:")
        all_test_cases = self.curriculum.get_comprehensive_test_set()
        final_accuracy, final_convergence, final_results = evaluate_quick_lcm_model(
            final_model, all_test_cases
        )

        print(f"Comprehensive Results:")
        print(f"  Test cases: {len(all_test_cases)}")
        print(f"  Accuracy: {final_accuracy:.3f} ({final_accuracy*100:.1f}%)")
        print(f"  Convergence: {final_convergence:.3f} ({final_convergence*100:.1f}%)")

        # Phase-by-phase summary
        print(f"\n📊 PHASE SUMMARY:")
        total_passed = 0
        for phase_name in all_phases:
            result = phase_results[phase_name]
            target = self.curriculum.get_phase(phase_name)['target_accuracy']
            status = "✅" if result['accuracy'] >= target else "❌"
            if result['accuracy'] >= target:
                total_passed += 1
            print(f"  {phase_name:25}: {result['accuracy']:.3f} (target: {target:.3f}) {status}")

        print(f"\nPhases passed: {total_passed}/{len(all_phases)} ({total_passed/len(all_phases)*100:.1f}%)")

        # Save final model
        final_model_data = {
            'rule': final_model.rule,
            'prog': final_model.prog,
            'encoding_params': final_model.encoding_params,
            'decoding_params': final_model.decoding_params,
            'halting_params': final_model.halting_params,
            'final_accuracy': final_accuracy,
            'final_convergence': final_convergence,
            'phase_results': phase_results,
            'training_time': total_time,
            'comprehensive_results': final_results,
            'curriculum_type': 'lcm_6_phase',
            'training_mode': 'quick'
        }

        with open('quick_lcm_model_final.pkl', 'wb') as f:
            pickle.dump(final_model_data, f)

        print(f"\n💾 Final LCM model saved: quick_lcm_model_final.pkl")
        print(f"🎉 Quick LCM training pipeline complete!")

        return final_model

def run_quick_lcm_training():
    """Main function to run quick LCM training."""
    trainer = QuickLCMTrainer()
    final_model = trainer.run_quick_lcm_curriculum()
    return final_model

def test_quick_lcm_trainer():
    """Test the quick LCM trainer components."""
    print("🧪 TESTING QUICK LCM TRAINER")
    print("=" * 50)

    # Test model creation
    rng = np.random.default_rng(42)
    model = create_random_quick_lcm_model(rng)

    print(f"Created LCM model:")
    print(f"  Rule size: {len(model.rule)}")
    print(f"  Program size: {len(model.prog)}")
    print(f"  Encoding: {model.encoding_params}")
    print(f"  Decoding: {model.decoding_params}")

    # Test simulation
    test_cases = [(2, 3), (4, 6), (5, 5), (3, 9)]
    for a, b in test_cases:
        result = model.simulate(a, b, max_steps=200)
        expected = lcm(a, b)
        print(f"  LCM({a},{b})={expected} → {result}")

    print("\n✅ Quick LCM trainer test completed")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "train":
        run_quick_lcm_training()
    else:
        test_quick_lcm_trainer()
