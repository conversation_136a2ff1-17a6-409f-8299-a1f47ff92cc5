# GCD Model

This folder contains the EM43 model trained to compute Greatest Common Divisor (GCD):

- `commutative_nonlocal_gcd_trainer.py` - Main GCD training script using nonlocal rules
- `test_gcd_trainer.py` - Testing and validation scripts
- `analyze_gcd_model.py` - Comprehensive model analysis
- `create_gcd_viewer.py` - HTML viewer generator
- `gcd_viewer_trained.html` - Interactive web viewer for the trained GCD model
- `best_gcd_model.pkl` - Best trained GCD model

## Results
The GCD model achieves:
- 60.7% accuracy on test cases
- 100% convergence (no timeouts)
- Perfect commutativity: GCD(a,b) = GCD(b,a)
- Excellent detection of coprime pairs (GCD=1)

This represents the latest advancement in EM43 mathematical computation.
