"""
GCD Curriculum Learning Framework

This module implements a progressive curriculum for training EM43 models to compute GCD.
The curriculum is designed to gradually increase complexity while building on previous knowledge.

Curriculum Phases:
1. Co-prime pairs (GCD=1) - Learn basic structure
2. Small shared divisors (GCD=2,3) - Learn divisor detection  
3. Medium numbers (GCD=2-6) - Scale to larger inputs
4. Larger divergent pairs - Handle size differences
5. Complex cases - Edge cases and generalization
"""

import numpy as np
from typing import List, Tuple, Dict, Any
import math

def gcd(a: int, b: int) -> int:
    """Compute GCD using Euclidean algorithm."""
    while b:
        a, b = b, a % b
    return a

class GCDCurriculumFramework:
    """Framework for progressive GCD curriculum learning."""
    
    def __init__(self):
        self.phases = self._create_curriculum_phases()
    
    def _create_curriculum_phases(self) -> Dict[str, Dict[str, Any]]:
        """Create the complete curriculum with all phases."""
        
        phases = {
            'phase1_coprime': {
                'name': 'Co-prime Pairs (GCD=1)',
                'description': 'Learn to identify when numbers have no common divisors',
                'cases': self._generate_coprime_cases(),
                'target_accuracy': 0.95,
                'window': 150,
                'max_steps': 300,
                'generations': 200,
                'population_size': 300,
                'mutation_rate': 0.03,
                'elite_fraction': 0.15,
                'fitness_weights': {'accuracy': 100, 'convergence': 20, 'correctness_bonus': 50}
            },
            
            'phase2_small_divisors': {
                'name': 'Small Shared Divisors (GCD=2,3)',
                'description': 'Learn to detect and compute small common divisors',
                'cases': self._generate_small_divisor_cases(),
                'target_accuracy': 0.90,
                'window': 200,
                'max_steps': 400,
                'generations': 250,
                'population_size': 400,
                'mutation_rate': 0.025,
                'elite_fraction': 0.20,
                'fitness_weights': {'accuracy': 100, 'convergence': 15, 'gcd_diversity': 30}
            },
            
            'phase3_medium_numbers': {
                'name': 'Medium Numbers (GCD=2-6)',
                'description': 'Scale to larger inputs with various GCD values',
                'cases': self._generate_medium_number_cases(),
                'target_accuracy': 0.85,
                'window': 250,
                'max_steps': 500,
                'generations': 300,
                'population_size': 500,
                'mutation_rate': 0.02,
                'elite_fraction': 0.25,
                'fitness_weights': {'accuracy': 100, 'convergence': 10, 'generalization': 40}
            },
            
            'phase4_divergent_pairs': {
                'name': 'Larger Divergent Pairs',
                'description': 'Handle cases where inputs differ significantly in size',
                'cases': self._generate_divergent_cases(),
                'target_accuracy': 0.80,
                'window': 300,
                'max_steps': 600,
                'generations': 350,
                'population_size': 600,
                'mutation_rate': 0.018,
                'elite_fraction': 0.30,
                'fitness_weights': {'accuracy': 100, 'convergence': 8, 'size_robustness': 50}
            },
            
            'phase5_complex_cases': {
                'name': 'Complex Cases',
                'description': 'Edge cases, large numbers, and comprehensive generalization',
                'cases': self._generate_complex_cases(),
                'target_accuracy': 0.75,
                'window': 400,
                'max_steps': 800,
                'generations': 400,
                'population_size': 800,
                'mutation_rate': 0.015,
                'elite_fraction': 0.35,
                'fitness_weights': {'accuracy': 100, 'convergence': 5, 'edge_case_handling': 60}
            }
        }
        
        return phases
    
    def _generate_coprime_cases(self) -> List[Tuple[int, int]]:
        """Generate co-prime pairs (GCD=1) for phase 1."""
        cases = []
        
        # Small co-prime pairs
        small_coprimes = [
            (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7),
            (2, 3), (2, 5), (2, 7), 
            (3, 4), (3, 5), (3, 7), (3, 8),
            (4, 5), (4, 7), (4, 9),
            (5, 6), (5, 7), (5, 8), (5, 9),
            (6, 7), (7, 8), (7, 9), (8, 9)
        ]
        
        # Add both orders for commutativity
        for a, b in small_coprimes:
            cases.extend([(a, b), (b, a)])
        
        # Remove duplicates and ensure all are actually coprime
        cases = list(set(cases))
        cases = [(a, b) for a, b in cases if gcd(a, b) == 1]
        
        return cases[:30]  # Limit to 30 cases for focused learning
    
    def _generate_small_divisor_cases(self) -> List[Tuple[int, int]]:
        """Generate cases with small common divisors (GCD=2,3) for phase 2."""
        cases = []
        
        # GCD = 2 cases
        gcd2_cases = [
            (2, 4), (2, 6), (2, 8), (2, 10),
            (4, 6), (4, 8), (4, 10), (4, 12),
            (6, 8), (6, 10), (6, 12), (6, 14),
            (8, 10), (8, 12), (8, 14), (10, 12)
        ]
        
        # GCD = 3 cases  
        gcd3_cases = [
            (3, 6), (3, 9), (3, 12), (3, 15),
            (6, 9), (6, 12), (6, 15), (6, 18),
            (9, 12), (9, 15), (9, 18), (12, 15)
        ]
        
        # Combine and add both orders
        all_cases = gcd2_cases + gcd3_cases
        for a, b in all_cases:
            cases.extend([(a, b), (b, a)])
        
        # Add some coprime cases to maintain previous learning
        coprime_subset = [(1, 5), (2, 7), (3, 8), (4, 9), (5, 7)]
        for a, b in coprime_subset:
            cases.extend([(a, b), (b, a)])
        
        return list(set(cases))
    
    def _generate_medium_number_cases(self) -> List[Tuple[int, int]]:
        """Generate medium number cases with GCD values 2-6 for phase 3."""
        cases = []
        
        # Systematically generate cases for each GCD value
        for target_gcd in range(2, 7):  # GCD = 2, 3, 4, 5, 6
            for mult1 in range(1, 6):
                for mult2 in range(mult1, 6):
                    if gcd(mult1, mult2) == 1:  # Ensure mult1, mult2 are coprime
                        a = target_gcd * mult1
                        b = target_gcd * mult2
                        if a <= 20 and b <= 20:  # Keep numbers reasonable
                            cases.extend([(a, b), (b, a)])
        
        # Add some coprime cases to maintain learning
        coprime_cases = [(7, 11), (8, 15), (9, 14), (10, 13), (11, 12)]
        for a, b in coprime_cases:
            cases.extend([(a, b), (b, a)])
        
        return list(set(cases))
    
    def _generate_divergent_cases(self) -> List[Tuple[int, int]]:
        """Generate cases where inputs differ significantly in size for phase 4."""
        cases = []
        
        # Large vs small with various GCDs
        divergent_patterns = [
            # Small GCD, large difference
            (2, 20), (3, 21), (4, 24), (5, 25), (6, 30),
            (2, 18), (3, 18), (4, 20), (5, 20), (6, 24),
            
            # Medium GCD, large difference  
            (6, 18), (8, 24), (9, 27), (10, 30), (12, 36),
            (4, 28), (6, 42), (8, 32), (10, 40), (12, 48),
            
            # Coprime with large difference
            (1, 25), (3, 25), (7, 25), (11, 25), (13, 25),
            (5, 28), (7, 30), (11, 30), (13, 30), (17, 30)
        ]
        
        for a, b in divergent_patterns:
            cases.extend([(a, b), (b, a)])
        
        return list(set(cases))
    
    def _generate_complex_cases(self) -> List[Tuple[int, int]]:
        """Generate complex cases for final phase including edge cases."""
        cases = []
        
        # Large numbers with various GCDs
        large_cases = [
            (12, 18), (15, 25), (16, 24), (18, 27), (20, 30),
            (21, 35), (24, 36), (25, 35), (28, 42), (30, 45),
            (32, 48), (35, 49), (36, 54), (40, 60), (42, 63)
        ]
        
        # Edge cases
        edge_cases = [
            (1, 1),   # Same number
            (7, 7), (12, 12), (15, 15),  # Identical larger numbers
            (1, 50), (2, 50), (5, 50),   # One very large
        ]
        
        # Prime number cases
        prime_cases = [
            (7, 11), (7, 13), (11, 13), (11, 17), (13, 17),
            (17, 19), (19, 23), (7, 23), (11, 23), (13, 19)
        ]
        
        all_cases = large_cases + edge_cases + prime_cases
        for a, b in all_cases:
            if a <= 50 and b <= 50:  # Reasonable size limit
                cases.extend([(a, b), (b, a)])
        
        return list(set(cases))
    
    def get_phase(self, phase_name: str) -> Dict[str, Any]:
        """Get configuration for a specific phase."""
        if phase_name not in self.phases:
            raise ValueError(f"Unknown phase: {phase_name}")
        return self.phases[phase_name]
    
    def get_all_phases(self) -> List[str]:
        """Get list of all phase names in order."""
        return [
            'phase1_coprime',
            'phase2_small_divisors', 
            'phase3_medium_numbers',
            'phase4_divergent_pairs',
            'phase5_complex_cases'
        ]
    
    def validate_phase_progression(self, results: Dict[str, float]) -> Dict[str, bool]:
        """Validate if each phase meets its target accuracy."""
        validation = {}
        for phase_name in self.get_all_phases():
            phase_config = self.phases[phase_name]
            target = phase_config['target_accuracy']
            actual = results.get(phase_name, 0.0)
            validation[phase_name] = actual >= target
        return validation
    
    def get_comprehensive_test_set(self) -> List[Tuple[int, int]]:
        """Get comprehensive test set combining all phases."""
        all_cases = []
        for phase_name in self.get_all_phases():
            phase_cases = self.phases[phase_name]['cases']
            all_cases.extend(phase_cases)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_cases = []
        for case in all_cases:
            if case not in seen:
                seen.add(case)
                unique_cases.append(case)
        
        return unique_cases

def test_curriculum_framework():
    """Test the curriculum framework."""
    print("🧪 TESTING GCD CURRICULUM FRAMEWORK")
    print("=" * 50)
    
    framework = GCDCurriculumFramework()
    
    for phase_name in framework.get_all_phases():
        phase = framework.get_phase(phase_name)
        cases = phase['cases']
        
        print(f"\n{phase['name']}:")
        print(f"  Cases: {len(cases)}")
        print(f"  Target accuracy: {phase['target_accuracy']}")
        print(f"  Sample cases: {cases[:10]}")
        
        # Verify GCD values
        gcd_values = [gcd(a, b) for a, b in cases]
        unique_gcds = sorted(set(gcd_values))
        print(f"  GCD values: {unique_gcds}")
    
    print(f"\nTotal comprehensive test cases: {len(framework.get_comprehensive_test_set())}")
    print("✅ Curriculum framework test completed")

if __name__ == "__main__":
    test_curriculum_framework()
