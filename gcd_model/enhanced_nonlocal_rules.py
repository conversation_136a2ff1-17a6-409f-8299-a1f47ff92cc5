"""
Enhanced Non-local Rule System for GCD Computation

This module implements an extended non-local rule system with 8 different rule types
to enable better long-range information propagation and more sophisticated computation.

Key improvements over the 4-rule system:
1. More diverse neighborhood patterns
2. Better handling of information blocking
3. Adaptive rule selection based on local context
4. Support for multi-step information propagation
"""

import numpy as np
from typing import Tuple, List

class EnhancedNonlocalRules:
    """Enhanced non-local rule system with 8 rule types."""
    
    def __init__(self, rule_array: np.ndarray):
        """
        Initialize with 512-entry rule array (8 types × 64 entries each).
        
        Rule types:
        0: Local (radius-1): (left, center, right)
        1: Skip-left: (left-2, center, right)  
        2: Skip-right: (left, center, right+2)
        3: Long-range: (left-2, center, right+2)
        4: Skip-2-left: (left-3, center, right)
        5: Skip-2-right: (left, center, right+3)
        6: Diagonal-left: (left-2, center, right+1)
        7: Diagonal-right: (left-1, center, right+2)
        """
        assert len(rule_array) == 512, f"Rule array must have 512 entries, got {len(rule_array)}"
        self.rule_array = rule_array.copy()
        
        # Split into 8 rule types
        self.rules = {}
        for i in range(8):
            self.rules[i] = rule_array[i*64:(i+1)*64]
    
    def get_neighborhood_value(self, state: np.ndarray, x: int, rule_type: int) -> int:
        """Get the neighborhood value for a specific rule type at position x."""
        N = len(state)
        
        if rule_type == 0:  # Local
            left = state[x-1] if x > 0 else 0
            center = state[x]
            right = state[x+1] if x < N-1 else 0
            return (left << 4) | (center << 2) | right
            
        elif rule_type == 1:  # Skip-left
            left2 = state[x-2] if x > 1 else 0
            center = state[x]
            right = state[x+1] if x < N-1 else 0
            return (left2 << 4) | (center << 2) | right
            
        elif rule_type == 2:  # Skip-right
            left = state[x-1] if x > 0 else 0
            center = state[x]
            right2 = state[x+2] if x < N-2 else 0
            return (left << 4) | (center << 2) | right2
            
        elif rule_type == 3:  # Long-range
            left2 = state[x-2] if x > 1 else 0
            center = state[x]
            right2 = state[x+2] if x < N-2 else 0
            return (left2 << 4) | (center << 2) | right2
            
        elif rule_type == 4:  # Skip-2-left
            left3 = state[x-3] if x > 2 else 0
            center = state[x]
            right = state[x+1] if x < N-1 else 0
            return (left3 << 4) | (center << 2) | right
            
        elif rule_type == 5:  # Skip-2-right
            left = state[x-1] if x > 0 else 0
            center = state[x]
            right3 = state[x+3] if x < N-3 else 0
            return (left << 4) | (center << 2) | right3
            
        elif rule_type == 6:  # Diagonal-left
            left2 = state[x-2] if x > 1 else 0
            center = state[x]
            right1 = state[x+1] if x < N-1 else 0
            return (left2 << 4) | (center << 2) | right1
            
        elif rule_type == 7:  # Diagonal-right
            left1 = state[x-1] if x > 0 else 0
            center = state[x]
            right2 = state[x+2] if x < N-2 else 0
            return (left1 << 4) | (center << 2) | right2
            
        else:
            raise ValueError(f"Invalid rule type: {rule_type}")
    
    def select_rule_type(self, state: np.ndarray, x: int) -> int:
        """
        Intelligently select which rule type to use based on local context.
        
        Strategy:
        1. Use local rule by default
        2. Use skip rules when neighbors are blocking
        3. Use long-range rules when center is non-zero
        4. Use diagonal rules for asymmetric patterns
        5. Use skip-2 rules for dense blocking regions
        """
        N = len(state)
        
        # Get local neighborhood
        left = state[x-1] if x > 0 else 0
        center = state[x]
        right = state[x+1] if x < N-1 else 0
        
        # Get extended neighborhood for analysis
        left2 = state[x-2] if x > 1 else 0
        right2 = state[x+2] if x < N-2 else 0
        left3 = state[x-3] if x > 2 else 0
        right3 = state[x+3] if x < N-3 else 0
        
        # Count non-zero neighbors in different ranges
        local_nonzero = sum([left != 0, center != 0, right != 0])
        extended_nonzero = sum([left2 != 0, right2 != 0])
        far_nonzero = sum([left3 != 0, right3 != 0])
        
        # Rule selection logic
        if center != 0:
            # Non-zero center: use long-range to propagate through
            if extended_nonzero > 0:
                return 3  # Long-range
            else:
                return 0  # Local
                
        elif local_nonzero >= 2:
            # Dense local region: try to skip over blocking
            if far_nonzero > 0:
                # Use skip-2 rules for very dense regions
                if left != 0 and left2 != 0:
                    return 4  # Skip-2-left
                elif right != 0 and right2 != 0:
                    return 5  # Skip-2-right
            
            # Use regular skip rules
            if left != 0 and right == 0:
                return 1  # Skip-left
            elif right != 0 and left == 0:
                return 2  # Skip-right
            else:
                return 3  # Long-range
                
        elif local_nonzero == 1:
            # Sparse region: use diagonal rules for asymmetric propagation
            if left != 0:
                return 6  # Diagonal-left
            elif right != 0:
                return 7  # Diagonal-right
            else:
                return 0  # Local (center is non-zero)
                
        else:
            # All zeros locally: use local rule
            return 0
    
    def apply_rules(self, state: np.ndarray) -> np.ndarray:
        """Apply the enhanced non-local rules to compute next state."""
        N = len(state)
        nxt = np.zeros(N, dtype=np.uint8)
        
        for x in range(1, N-1):
            # Select appropriate rule type
            rule_type = self.select_rule_type(state, x)
            
            # Get neighborhood value
            neighborhood_idx = self.get_neighborhood_value(state, x, rule_type)
            
            # Apply rule
            nxt[x] = self.rules[rule_type][neighborhood_idx]
        
        return nxt
    
    def get_rule_usage_stats(self, state: np.ndarray) -> dict:
        """Get statistics on which rule types would be used."""
        N = len(state)
        usage = {i: 0 for i in range(8)}
        
        for x in range(1, N-1):
            rule_type = self.select_rule_type(state, x)
            usage[rule_type] += 1
        
        return usage

def create_random_enhanced_rules(rng: np.random.Generator) -> np.ndarray:
    """Create a random 512-entry enhanced rule array."""
    rule = rng.integers(0, 4, 512, dtype=np.uint8)
    
    # Sanitize the local rule (first 64 entries) using existing function
    from em43_numba import _sanitize_rule
    base_rule = rule[0:64]
    base_rule = _sanitize_rule(base_rule)
    rule[0:64] = base_rule
    
    return rule

def test_enhanced_rules():
    """Test the enhanced rule system."""
    print("🧪 TESTING ENHANCED NON-LOCAL RULES")
    print("=" * 50)
    
    # Create test rule
    rng = np.random.default_rng(42)
    rule_array = create_random_enhanced_rules(rng)
    rules = EnhancedNonlocalRules(rule_array)
    
    # Test state with various patterns
    test_state = np.array([0, 1, 0, 2, 0, 3, 0, 1, 2, 0, 0, 3, 0], dtype=np.uint8)
    print(f"Test state: {test_state}")
    
    # Get rule usage statistics
    usage = rules.get_rule_usage_stats(test_state)
    print(f"Rule usage: {usage}")
    
    # Apply one step
    next_state = rules.apply_rules(test_state)
    print(f"Next state: {next_state}")
    
    print("✅ Enhanced rules test completed")

if __name__ == "__main__":
    test_enhanced_rules()
