"""
Commutative Nonlocal EM43 Training for GCD

Building on the successful addition model, this trains an EM43 with nonlocal rules
to compute GCD(a,b) with commutative encoding where GCD(a,b) = GCD(b,a).

Key innovations from addition model:
- 4 nonlocal rule types (local, skip-left, skip-right, long-range)
- Adaptive encoding/decoding/halting schemes
- Commutative input encoding for when a=b overlap
"""

import numpy as np
import time
import pickle
import math
from tqdm import tqdm
from adaptive_nonlocal_training import AdaptiveEM43, create_random_adaptive_em43

def gcd(a, b):
    """Compute GCD using Euclidean algorithm"""
    while b:
        a, b = b, a % b
    return a

def create_gcd_test_cases():
    """Create comprehensive GCD test cases"""
    cases = []
    
    # Small cases (training set)
    for a in range(1, 8):
        for b in range(1, 8):
            cases.append((a, b))
    
    # Add some specific interesting cases
    interesting = [
        (8, 12), (12, 8),   # GCD = 4
        (15, 25), (25, 15), # GCD = 5
        (7, 11), (11, 7),   # GCD = 1 (coprime)
        (6, 9), (9, 6),     # GCD = 3
        (10, 15), (15, 10), # GCD = 5
        (14, 21), (21, 14), # GCD = 7
    ]
    
    cases.extend(interesting)
    return list(set(cases))  # Remove duplicates

def evaluate_gcd_model(em43, test_cases, verbose=False):
    """Evaluate GCD model on test cases"""
    correct = 0
    convergent = 0
    results = []
    
    for a, b in test_cases:
        expected = gcd(a, b)
        try:
            result = em43.simulate(a, b, max_steps=500)
            is_correct = (result == expected)
            is_convergent = (result != -10)
            
            if is_correct:
                correct += 1
            if is_convergent:
                convergent += 1
                
            results.append({
                'a': a, 'b': b, 'expected': expected, 'result': result,
                'correct': is_correct, 'convergent': is_convergent
            })
            
            if verbose and (is_correct or not is_convergent):
                status = "✅" if is_correct else ("⏰" if not is_convergent else "❌")
                print(f"  GCD({a},{b})={expected} → {result} {status}")
                
        except Exception as e:
            results.append({
                'a': a, 'b': b, 'expected': expected, 'result': -99,
                'correct': False, 'convergent': False
            })
            if verbose:
                print(f"  GCD({a},{b})={expected} → ERROR: {e}")
    
    accuracy = correct / len(test_cases) if test_cases else 0
    convergence = convergent / len(test_cases) if test_cases else 0
    
    return accuracy, convergence, results

def create_gcd_adaptive_em43(rng):
    """Create adaptive EM43 specifically tuned for GCD computation"""
    
    # 256-entry nonlocal rule (4 types × 64 entries each)
    rule = rng.integers(0, 4, 256, dtype=np.uint8)
    
    # Sanitize base local rule (first 64 entries)
    from em43_numba import _sanitize_rule, _sanitize_programme
    base_rule = rule[0:64]
    base_rule = _sanitize_rule(base_rule)
    rule[0:64] = base_rule
    
    # Program - slightly longer for GCD complexity
    prog = rng.choice([0, 1], size=rng.integers(6, 15), p=[0.6, 0.4])
    prog = _sanitize_programme(prog)
    
    # GCD-specific encoding parameters
    encoding_params = {
        'separator_type': rng.integers(0, 4),  # 0=BB, 1=B, 2=BBB, 3=none
        'input_encoding': 1,  # Force commutative encoding for GCD
    }
    
    # GCD-specific decoding parameters  
    decoding_params = {
        'method': rng.integers(0, 5),  # Try different decoding strategies
    }
    
    # GCD-specific halting parameters
    halting_params = {
        'condition_type': rng.integers(0, 5),
        'threshold': rng.integers(40, 90),  # Higher threshold for stability
    }
    
    return AdaptiveEM43(rule, prog, encoding_params, decoding_params, halting_params)

def genetic_algorithm_gcd(population_size=1000, generations=500, elite_fraction=0.1):
    """Run genetic algorithm to evolve GCD-computing EM43"""
    
    print("🧬 STARTING GCD GENETIC ALGORITHM")
    print("=" * 60)
    
    # Create test cases
    train_cases = [(a, b) for a in range(1, 6) for b in range(1, 6)]  # Small training set
    test_cases = create_gcd_test_cases()
    
    print(f"Training cases: {len(train_cases)}")
    print(f"Test cases: {len(test_cases)}")
    
    # Initialize population
    rng = np.random.default_rng(42)
    population = []
    fitness_scores = []
    
    print("Initializing population...")
    for _ in tqdm(range(population_size), desc="Creating population"):
        em43 = create_gcd_adaptive_em43(rng)
        population.append(em43)
        
        # Evaluate fitness
        accuracy, convergence, _ = evaluate_gcd_model(em43, train_cases)
        fitness = accuracy * 100 + convergence * 10  # Prioritize accuracy
        fitness_scores.append(fitness)
    
    fitness_scores = np.array(fitness_scores)
    best_fitness = np.max(fitness_scores)
    best_idx = np.argmax(fitness_scores)
    best_model = population[best_idx]
    
    print(f"Initial best fitness: {best_fitness:.2f}")
    
    # Evolution loop
    n_elite = int(elite_fraction * population_size)
    
    for generation in tqdm(range(generations), desc="Evolving"):
        
        # Selection and reproduction
        new_population = []
        new_fitness = []
        
        # Keep elite
        elite_indices = np.argsort(fitness_scores)[-n_elite:]
        for idx in elite_indices:
            new_population.append(population[idx])
            new_fitness.append(fitness_scores[idx])
        
        # Generate offspring
        while len(new_population) < population_size:
            # Tournament selection
            parent1_idx = tournament_selection(fitness_scores, k=3, rng=rng)
            parent2_idx = tournament_selection(fitness_scores, k=3, rng=rng)
            
            # Crossover and mutation
            child = crossover_and_mutate(
                population[parent1_idx], 
                population[parent2_idx], 
                rng
            )
            
            # Evaluate child
            accuracy, convergence, _ = evaluate_gcd_model(child, train_cases)
            child_fitness = accuracy * 100 + convergence * 10
            
            new_population.append(child)
            new_fitness.append(child_fitness)
        
        population = new_population
        fitness_scores = np.array(new_fitness)
        
        # Track best
        current_best_fitness = np.max(fitness_scores)
        if current_best_fitness > best_fitness:
            best_fitness = current_best_fitness
            best_idx = np.argmax(fitness_scores)
            best_model = population[best_idx]
            
            print(f"\nGeneration {generation}: New best fitness {best_fitness:.2f}")
            
            # Test on full test set
            test_accuracy, test_convergence, results = evaluate_gcd_model(
                best_model, test_cases, verbose=False
            )
            print(f"Test accuracy: {test_accuracy:.3f}, convergence: {test_convergence:.3f}")
            
            # Show some specific results
            print("Sample results:")
            for result in results[:10]:
                a, b = result['a'], result['b']
                expected, actual = result['expected'], result['result']
                status = "✅" if result['correct'] else "❌"
                print(f"  GCD({a},{b})={expected} → {actual} {status}")
        
        # Periodic detailed analysis
        if generation % 50 == 0:
            print(f"\nGeneration {generation} statistics:")
            print(f"  Best fitness: {best_fitness:.2f}")
            print(f"  Mean fitness: {np.mean(fitness_scores):.2f}")
            print(f"  Std fitness: {np.std(fitness_scores):.2f}")
    
    return best_model, best_fitness

def tournament_selection(fitness_scores, k=3, rng=None):
    """Tournament selection"""
    if rng is None:
        rng = np.random.default_rng()
    
    candidates = rng.choice(len(fitness_scores), k, replace=False)
    winner = candidates[np.argmax(fitness_scores[candidates])]
    return winner

def crossover_and_mutate(parent1, parent2, rng, mutation_rate=0.05):
    """Crossover and mutation for adaptive EM43"""
    
    # Crossover rules
    child_rule = np.zeros(256, dtype=np.uint8)
    crossover_point = rng.integers(1, 256)
    child_rule[:crossover_point] = parent1.rule[:crossover_point]
    child_rule[crossover_point:] = parent2.rule[crossover_point:]
    
    # Crossover program
    prog_crossover = rng.integers(1, len(parent1.prog))
    child_prog = np.concatenate([
        parent1.prog[:prog_crossover],
        parent2.prog[prog_crossover:]
    ])
    
    # Mutation
    for i in range(len(child_rule)):
        if rng.random() < mutation_rate:
            child_rule[i] = rng.integers(0, 4)
    
    for i in range(len(child_prog)):
        if rng.random() < mutation_rate:
            child_prog[i] = rng.integers(0, 4)
    
    # Inherit parameters (with occasional mutation)
    encoding_params = parent1.encoding_params.copy()
    decoding_params = parent1.decoding_params.copy()
    halting_params = parent1.halting_params.copy()
    
    if rng.random() < 0.1:  # 10% chance to mutate parameters
        encoding_params['separator_type'] = rng.integers(0, 4)
        decoding_params['method'] = rng.integers(0, 5)
        halting_params['condition_type'] = rng.integers(0, 5)
        halting_params['threshold'] = rng.integers(40, 90)
    
    return AdaptiveEM43(child_rule, child_prog, encoding_params, decoding_params, halting_params)

if __name__ == "__main__":
    print("🎯 COMMUTATIVE NONLOCAL GCD TRAINER")
    print("Building on successful addition model architecture")
    print("=" * 60)
    
    # Run genetic algorithm
    best_model, best_fitness = genetic_algorithm_gcd(
        population_size=500,  # Smaller for faster iteration
        generations=200,
        elite_fraction=0.15
    )
    
    print(f"\n🏆 TRAINING COMPLETE!")
    print(f"Best fitness achieved: {best_fitness:.2f}")
    
    # Final comprehensive evaluation
    print(f"\n🧪 FINAL EVALUATION:")
    all_test_cases = create_gcd_test_cases()
    accuracy, convergence, results = evaluate_gcd_model(best_model, all_test_cases, verbose=True)
    
    print(f"\nFinal Results:")
    print(f"  Accuracy: {accuracy:.3f} ({accuracy*100:.1f}%)")
    print(f"  Convergence: {convergence:.3f} ({convergence*100:.1f}%)")
    
    # Save the best model
    model_data = {
        'rule': best_model.rule,
        'prog': best_model.prog,
        'encoding_params': best_model.encoding_params,
        'decoding_params': best_model.decoding_params,
        'halting_params': best_model.halting_params,
        'fitness': best_fitness,
        'accuracy': accuracy,
        'convergence': convergence
    }
    
    with open('best_gcd_model.pkl', 'wb') as f:
        pickle.dump(model_data, f)
    
    print(f"\n💾 Model saved as 'best_gcd_model.pkl'")
    print(f"🎉 Ready to test GCD computation with nonlocal rules!")
