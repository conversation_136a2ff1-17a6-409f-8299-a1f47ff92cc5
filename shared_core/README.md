# Shared Core Components

This folder contains the core EM43 implementation and utilities shared across all models:

- `em43_numba.py` - Core EM43 cellular automaton implementation with Numba optimization
- `em43_infer.py` - Inference utilities for trained models
- `eval_best_model.py` - Model evaluation utilities
- `see_genome.py` - Genome inspection tools
- `extract_genome.py` - Genome extraction utilities
- `requirements.txt` - Python dependencies
- `README.md` - Main project documentation
